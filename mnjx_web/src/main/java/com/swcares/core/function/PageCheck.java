package com.swcares.core.function;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 分页校验注解
 *
 * <AUTHOR>
 * @date 2021/12/30 10:29
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PageCheck {
    /**
     * 1 默认从url /{current}/{limit}取值 其他模式从 request param取值
     **/
    int model() default 1;
    /**
     * model=1时 pageSize 在url的倒数位置
     **/
    int sizeOrder() default 1;
    /**
     * model!=1时 request param的 pageSize参数名称
     **/
    String sizeName() default "pageSize";
}

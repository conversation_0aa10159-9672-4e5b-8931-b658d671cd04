package com.swcares.core.security.bypass;

import cn.hutool.http.HttpStatus;
import com.swcares.core.unified.UnifiedResult;
import org.springframework.security.web.session.InvalidSessionStrategy;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
/**
 * 会话超时
 *
 * <AUTHOR>
 */
@Component
public class SecurityInvalidSessionStrategy implements InvalidSessionStrategy {
    @Override
    public void onInvalidSessionDetected(HttpServletRequest request, HttpServletResponse response) throws IOException {
        UnifiedResult.writeJson(response,UnifiedResult.fail(HttpStatus.HTTP_UNAUTHORIZED, "当前会话超时（超时就是未登录的一种）"));
    }
}

package com.swcares.core.security;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.CompressionCodecs;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Date;

/**
 * token的生成是使用JWT工具类来生成的
 *
 * <AUTHOR>
 * @date 2021-08-18 16:07:27
 */
@Slf4j
@Component
public class TokenJwtManager {
    public final static String KEY_AUTHORIZATION = "Authorization";
    /**
     * 过期时间目前设置成2天，这个配置随业务需求而定
     */
    private final Duration expiration = Duration.ofHours(8);
    @Value("${security.jwt.token}")
    private String tokenSecret;

    /**
     * 生成JWT
     *
     * @param username 用户名
     * @return JWT
     */
    public String createToken(String username) {
        // 过期时间
        Date expiryDate = new Date(System.currentTimeMillis() + expiration.toMillis());
        return Jwts.builder()
                // 将用户名放进JWT
                .setSubject(username)
                // 生效时间
                .setNotBefore(new Date())
                // 设置JWT签发时间
                .setIssuedAt(new Date())
                // 设置过期时间
                .setExpiration(expiryDate)
                // 设置加密算法和秘钥
                .signWith(Keys.hmacShaKeyFor(DigestUtil.sha256(tokenSecret, CharsetUtil.UTF_8)), SignatureAlgorithm.HS256)
                // 数据压缩方式
                .compressWith(CompressionCodecs.GZIP)
                //
                .compact();
    }

    /**
     * 解析JWT
     *
     * @param token JWT字符串
     * @return 解析成功返回Claims对象，解析失败返回null
     */
    public String parseToken(String token) {
        String username = StrUtil.EMPTY;
        // 如果是空字符串直接返回“”
        try {
            if (StrUtil.isNotEmpty(token)) {
                // 解析失败了会抛出异常，所以我们要捕捉一下。token过期、token非法都会导致解析失败
                Claims claims = Jwts.parserBuilder()
                        .setSigningKey(Keys.hmacShaKeyFor(DigestUtil.sha256(tokenSecret, CharsetUtil.UTF_8)))
                        .build()
                        .parseClaimsJws(token)
                        // 获得有效载荷
                        .getBody();
                if (ObjectUtil.isNotNull(claims)) {
                    username = claims.getSubject();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("token解析异常:", e.getMessage());
        }
        return username;
    }
}

package com.swcares.core.security.bypass;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.controller.SecurityController;
import com.swcares.core.unified.UnifiedResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
/**
 * 这个过滤器的主要作用时过滤每个需要认证的接口，判断这个接口是否已经认证了
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SecurityCaptchaOncePerRequestFilter extends OncePerRequestFilter {
    @Resource
    private CacheManager cacheManager;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        if (request.getMethod().equalsIgnoreCase(HttpMethod.POST.name()) && SecurityController.LOGIN_FULL_PATH.equalsIgnoreCase(request.getServletPath())) {
            // 登录请求并且是post的类型请求
            String timestamp = request.getParameter("timestamp");
            // 获取缓存中的验证码
            Cache cache = cacheManager.getCache(SecurityController.CACHE_WORM_REAR);
            Cache.ValueWrapper valueWrapper = cache.get(timestamp);
            if (ObjectUtil.isNotNull(valueWrapper)) {
                String captchaCode = valueWrapper.get().toString();
                // 缓存中的验证码取出后就马上清理掉
                cache.evict(timestamp);
                // 从请求中获取验证码
                String captcha = request.getParameter("captcha");
                if (StrUtil.isEmpty(captchaCode) || StrUtil.isEmpty(captcha) || !StrUtil.equalsIgnoreCase(captcha, captchaCode)) {
                    UnifiedResult.writeJson(response, UnifiedResult.fail("请输入正确的验证码"));
                } else {
                    filterChain.doFilter(request, response);
                }
            } else {
                UnifiedResult.writeJson(response, UnifiedResult.fail("请先获取验证码"));
            }
        } else {
            filterChain.doFilter(request, response);
        }

    }
}


//package com.swcares.core.config;
//
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.CorsRegistry;
//import org.springframework.web.servlet.config.annotation.EnableWebMvc;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
///**
// * <AUTHOR>
// * @date 2023/9/22 14:28
// */
//@Configuration
//@EnableWebMvc
//public class WebConfig implements WebMvcConfigurer {
//
//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        registry.addMapping("/**")
//                .allowedOrigins("http://192.168.18.10:30080")
//                .allowedMethods("PUT", "DELETE", "GET", "POST")
////                .allowedHeaders("header1", "header2", "header3")
////                .exposedHeaders("X-Authenticate")
//                .allowCredentials(true).maxAge(3600);
//    }
//}

package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxAgentAirline;
import com.swcares.obj.vo.AgentAirlineRetrieveVo;
import com.swcares.obj.vo.AgentAirlineVo;
import com.swcares.service.IAgentAirlineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/23-13:15
 */
@Api(tags = "航空公司授权管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@Slf4j
@RestController
@RequestMapping("/agentAirline")

public class AgentAirlineController {

    @Resource
    private IAgentAirlineService iAgentAirlineService;

    @ApiOperation("新增航空公司授权信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mnjxAgentAirline",
                    value = "代理人销售航空公司对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = MnjxAgentAirline.class)
    })
    @PostMapping("/create")
    public String create(@RequestBody @Validated MnjxAgentAirline mnjxAgentAirline) throws UnifiedResultException {
        boolean isOk = iAgentAirlineService.create(mnjxAgentAirline);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("分页查询航空公司授权信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "current",
                    value = "当前页码",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class),
            @ApiImplicitParam(
                    name = "limit",
                    value = "每页记录数",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class),
            @ApiImplicitParam(
                    name = "agentAirlineQueryVo",
                    value = "查询对象",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = AgentAirlineRetrieveVo.class)
    })
    @PostMapping("/retrievePageByCond/{current}/{limit}")
    public IPage<AgentAirlineVo> retrievePageByCond(@PathVariable long current,
                                                    @PathVariable long limit,
                                                    @RequestBody AgentAirlineRetrieveVo agentAirlineRetrieveVo) {
        return iAgentAirlineService.retrievePageByCond(new Page<>(current, limit), agentAirlineRetrieveVo);
    }

    @ApiOperation("根据id获取航空公司授权信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "id",
                    value = "航空公司授权信息id",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class
            )
    })
    @GetMapping("/retrieveById/{id}")
    public MnjxAgentAirline retrieveById(@PathVariable String id) throws UnifiedResultException {
        return iAgentAirlineService.retrieveById(id);
    }

    @ApiOperation("修改航空公司授权信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxAgentAirline",
                    value = "航空公司授权信息对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = MnjxAgentAirline.class
            )
    })
    @PutMapping("/updateById")
    public String updateById(@RequestBody MnjxAgentAirline mnjxAgentAirline) throws UnifiedResultException {
        boolean isOk = iAgentAirlineService.updateById(mnjxAgentAirline);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "ids",
                    value = "航空公司授权信息ID列表",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    allowMultiple = true,
                    dataTypeClass = List.class
            )
    })
    @DeleteMapping("/deleteByIds")
    public String deleteByIds(@RequestBody List<String> ids) throws UnifiedResultException {
        boolean result = iAgentAirlineService.deleteByIds(ids);
        if (result) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }
}

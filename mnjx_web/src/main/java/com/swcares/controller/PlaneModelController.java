package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.function.ParmNullCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxPlaneModel;
import com.swcares.obj.vo.PlaneModelRetrieveVo;
import com.swcares.obj.vo.PlaneModelVo;
import com.swcares.service.IPlaneModelService;
import com.swcares.core.utils.PlaneModelEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> by 王磊
 * 2021/8/25-13:15
 */
@Api(tags = "飞机机型管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@RestController
@RequestMapping("/planeModel")
@Slf4j
public class PlaneModelController {

    @Resource
    private IPlaneModelService iPlaneModelService;

    @ApiOperation("新增飞机机型信息")
    @PostMapping("/create")
    public String create(@RequestBody @Valid MnjxPlaneModel mnjxPlaneModel) throws UnifiedResultException, InstantiationException, IllegalAccessException {
        boolean isOk = iPlaneModelService.create(mnjxPlaneModel);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("根据id获取飞机机型信息")
    @GetMapping("/retrieveById/{planeModelId}")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "飞机机型信息id", name = "planeModelId", required = true, dataTypeClass = String.class, paramType = Constant.PARAM_TYPE_PATH)
    })
    @ParmNullCheck
    public PlaneModelVo retrieveById(@PathVariable String planeModelId) {
        return iPlaneModelService.retrieveById(planeModelId);
    }

    @ApiOperation("分页查询飞机机型信息")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "当前页码", name = "current", required = true, dataTypeClass = long.class, paramType = Constant.PARAM_TYPE_PATH),
            @ApiImplicitParam(value = "每页记录数", name = "limit", required = true, dataTypeClass = long.class, paramType = Constant.PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "vo", value = "查询对象", dataTypeClass = PlaneModelRetrieveVo.class, paramType = Constant.PARAM_TYPE_BODY)
    })
    @PostMapping("/retrieveByCond/{current}/{limit}")
    @PageCheck
    @ParmNullCheck
    public IPage<PlaneModelVo> retrieveByCond(@PathVariable("current") long current, @PathVariable("limit") long limit, @RequestBody PlaneModelRetrieveVo vo) {
        return iPlaneModelService.retrieveByCond(new Page<>(current, limit), vo);
    }

    @ApiOperation("飞机机型版本")
    @PostMapping("/retrieveVersionAndType")
    public List<PlaneModelRetrieveVo> retrieveVersionAndType() {
        return iPlaneModelService.retrieveVersionAndType();
    }

    @ApiOperation("飞机类型")
    @PostMapping("/retrieveKind")
    public Map<PlaneModelEnum, String> retrieveKind() {
        return iPlaneModelService.retrieveKind();
    }


    @ApiOperation("修改飞机机型信息")
    @PutMapping("/updateById")
    public String updateById(@RequestBody @Valid MnjxPlaneModel mnjxPlaneModel) throws UnifiedResultException {
        boolean isOk = iPlaneModelService.updateById(mnjxPlaneModel);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation(value = "根据多个id批量删除", notes = "根据id批量删除")
    @DeleteMapping("/deleteByIds")
    public String deleteByIds(@RequestBody List<String> ids) throws UnifiedResultException {
        boolean isOk = iPlaneModelService.deleteByIds(ids);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }
}

package com.swcares.controller;

import cn.hutool.core.util.ReUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ReUtils;
import com.swcares.obj.dto.RtDto;
import com.swcares.service.IPnrCommandService;
import com.swcares.service.IPnrOperationService;
import com.swcares.service.IPnrRecordService;
import com.swcares.obj.vo.PnrRecordQueryVo;
import com.swcares.obj.vo.PnrRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * description：订座记录管理
 *
 * @author：zhaokan
 * @date：2021/10/14
 */
@Api(tags = "订座记录管理")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/pnrRecord")
public class PnrRecordController {

	@Resource
	private IPnrRecordService iPnrRecordService;

	@Resource
	private IPnrCommandService iPnrCommandService;

	@Resource
	private IPnrOperationService iPnrOperationService;

	@ApiOperation("分页列表查询")
	@PostMapping("/retrieveListByPage/{current}/{limit}")
	@ApiImplicitParams({
			@ApiImplicitParam(
					value = "当前页码",
					name = "current",
					required = true,
					dataTypeClass = long.class,
					paramType = Constant.PARAM_TYPE_PATH
			),
			@ApiImplicitParam(
					value = "每页记录数",
					name = "limit",
					required = true,
					dataTypeClass = long.class,
					paramType = Constant.PARAM_TYPE_PATH
			),
			@ApiImplicitParam(
					value = "订座记录查询对象",
					name = "pnrRecordQueryVo",
					dataTypeClass = PnrRecordQueryVo.class,
					paramType = Constant.PARAM_TYPE_BODY
			)
	})
	public IPage<PnrRecordVo> retrieveListByPage(@PathVariable long current,
												 @PathVariable long limit,
												 @RequestBody PnrRecordQueryVo pnrRecordQueryVo) {
		return iPnrRecordService.retrieveListByPage(current, limit, pnrRecordQueryVo);
	}

	@ApiOperation("查看完整PNR")
	@GetMapping("/retrievePnrDetails/{crsPnr}")
	public String retrievePnrDetails(@PathVariable String crsPnr) throws UnifiedResultException {
		RtDto rtDto = new RtDto();
		rtDto.setPnrCode(crsPnr);
		rtDto.setExtractParameter("C");
		MemoryDataPnr newMemoryDataPnr = new MemoryDataPnr();
		iPnrCommandService.rt(newMemoryDataPnr,rtDto);
		newMemoryDataPnr.setByRt(true);
		String pnrRecord = iPnrOperationService.recallByRtC(newMemoryDataPnr,null).replace("\r\n","\n");
		String idCard = ReUtils.getGroup1("(\\b(NI|PP|ID)\\w*)", pnrRecord);
		String originalSsrInfoIdCard = idCard;
		// 身份证号脱敏
		if (ReUtil.isMatch(Constant.CERTIFICATE_PATTERN, idCard.substring(2))) {
			// 保留前6位和最后4位，中间用星号替换
			StringBuilder sb = new StringBuilder(idCard.length());
			idCard = idCard.substring(2);
			sb.append("NI");
			sb.append(idCard, 0, 3); // 添加前3位
			sb.append("******"); // 添加星号，长度根据需要调整
			sb.append(idCard.substring(idCard.length() - 4)); // 添加最后4位
			idCard = sb.toString();
			pnrRecord = pnrRecord.replace(originalSsrInfoIdCard, idCard);
		}
		return pnrRecord;
	}
}

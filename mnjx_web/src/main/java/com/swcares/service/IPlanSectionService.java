package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.obj.vo.PlanSectionPageVo;
import com.swcares.obj.vo.PlanSectionRetrieveVo;

/**
 * @author：wangchun
 */
public interface IPlanSectionService {

    /**
     * 航节飞行计划分页列表查询
     *
     * @param planSectionRetrieveVo
     * @return 航节飞行计划分页列表
     */
    IPage<PlanSectionPageVo> retrievePage(PlanSectionRetrieveVo planSectionRetrieveVo);
}

package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.obj.vo.ActionRecordQueryVo;
import com.swcares.obj.vo.ActionRecordResultListVo;
import com.swcares.obj.vo.ActionRecordResultVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> by sxl
 * 2023/2/2-15:33
 */
public interface IActionRecordService {

    /**
     * 分页+条件查询
     *
     * @param page                page
     * @param actionRecordQueryVo ActionRecordQueryVo
     * @return 分页+条件查询
     */
    IPage<ActionRecordResultVo> retrievePage(Page<ActionRecordResultVo> page, ActionRecordQueryVo actionRecordQueryVo);

    /**
     * office工作号详情
     *
     * @param actionRecordResultVo ActionRecordResultVo
     * @return list
     */
    List<ActionRecordResultListVo> retrieveDetail(ActionRecordResultVo actionRecordResultVo);

    /**
     * 下载
     *
     * @param actionRecordQueryVo actionRecordQueryVo
     * @param request             HttpServletRequest
     * @param response            HttpServletResponse
     */
    void download(ActionRecordQueryVo actionRecordQueryVo, HttpServletRequest request, HttpServletResponse response);
}

package com.swcares.service;

import com.swcares.entity.MnjxSeatModel;

/**
 * <AUTHOR>
 */
public interface ISeatModelService {
    /**
     * 创建座位模型数据
     *
     * @param mnjxSeatModel 座位模型
     * @return 是否成功
     */
    boolean create(MnjxSeatModel mnjxSeatModel);

    /**
     * 根据座位模型id获取模型数据
     *
     * @param id 模型id
     * @return 座位模型数据
     */
    MnjxSeatModel retrieveById(String id);

    /**
     * 更新模型数据
     *
     * @param mnjxSeatModel 模型数据
     * @return 更新是否成功
     */
    boolean update(MnjxSeatModel mnjxSeatModel);

    /**
     * 根据id删除模型数据
     *
     * @param id 模型id
     * @return 删除是否成功
     */
    boolean deleteById(String id);
}

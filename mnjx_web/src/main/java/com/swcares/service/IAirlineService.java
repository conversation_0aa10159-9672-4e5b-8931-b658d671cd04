package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxAirline;
import com.swcares.obj.vo.AirlineRetrieveBaseVo;
import com.swcares.obj.vo.AirlineRetrieveVo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> by yaodan
 * 2021/8/19-11:26
 */
public interface IAirlineService {

    /**
     * saveNotDistinct
     *
     * @param mnjxAirline mnjxAirline
     * @return saveNotDistinct
     * @throws UnifiedResultException UnifiedResultException
     */
    boolean create(MnjxAirline mnjxAirline) throws UnifiedResultException;

    /**
     * 导入excel中的数据
     *
     * @param mnjxAirlines 航空公司数据
     */
    void createByExcel(List<MnjxAirline> mnjxAirlines);

    /**
     * 条件加分页查询
     *
     * @param page                  page
     * @param airlineRetrieveBaseVo airlineQueryVo
     * @return 条件加分页查询
     */
    IPage<AirlineRetrieveVo> retrievePageByCond(Page<AirlineRetrieveVo> page, AirlineRetrieveBaseVo airlineRetrieveBaseVo);

    /**
     * 查询所有航空公司的二字码
     *
     * @return 二字码列表
     */
    Set<String> retrieveAirlineCode();

    /**
     * 查询所有航空公司的三字码
     * @return
     */
    Set<String> retrieveAirlineThreeCode();
    /**
     * Title: retrieveList
     * Description: 查询航空公司列表<br>
     *
     * @return {@link List<MnjxAirline>}
     * <AUTHOR>
     * @date 2022/2/10 13:59
     */
    List<MnjxAirline> retrieveList();

    /**
     * 通过ID查询航空公司信息
     *
     * @param id id
     * @return 航空公司对象
     */
    AirlineRetrieveVo retrieveById(String id);

    /**
     * updateNotDistinct
     *
     * @param mnjxAirline mnjxAirline
     * @return updateNotDistinct
     * @throws UnifiedResultException UnifiedResultException
     */
    String updateById(MnjxAirline mnjxAirline) throws UnifiedResultException;

    /**
     * Title: updateStatusByIds
     * Description: 修改航空公司状态<br>
     *
     * @param ids ids
     * <AUTHOR>
     * @date 2022/2/10 15:25
     * @throws UnifiedResultException 统一异常
     */
    void updateStatusByIds(List<String> ids) throws UnifiedResultException;

    /**
     * Title: deleteAirlines
     * Description: 批量删除航空公司<br>
     *
     * @param ids ids
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/2/10 14:05
     * @throws UnifiedResultException 统一异常
     */
    boolean deleteByIds(List<String> ids) throws UnifiedResultException;


}

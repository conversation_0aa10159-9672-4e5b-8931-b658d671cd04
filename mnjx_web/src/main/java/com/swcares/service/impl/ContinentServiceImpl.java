package com.swcares.service.impl;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ObjectUtils;
import com.swcares.entity.MnjxContinent;
import com.swcares.obj.vo.ContinentQueryVo;
import com.swcares.service.IContinentService;
import com.swcares.service.IMnjxContinentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/19-11:28
 */
@Slf4j
@Service
public class ContinentServiceImpl implements IContinentService {

    @Resource
    private IMnjxContinentService iMnjxContinentService;

    @Override
    public boolean create(MnjxContinent mnjxContinent) throws UnifiedResultException {
        String continentCode = mnjxContinent.getContinentCode();
        MnjxContinent continent = iMnjxContinentService.lambdaQuery()
                .eq(MnjxContinent::getContinentCode, continentCode)
                .one();
        if (ObjectUtils.isNotEmpty(continent)) {
            throw new UnifiedResultException("该州编码已存在");
        }
        return iMnjxContinentService.save(mnjxContinent);
    }

    /**
     * 条件查询
     *
     * @param @return 州列表
     */
    @Override
    public List<MnjxContinent> retrieveContinentByCond(ContinentQueryVo continentQueryVo) {
        String continentCode = continentQueryVo.getContinentCode();
        String continentName = continentQueryVo.getContinentName();
        return iMnjxContinentService.lambdaQuery()
                .and(StrUtil.isNotEmpty(continentName) || StrUtil.isNotEmpty(continentCode), s -> s
                        .like(StrUtil.isNotEmpty(continentName), MnjxContinent::getContinentEname, continentName)
                        .like(StrUtil.isNotEmpty(continentCode), MnjxContinent::getContinentCode, continentCode))
                .or(StrUtil.isNotEmpty(continentName) || StrUtil.isNotEmpty(continentCode), s -> s
                        .like(StrUtil.isNotEmpty(continentName), MnjxContinent::getContinentCname, continentName)
                        .like(StrUtil.isNotEmpty(continentCode), MnjxContinent::getContinentCode, continentCode))

                .list();
    }

    @Override
    public List<MnjxContinent> retrieveContinentList() {
        return iMnjxContinentService.list();
    }


}

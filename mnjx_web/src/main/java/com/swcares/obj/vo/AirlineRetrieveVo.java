package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 【查询参数】
 *
 * <AUTHOR> by yaodan
 * 2021/8/19-11:22
 */
@Api(tags = "航空公司查询参数")
@Data
@EqualsAndHashCode(callSuper = false)
public class AirlineRetrieveVo {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("国家代码")
    private String countryIso;

    @ApiModelProperty("航空公司二字码")
    private String airlineCode;

    @ApiModelProperty("航空公司三字码")
    private String airlineThreeCode;

    @ApiModelProperty("航空公司状态，1,启用(默认) 0,停用")
    private String airlineStatus = "1";

    @ApiModelProperty(value = "国家表主键ID")
    private String countryId;
    @ApiModelProperty(value = "航司ID")
    private String airlineId;

    @ApiModelProperty(value = "航空公司全称", example = "中国国际航空公司")
    @NotBlank(message = "航空公司全称不能为空")
    private String airlineFullName;

    @ApiModelProperty(value = "航空公司简称 国航")
    private String airlineShortName;

    @ApiModelProperty(value = "航空公司英文 Air China Limited")
    private String airlineEname;

    @ApiModelProperty(value = "结算码 786")
    private String airlineSettlementCode;

    @ApiModelProperty(value = "联系人")
    private String airlineContactName;

    @ApiModelProperty(value = "联系方式")
    private String airlineContactPhone;

    @ApiModelProperty(value = "地址")
    private String airlineContactAddress;


    @ApiModelProperty(value = "航空公司折扣（用于计算票价）")
    private BigDecimal airlineCodeDiscount;
}

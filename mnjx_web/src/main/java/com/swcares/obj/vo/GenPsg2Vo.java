package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 【批量生成旅客订座记录信息】
 * <AUTHOR>
 * @date 2021/12/14
 */
@Data
@Api(tags = "批量生成旅客订座记录信息")
@EqualsAndHashCode(callSuper=false)
public class GenPsg2Vo extends GenPsgVo {

    @ApiModelProperty(value = "销售舱位")
    private String sellCabin;

    @ApiModelProperty(value = "输入值")
    private String inputValue;

    @ApiModelProperty(value = "是否联程")
    private boolean isTrip = false;


}

package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/7
 */
@Api(tags = "机场信息返回对象")
@Data
public class AirportVo {

    @ApiModelProperty(value = "机场id")
    private String airportId;

    @ApiModelProperty(value = "机场三字码")
    private String airportCode;

    @ApiModelProperty(value = "机场中文名")
    private String airportCname;

    @ApiModelProperty(value = "机场英文名")
    private String airportEname;

    @ApiModelProperty(value = "状态 1,启用 0,停用")
    private Integer airportStatus;

    @ApiModelProperty(value = "城市中文名")
    private String cityCname;
    @ApiModelProperty(value = "城市id")
    private String cityId;
    @ApiModelProperty(value = "城市三字码")
    private String cityCode;

    @ApiModelProperty(value = "机场纬度", example = "400448N")
    private String latitude;

    @ApiModelProperty(value = "机场经度", example = "1163504E")
    private String longitude;

}

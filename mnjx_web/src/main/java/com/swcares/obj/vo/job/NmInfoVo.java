package com.swcares.obj.vo.job;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 **/
@Data
public class NmInfoVo {
    /**
     * 查询旅客姓名相关信息
     */
    List<String> nmFcIdList = new ArrayList<>();
    List<String> nmEiIdLIst = new ArrayList<>();
    List<String> nmCtIdList = new ArrayList<>();
    List<String> nmUmIdList = new ArrayList<>();
    List<String> nmFnIdList = new ArrayList<>();
    List<String> nmXnIdList = new ArrayList<>();
    List<String> nmOiIdList = new ArrayList<>();
    List<String> nmOsiIdList = new ArrayList<>();
    List<String> nmRmkIdList = new ArrayList<>();
    List<String> nmSsrIdList = new ArrayList<>();
    List<String> nmFpIdList = new ArrayList<>();
    List<String> luggageCarryonIdList = new ArrayList<>();
    List<String> unpackInfoIdList = new ArrayList<>();
    List<String> luggageIdList = new ArrayList<>();
    List<String> nmTicketIdList = new ArrayList<>();
    List<String> ticketOperateRecordIdList = new ArrayList<>();
    List<String> refundTicketIdList = new ArrayList<>();
    List<String> tnIdList = new ArrayList<>();
    List<String> exLuggageIdList = new ArrayList<>();
    List<String> psgCkiOptionIdList = new ArrayList<>();
    List<String> psgSeatIdList = new ArrayList<>();
    List<String> operateRecordIdList = new ArrayList<>();
    List<String> psgCkiIdList = new ArrayList<>();
    List<String> verifyInfoIdList = new ArrayList<>();
    List<String> nmIdList = new ArrayList<>();
}

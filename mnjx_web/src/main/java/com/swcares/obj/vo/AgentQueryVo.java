package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 【查询参数】
 *
 * <AUTHOR> by yaodan
 * 2021/8/19-11:22
 */
@Api(tags = "代理人查询参数")
@Data
public class AgentQueryVo {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("代理人中文名")
    private String agentCname;

    @ApiModelProperty("代理人IATA号")
    private String agentIata;

    @ApiModelProperty("状态，0,启用(默认) 1,停用")
    private String agentStatus;
}

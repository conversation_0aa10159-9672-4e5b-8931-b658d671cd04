package com.swcares.obj.vo;

import com.swcares.core.validator.PropConstraint;
import com.swcares.core.validator.ValueTypeConstraint;
import com.swcares.service.ILevelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @Desc
 * @date 2022/3/17 - 15:09
 * @Version
 */
@Data
@Api(tags = "工作号修改参数实体")
@AllArgsConstructor
@NoArgsConstructor
public class SiUpdateVo {

    @ApiModelProperty(value = "siId")
    @NotBlank(message = "siId不能为空")
    private String siId;

    @ApiModelProperty(value = "机构类型id", example = "0")
    @NotBlank(message = "机构类型不能为空")
    private String officeType;

    @ApiModelProperty(value = "机构id", example = "10")
    @NotBlank(message = "机构不能为空")
    private String orgId;

    @ApiModelProperty(value = "office号")
    @NotBlank(message = "office号不能为空")
    private String officeNo;

    @ApiModelProperty(value = "工作号")
    @NotBlank(message = "工作号不能为空")
    private String siNo;

    @ApiModelProperty(value = "工作号密码")
//    @NotBlank(message = "工作号密码不能为空")
    private String siPassWord;

    @ApiModelProperty(value = "工作号PID")
    @NotBlank(message = "工作号PID不能为空")
    @Pattern(regexp = "[0-9]{1,6}",message = "仅可输入正整数，且长度为6位数")
    private String siPid;

    @ApiModelProperty(value = "工作号状态，1-启用，0停用")
    @NotBlank(message = "工作号状态不能为空")
    @ValueTypeConstraint
    private String siStatus;

    @ApiModelProperty(value = "工作号级别")
    @NotBlank(message = "工作号级别不能为空")
    @PropConstraint(service = ILevelService.class,
            method = "retrieveLevelCode",
            message = "工作号级别不存在")
    private String levelCode;

    @ApiModelProperty(value = "工作号描述")
    private String remark;

}

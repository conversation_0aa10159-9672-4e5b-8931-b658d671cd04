package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ApiModel("销售舱位查询返回结果对象")
@Data
public class SellCabinVo {
    @ApiModelProperty(value = "ID")
    private String sellCabinId;

    @ApiModelProperty(value = "舱等ID")
    private String sellClassId;

    @ApiModelProperty(value = "销售舱位")
    private String sellCabin;

    @ApiModelProperty(value = "销售舱位折扣率")
    private BigDecimal sellCabinDiscount;

    @ApiModelProperty(value = "票价等级")
    private String ticketLevel;

    @ApiModelProperty(value = "币种代码:CNY")
    private String currencyCode;
}

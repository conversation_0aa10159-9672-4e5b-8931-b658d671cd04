package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Title:       [项目名称]_[模块名]
 * Description: [电子登机牌信息]
 * Created on 2021-9-30
 *
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel("电子登机牌信息")
@Data
public class BoardingPassVo {

    @ApiModelProperty("英文名、拼音")
    private String yName;

    @ApiModelProperty("中文名")
    private String cName;

    @ApiModelProperty("身份证号")
    private String idCard;

    @ApiModelProperty("航班号")
    private String fltNo;

    @ApiModelProperty("日期")
    private String fltDate;

    @ApiModelProperty("出发地中文")
    private String orgName;

    @ApiModelProperty("到达地中文")
    private String dstName;

    @ApiModelProperty("登机时间")
    private String boardingTime;

    @ApiModelProperty("登机口")
    private String gate;

    @ApiModelProperty("座位号")
    private String seatNo;

    @ApiModelProperty("常客卡号（如果有）")
    private String psgCard;

    @ApiModelProperty("登机号")
    private String aboardNo;

    @ApiModelProperty("主舱位")
    private String mainClass;

    @ApiModelProperty("销售舱位")
    private String cabinClass;

    @ApiModelProperty("出发地三字码")
    private String org;

    @ApiModelProperty("到达地三字码")
    private String dst;

    @ApiModelProperty("电子客票号")
    private String etkt;
}

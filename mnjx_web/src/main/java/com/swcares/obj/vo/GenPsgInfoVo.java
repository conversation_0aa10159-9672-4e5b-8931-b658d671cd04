package com.swcares.obj.vo;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 【旅客订座记录信息】
 *
 * <AUTHOR> by csj
 * 2021/10/14-14:45
 */
@Api(tags = "旅客订座记录信息-旅客数据")
@Data
public class GenPsgInfoVo implements Serializable {
	@ApiModelProperty(value = "旅客类型")
	private String psgType;

	@ApiModelProperty(value = "旅客姓名")
	private String psgName;

	@ApiModelProperty(value = "特殊旅客")
	private String[] spacialServer;

	@ApiModelProperty(value = "证件号")
	private String foidValue;

	@ApiModelProperty(value = "证件号对应姓名")
	private String foidToWho;

	@ApiModelProperty(value = "特殊餐食")
	private String mlSpelType;

	@ApiModelProperty(value = "特殊轮椅")
	private String wcSpelType;

	@ApiModelProperty(value = "婴儿，出生日期")
	private String infBirth;

	@ApiModelProperty(value = "航司二字码")
	private String airlineCode;

	@ApiModelProperty(value = "联系方式")
	private String contact;

}

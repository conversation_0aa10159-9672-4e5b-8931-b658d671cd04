package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * description：订座记录查询
 * @author：zhao<PERSON>
 * @date：2021/10/14
*/
@Api(tags = "订座记录查询对象")
@Data
public class PnrRecordQueryVo {

    @ApiModelProperty(value = "crs pnr 编码")
    private String pnrCrs;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

}

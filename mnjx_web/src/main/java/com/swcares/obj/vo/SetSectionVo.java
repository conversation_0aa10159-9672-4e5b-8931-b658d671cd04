package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/10/12 1:24 下午
 * 概要
 */
@ApiModel(value = "设置航节参数")
@Data
public class SetSectionVo {

	@ApiModelProperty(value = "航班ID")
	private String flightId;

	@ApiModelProperty(value = "航班号")
	private String flightNo;

	@ApiModelProperty(value = "航节号")
	@NotNull(message = "航节号不能为空")
	private Integer sectionNo;

	@ApiModelProperty(value = "机场代码")
	@NotBlank(message = "机场代码不能为空")
	private String airportCode;

	@ApiModelProperty(value = "机场城市")
	private String airportCity;

	@ApiModelProperty(value = "距离")
	private String distance;

	@ApiModelProperty(value = "飞行时间")
	private String flyTime;

	@ApiModelProperty(value = "计划进港时间")
	private String estimateArr;

	@ApiModelProperty(value = "计划离港时间")
	private String estimateOff;
//	estimateOff estimateOff
}

admin_name=ä»»å¡è°åº¦ä¸­å¿
admin_name_full=åå¸å¼ä»»å¡è°åº¦å¹³å°XXL-JOB
admin_version=2.3.0
admin_i18n=

## system
system_tips=ç³»ç»æç¤º
system_ok=ç¡®å®
system_close=å³é­
system_save=ä¿å­
system_cancel=åæ¶
system_search=æç´¢
system_status=ç¶æ
system_opt=æä½
system_please_input=è¯·è¾å¥
system_please_choose=è¯·éæ©
system_success=æå
system_fail=å¤±è´¥
system_add_suc=æ°å¢æå
system_add_fail=æ°å¢å¤±è´¥
system_update_suc=æ´æ°æå
system_update_fail=æ´æ°å¤±è´¥
system_all=å¨é¨
system_api_error=æ¥å£å¼å¸¸
system_show=æ¥ç
system_empty=æ 
system_opt_suc=æä½æå
system_opt_fail=æä½å¤±è´¥
system_opt_edit=ç¼è¾
system_opt_del=å é¤
system_opt_copy=å¤å¶
system_unvalid=éæ³
system_not_found=ä¸å­å¨
system_nav=å¯¼èª
system_digits=æ´æ°
system_lengh_limit=é¿åº¦éå¶
system_permission_limit=æéæ¦æª
system_welcome=æ¬¢è¿

## daterangepicker
daterangepicker_ranges_recent_hour=æè¿ä¸å°æ¶
daterangepicker_ranges_today=ä»æ¥
daterangepicker_ranges_yesterday=æ¨æ¥
daterangepicker_ranges_this_month=æ¬æ
daterangepicker_ranges_last_month=ä¸ä¸ªæ
daterangepicker_ranges_recent_week=æè¿ä¸å¨
daterangepicker_ranges_recent_month=æè¿ä¸æ
daterangepicker_custom_name=èªå®ä¹
daterangepicker_custom_starttime=èµ·å§æ¶é´
daterangepicker_custom_endtime=ç»ææ¶é´
daterangepicker_custom_daysofweek=æ¥,ä¸,äº,ä¸,å,äº,å­
daterangepicker_custom_monthnames=ä¸æ,äºæ,ä¸æ,åæ,äºæ,å­æ,ä¸æ,å«æ,ä¹æ,åæ,åä¸æ,åäºæ

## dataTable
dataTable_sProcessing=å¤çä¸­...
dataTable_sLengthMenu=æ¯é¡µ _MENU_ æ¡è®°å½
dataTable_sZeroRecords=æ²¡æå¹éç»æ
dataTable_sInfo=ç¬¬ _PAGE_ é¡µ ( æ»å± _PAGES_ é¡µï¼_TOTAL_ æ¡è®°å½ )
dataTable_sInfoEmpty=æ è®°å½
dataTable_sInfoFiltered=(ç± _MAX_ é¡¹ç»æè¿æ»¤)
dataTable_sSearch=æç´¢
dataTable_sEmptyTable=è¡¨ä¸­æ°æ®ä¸ºç©º
dataTable_sLoadingRecords=è½½å¥ä¸­...
dataTable_sFirst=é¦é¡µ
dataTable_sPrevious=ä¸é¡µ
dataTable_sNext=ä¸é¡µ
dataTable_sLast=æ«é¡µ
dataTable_sSortAscending=: ä»¥ååºæåæ­¤å
dataTable_sSortDescending=: ä»¥éåºæåæ­¤å

## login
login_btn=ç»å½
login_remember_me=è®°ä½å¯ç 
login_username_placeholder=è¯·è¾å¥ç»å½è´¦å·
login_password_placeholder=è¯·è¾å¥ç»å½å¯ç 
login_username_empty=è¯·è¾å¥ç»å½è´¦å·
login_username_lt_4=ç»å½è´¦å·ä¸åºä½äº4ä½
login_password_empty=è¯·è¾å¥ç»å½å¯ç 
login_password_lt_4=ç»å½å¯ç ä¸åºä½äº4ä½
login_success=ç»å½æå
login_fail=ç»å½å¤±è´¥
login_param_empty=è´¦å·æå¯ç ä¸ºç©º
login_param_unvalid=è´¦å·æå¯ç éè¯¯

## logout
logout_btn=æ³¨é
logout_confirm=ç¡®è®¤æ³¨éç»å½?
logout_success=æ³¨éæå
logout_fail=æ³¨éå¤±è´¥

## change pwd
change_pwd=ä¿®æ¹å¯ç 
change_pwd_suc_to_logout=ä¿®æ¹å¯ç æåï¼å³å°æ³¨éç»é
change_pwd_field_newpwd=æ°å¯ç 

## dashboard
job_dashboard_name=è¿è¡æ¥è¡¨
job_dashboard_job_num=ä»»å¡æ°é
job_dashboard_job_num_tip=è°åº¦ä¸­å¿è¿è¡çä»»å¡æ°é
job_dashboard_trigger_num=è°åº¦æ¬¡æ°
job_dashboard_trigger_num_tip=è°åº¦ä¸­å¿è§¦åçè°åº¦æ¬¡æ°
job_dashboard_jobgroup_num=æ§è¡å¨æ°é
job_dashboard_jobgroup_num_tip=è°åº¦ä¸­å¿å¨çº¿çæ§è¡å¨æºå¨æ°é
job_dashboard_report=è°åº¦æ¥è¡¨
job_dashboard_report_loaddata_fail=è°åº¦æ¥è¡¨æ°æ®å è½½å¼å¸¸
job_dashboard_date_report=æ¥æåå¸å¾
job_dashboard_rate_report=æåæ¯ä¾å¾

## job info
jobinfo_name=ä»»å¡ç®¡ç
jobinfo_job=ä»»å¡
jobinfo_field_add=æ°å¢
jobinfo_field_update=æ´æ°ä»»å¡
jobinfo_field_id=ä»»å¡ID
jobinfo_field_jobgroup=æ§è¡å¨
jobinfo_field_jobdesc=ä»»å¡æè¿°
jobinfo_field_gluetype=è¿è¡æ¨¡å¼
jobinfo_field_executorparam=ä»»å¡åæ°
jobinfo_field_author=è´è´£äºº
jobinfo_field_timeout=ä»»å¡è¶æ¶æ¶é´
jobinfo_field_alarmemail=æ¥è­¦é®ä»¶
jobinfo_field_alarmemail_placeholder=è¯·è¾å¥æ¥è­¦é®ä»¶ï¼å¤ä¸ªé®ä»¶å°ååéå·åé
jobinfo_field_executorRouteStrategy=è·¯ç±ç­ç¥
jobinfo_field_childJobId=å­ä»»å¡ID
jobinfo_field_childJobId_placeholder=è¯·è¾å¥å­ä»»å¡çä»»å¡ID,å¦å­å¨å¤ä¸ªåéå·åé
jobinfo_field_executorBlockStrategy=é»å¡å¤çç­ç¥
jobinfo_field_executorFailRetryCount=å¤±è´¥éè¯æ¬¡æ°
jobinfo_field_executorFailRetryCount_placeholder=å¤±è´¥éè¯æ¬¡æ°ï¼å¤§äºé¶æ¶çæ
jobinfo_script_location=èæ¬ä½ç½®
jobinfo_shard_index=åçåºå·
jobinfo_shard_total=åçæ»æ°
jobinfo_opt_stop=åæ­¢
jobinfo_opt_start=å¯å¨
jobinfo_opt_log=æ¥è¯¢æ¥å¿
jobinfo_opt_run=æ§è¡ä¸æ¬¡
jobinfo_opt_run_tips=è¯·è¾å¥æ¬æ¬¡æ§è¡çæºå¨å°åï¼ä¸ºç©ºåä»æ§è¡å¨è·å
jobinfo_opt_registryinfo=æ³¨åèç¹
jobinfo_opt_next_time=ä¸æ¬¡æ§è¡æ¶é´
jobinfo_glue_remark=æºç å¤æ³¨
jobinfo_glue_remark_limit=æºç å¤æ³¨é¿åº¦éå¶ä¸º4~100
jobinfo_glue_rollback=çæ¬åæº¯
jobinfo_glue_jobid_unvalid=ä»»å¡IDéæ³
jobinfo_glue_gluetype_unvalid=è¯¥ä»»å¡éGLUEæ¨¡å¼
jobinfo_field_executorTimeout_placeholder=ä»»å¡è¶æ¶æ¶é´ï¼åä½ç§ï¼å¤§äºé¶æ¶çæ
schedule_type=è°åº¦ç±»å
schedule_type_none=æ 
schedule_type_cron=CRON
schedule_type_fix_rate=åºå®éåº¦
schedule_type_fix_delay=åºå®å»¶è¿
schedule_type_none_limit_start=å½åè°åº¦ç±»åç¦æ­¢å¯å¨
misfire_strategy=è°åº¦è¿æç­ç¥
misfire_strategy_do_nothing=å¿½ç¥
misfire_strategy_fire_once_now=ç«å³æ§è¡ä¸æ¬¡
jobinfo_conf_base=åºç¡éç½®
jobinfo_conf_schedule=è°åº¦éç½®
jobinfo_conf_job=ä»»å¡éç½®
jobinfo_conf_advanced=é«çº§éç½®

## job log
joblog_name=è°åº¦æ¥å¿
joblog_status=ç¶æ
joblog_status_all=å¨é¨
joblog_status_suc=æå
joblog_status_fail=å¤±è´¥
joblog_status_running=è¿è¡ä¸­
joblog_field_triggerTime=è°åº¦æ¶é´
joblog_field_triggerCode=è°åº¦ç»æ
joblog_field_triggerMsg=è°åº¦å¤æ³¨
joblog_field_handleTime=æ§è¡æ¶é´
joblog_field_handleCode=æ§è¡ç»æ
joblog_field_handleMsg=æ§è¡å¤æ³¨
joblog_field_executorAddress=æ§è¡å¨å°å
joblog_clean=æ¸ç
joblog_clean_log=æ¥å¿æ¸ç
joblog_clean_type=æ¸çæ¹å¼
joblog_clean_type_1=æ¸çä¸ä¸ªæä¹åæ¥å¿æ°æ®
joblog_clean_type_2=æ¸çä¸ä¸ªæä¹åæ¥å¿æ°æ®
joblog_clean_type_3=æ¸çå­ä¸ªæä¹åæ¥å¿æ°æ®
joblog_clean_type_4=æ¸çä¸å¹´ä¹åæ¥å¿æ°æ®
joblog_clean_type_5=æ¸çä¸åæ¡ä»¥åæ¥å¿æ°æ®
joblog_clean_type_6=æ¸çä¸ä¸æ¡ä»¥åæ¥å¿æ°æ®
joblog_clean_type_7=æ¸çä¸ä¸æ¡ä»¥åæ¥å¿æ°æ®
joblog_clean_type_8=æ¸çåä¸æ¡ä»¥åæ¥å¿æ°æ®
joblog_clean_type_9=æ¸çæææ¥å¿æ°æ®
joblog_clean_type_unvalid=æ¸çç±»ååæ°å¼å¸¸
joblog_handleCode_200=æå
joblog_handleCode_500=å¤±è´¥
joblog_handleCode_502=å¤±è´¥(è¶æ¶)
joblog_kill_log=ç»æ­¢ä»»å¡
joblog_kill_log_limit=è°åº¦å¤±è´¥ï¼æ æ³ç»æ­¢æ¥å¿
joblog_kill_log_byman=äººä¸ºæä½ï¼ä¸»å¨ç»æ­¢
joblog_lost_fail=ä»»å¡ç»æä¸¢å¤±ï¼æ è®°å¤±è´¥
joblog_rolling_log=æ§è¡æ¥å¿
joblog_rolling_log_refresh=å·æ°
joblog_rolling_log_triggerfail=ä»»å¡åèµ·è°åº¦å¤±è´¥ï¼æ æ³æ¥çæ§è¡æ¥å¿
joblog_rolling_log_failoften=ç»æ­¢è¯·æ±Rollingæ¥å¿,è¯·æ±å¤±è´¥æ¬¡æ°è¶ä¸é,å¯å·æ°é¡µé¢éæ°å è½½æ¥å¿
joblog_logid_unvalid=æ¥å¿IDéæ³

## job group
jobgroup_name=æ§è¡å¨ç®¡ç
jobgroup_list=æ§è¡å¨åè¡¨
jobgroup_add=æ°å¢æ§è¡å¨
jobgroup_edit=ç¼è¾æ§è¡å¨
jobgroup_del=å é¤æ§è¡å¨
jobgroup_field_title=åç§°
jobgroup_field_addressType=æ³¨åæ¹å¼
jobgroup_field_addressType_0=èªå¨æ³¨å
jobgroup_field_addressType_1=æå¨å½å¥
jobgroup_field_addressType_limit=æå¨å½å¥æ³¨åæ¹å¼ï¼æºå¨å°åä¸å¯ä¸ºç©º
jobgroup_field_registryList=æºå¨å°å
jobgroup_field_registryList_unvalid=æºå¨å°åæ ¼å¼éæ³
jobgroup_field_registryList_placeholder=è¯·è¾å¥æ§è¡å¨å°ååè¡¨ï¼å¤å°åéå·åé
jobgroup_field_appname_limit=éå¶ä»¥å°åå­æ¯å¼å¤´ï¼ç±å°åå­æ¯ãæ°å­åä¸­åçº¿ç»æ
jobgroup_field_appname_length=AppNameé¿åº¦éå¶ä¸º4~64
jobgroup_field_title_length=åç§°é¿åº¦éå¶ä¸º4~12
jobgroup_field_order_digits=è¯·è¾å¥æ´æ°
jobgroup_field_orderrange=åå¼èå´ä¸º1~1000
jobgroup_del_limit_0=æç»å é¤ï¼è¯¥æ§è¡å¨ä½¿ç¨ä¸­
jobgroup_del_limit_1=æç»å é¤, ç³»ç»è³å°ä¿çä¸ä¸ªæ§è¡å¨
jobgroup_empty=ä¸å­å¨æææ§è¡å¨,è¯·èç³»ç®¡çå

## job conf
jobconf_block_SERIAL_EXECUTION=åæºä¸²è¡
jobconf_block_DISCARD_LATER=ä¸¢å¼åç»­è°åº¦
jobconf_block_COVER_EARLY=è¦çä¹åè°åº¦
jobconf_route_first=ç¬¬ä¸ä¸ª
jobconf_route_last=æåä¸ä¸ª
jobconf_route_round=è½®è¯¢
jobconf_route_random=éæº
jobconf_route_consistenthash=ä¸è´æ§HASH
jobconf_route_lfu=æä¸ç»å¸¸ä½¿ç¨
jobconf_route_lru=æè¿æä¹æªä½¿ç¨
jobconf_route_failover=æéè½¬ç§»
jobconf_route_busyover=å¿ç¢è½¬ç§»
jobconf_route_shard=åçå¹¿æ­
jobconf_idleBeat=ç©ºé²æ£æµ
jobconf_beat=å¿è·³æ£æµ
jobconf_monitor=ä»»å¡è°åº¦ä¸­å¿çæ§æ¥è­¦
jobconf_monitor_detail=çæ§åè­¦æç»
jobconf_monitor_alarm_title=åè­¦ç±»å
jobconf_monitor_alarm_type=è°åº¦å¤±è´¥
jobconf_monitor_alarm_content=åè­¦åå®¹
jobconf_trigger_admin_adress=è°åº¦æºå¨
jobconf_trigger_exe_regtype=æ§è¡å¨-æ³¨åæ¹å¼
jobconf_trigger_exe_regaddress=æ§è¡å¨-å°ååè¡¨
jobconf_trigger_address_empty=è°åº¦å¤±è´¥ï¼æ§è¡å¨å°åä¸ºç©º
jobconf_trigger_run=è§¦åè°åº¦
jobconf_trigger_child_run=è§¦åå­ä»»å¡
jobconf_callback_child_msg1={0}/{1} [ä»»å¡ID={2}], è§¦å{3}, è§¦åå¤æ³¨: {4} <br>
jobconf_callback_child_msg2={0}/{1} [ä»»å¡ID={2}], è§¦åå¤±è´¥, è§¦åå¤æ³¨: ä»»å¡IDæ ¼å¼éè¯¯ <br>
jobconf_trigger_type=ä»»å¡è§¦åç±»å
jobconf_trigger_type_cron=Cronè§¦å
jobconf_trigger_type_manual=æå¨è§¦å
jobconf_trigger_type_parent=ç¶ä»»å¡è§¦å
jobconf_trigger_type_api=APIè§¦å
jobconf_trigger_type_retry=å¤±è´¥éè¯è§¦å
jobconf_trigger_type_misfire=è°åº¦è¿æè¡¥å¿

## user
user_manage=ç¨æ·ç®¡ç
user_username=è´¦å·
user_password=å¯ç 
user_role=è§è²
user_role_admin=ç®¡çå
user_role_normal=æ®éç¨æ·
user_permission=æé
user_add=æ°å¢ç¨æ·
user_update=æ´æ°ç¨æ·
user_username_repeat=è´¦å·éå¤
user_username_valid=éå¶ä»¥å°åå­æ¯å¼å¤´ï¼ç±å°åå­æ¯ãæ°å­ç»æ
user_password_update_placeholder=è¯·è¾å¥æ°å¯ç ï¼ä¸ºç©ºåä¸æ´æ°å¯ç 
user_update_loginuser_limit=ç¦æ­¢æä½å½åç»å½è´¦å·

## help
job_help=ä½¿ç¨æç¨
job_help_document=å®æ¹ææ¡£
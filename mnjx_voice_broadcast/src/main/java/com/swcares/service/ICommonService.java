package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxBroadcastTemplate;
import com.swcares.obj.vo.AudioVo;
import com.swcares.obj.vo.FlightVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/5 11:01
 */
public interface ICommonService {

    List<FlightVo> constructTemplateFlightVo(String flightNo, String gate, boolean forGenerate) throws UnifiedResultException;

    void generateAudioVo(List<AudioVo> audioVoList, MnjxBroadcastTemplate template, FlightVo flightVo);
}

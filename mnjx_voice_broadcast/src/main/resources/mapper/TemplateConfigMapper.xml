<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.TemplateConfigMapper">

    <select id="retrieveShaOffFlight" resultType="com.swcares.entity.MnjxFlight">
        SELECT distinct
            mf.*
        FROM
            mnjx_flight mf
            LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
            LEFT JOIN mnjx_tcard_section mts ON mt.tcard_id = mts.tcard_id
        WHERE
            mts.airport_id IN
            (
                SELECT
                    ma.airport_id
                FROM
                    mnjx_airport ma
                WHERE
                    ma.city_id =
                    (
                        SELECT
                            mc.city_id
                        FROM
                            mnjx_city mc
                        WHERE
                            mc.city_code = 'SHA'
                    )
            )
            AND mts.section_no = '1'
    </select>

    <select id="retrieveFlightNo" resultType="com.swcares.obj.vo.FlightVo">
        SELECT distinct
            mf.flight_no,
            mpf.plan_flight_id,
            mpf.ck_status,
            mps.gate
        FROM
            mnjx_flight mf
            LEFT JOIN mnjx_tcard mt ON mt.flight_id = mf.flight_id
            LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
            LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
        WHERE
            mpf.flight_date = #{fltDate}
            <if test="flightNo != null and flightNo != ''">
                and mf.flight_no = #{flightNo}
            </if>
            and mps.plan_section_id is not null
            and mps.dep_apt_id in (select ma.airport_id from mnjx_airport ma where ma.city_id = (select mc.city_id from mnjx_city mc where mc.city_code = #{org}))
        ORDER BY
            mps.estimate_off ASC,
            mps.actual_off asc
    </select>
</mapper>
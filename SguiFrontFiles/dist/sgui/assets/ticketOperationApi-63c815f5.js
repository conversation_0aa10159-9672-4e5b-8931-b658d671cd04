import{ae as e,af as s,fY as i}from"./index-5ab303b6.js";const r=t=>e(`${s}/crs/ticket/queryByPnr`).post({pnrNo:t}).json(),c=(t,n)=>e(`${s}/crs/ticket?ticketNo=${t}${n?`&type=${n}`:""}`).get().json(),a=t=>e(`${s}/crs/ticket/queryTicketDetail`).post(t).json(),u=t=>e(`${s}/crs/ticket/queryTicketDetail`,{ignoreError:!0}).post(t).json(),p=t=>e(`${s}/crs/ticket/queryTicketDigestsByCert`).post(t).json(),k=t=>e(`${s}/crs/ticket/getTicketDigestsByName`).post(t).json(),y=t=>e(`${s}/crs/ticket/invalid`).post(t).json(),A=t=>e(`${s}/crs/passenger/queryAllPassengersByTktNumber`).post(t).json(),l=(t,n)=>e(`${s}/crs/office/queryOfficeInformation`,{ignoreError:n??!1}).post(t).json(),g=t=>e(`${s}/crs/etTicket/getTol`).post(t).json(),T=t=>e(`${s}/crs/ticket/queryTicketByDetr`,{ignoreError:!0,originalValue:!0}).post(t).json(),d=t=>e(`${s}/crs/ticket/queryTicketByRtkt/${t}`,{ignoreError:!0,originalValue:!0}).get().json(),f=t=>e(`${s}/crs/ticket/queryTicketByRtkt/${t}`).get().json(),$=t=>e(`${s}/crs/ticket/tssChangeTicketStatus`).post(t).json(),j=t=>e(`${s}/crs/ticket/tssChangeTicketStatusByPnr`).post(t).json(),R=t=>e(`${s}/crs/ticket/etrfChangeTicketStatus`).post(t).json(),h=t=>e(`${s}/crs/bopRefund/dptr`).post(t).json(),B=t=>e(`${s}/crs/ticket/findRefundTicketAndFee`,{originalValue:!0}).post(t).json(),q=t=>e(`${s}/apiRefundTicket/batchRefund`).post(t).json(),m=t=>e(`${s}/apiRefundTicket/batchAutoRefund`).post(t).json(),C=t=>e(`${s}/apiRefundTicket/batchQueryTrfdZ`).post(t).json(),b=t=>e(`${s}/apiRefundTicket/deleteRefundForm`).post(t).json(),Q=t=>e(`${s}/crs/ticket/queryRefundForm`,{originalValue:!0}).post(t).json(),F=t=>e(`${s}/apiRefundTicket/modifyRefundForm`,{originalValue:!0}).post(t).json(),P=t=>e(`${s}/apiRefundTicket/ticket/refund/supplementary`,{originalValue:!0}).post(t).json(),V=t=>e(`${s}/crs/ticketAuth/airTicketDisplayAuth`,{originalValue:!0}).post(t).json(),D=t=>e(`${s}/crs/ticketAuth/airTicketRemoveAuth`,{originalValue:!0}).post(t).json(),S=t=>e(`${s}/crs/ticketAuth/airTicketAddAuth`,{originalValue:!0}).post(t).json(),E=t=>e(`${s}/crs/ticket/pullControl`).post(t).json(),N=t=>e(`${i}/crs/passenger/queryAllPassengersByPnrNo`).post(t).json(),v=t=>e(`${s}/apiRefundTicket/batchFindRefundFeeZ`).post(t).json(),_=t=>e(`${s}/crs/passenger/queryTicketByInvalid`).post(t).json(),I=t=>e(`${s}/crs/etTicket/getCRSStockTQTD`).post(t).json(),O=t=>e(`${s}/crs/ticketControl/getTicketPooll`).post(t).json(),z=t=>e(`${s}/crs/ticket/queryTicketManagementOrganization/${t}`,{ignoreError:!0,originalValue:!0}).get().json();export{u as A,c as B,T as C,b as D,F as E,l as F,O as G,g as H,I,a,r as b,p as c,k as d,m as e,f,C as g,A as h,_ as i,d as j,V as k,D as l,S as m,$ as n,B as o,R as p,Q as q,y as r,j as s,E as t,z as u,P as v,N as w,q as x,v as y,h as z};

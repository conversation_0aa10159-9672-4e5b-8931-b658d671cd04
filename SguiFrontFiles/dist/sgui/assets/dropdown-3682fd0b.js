import{_ as I,f as T,v as O,r as _,L as E,F as S,o as b,l as m,dv as L,B as f,C as t,db as $,K as o}from"./index-5ab303b6.js";import{l as h,u as C}from"./index-bc4d5ef3.js";const M=T({inheritAttrs:!1});function w(n,l,c,s,a,d){return O(n.$slots,"default")}var v=I(M,[["render",w],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection.vue"]]);const B=T({name:"ElCollectionItem",inheritAttrs:!1});function K(n,l,c,s,a,d){return O(n.$slots,"default")}var P=I(B,[["render",K],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection-item.vue"]]);const A="data-el-collection-item",x=n=>{const l=`El${n}Collection`,c=`${l}Item`,s=Symbol(l),a=Symbol(c),d={...v,name:l,setup(){const u=_(null),i=new Map;E(s,{itemMap:i,getItems:()=>{const r=m(u);if(!r)return[];const e=Array.from(r.querySelectorAll(`[${A}]`));return[...i.values()].sort((y,N)=>e.indexOf(y.ref)-e.indexOf(N.ref))},collectionRef:u})}},g={...P,name:c,setup(u,{attrs:i}){const p=_(null),r=S(s,void 0);E(a,{collectionItemRef:p}),b(()=>{const e=m(p);e&&r.itemMap.set(e,{ref:e,...i})}),L(()=>{const e=m(p);r.itemMap.delete(e)})}};return{COLLECTION_INJECTION_KEY:s,COLLECTION_ITEM_INJECTION_KEY:a,ElCollection:d,ElCollectionItem:g}},R=f({trigger:h.trigger,effect:{...C.effect,default:"light"},type:{type:t(String)},placement:{type:t(String),default:"bottom"},popperOptions:{type:t(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:t([Number,String]),default:0},maxHeight:{type:t([Number,String]),default:""},popperClass:{type:String,default:""},disabled:{type:Boolean,default:!1},role:{type:String,default:"menu"},buttonProps:{type:t(Object)},teleported:C.teleported}),D=f({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:$}}),U=f({onKeydown:{type:t(Function)}}),Y=[o.down,o.pageDown,o.home],j=[o.up,o.pageUp,o.end],V=[...Y,...j],{ElCollection:q,ElCollectionItem:z,COLLECTION_INJECTION_KEY:G,COLLECTION_ITEM_INJECTION_KEY:H}=x("Dropdown");export{H as C,q as E,V as F,j as L,D as a,A as b,x as c,R as d,z as e,U as f,G as g};

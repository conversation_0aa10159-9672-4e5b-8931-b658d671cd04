import{B as x,C as V,du as W,dM as $,dN as A,dw as j,r as f,a2 as G,L,g as B,h as r,f as C,j as F,m as H,v as N,p as m,l as e,_ as M,F as U,G as w,aa as q,H as z,x as y,w as I,dU as X,E as J,aF as Q,q as R,W as Y,X as Z,A as ee,O as se}from"./index-5ab303b6.js";import{c as K}from"./castArray-d2f89769.js";import{_ as ae}from"./index-342107ca.js";const S=()=>Math.floor(Math.random()*1e4),g=s=>typeof j(s),te=x({accordion:Boolean,modelValue:{type:V([Array,String,Number]),default:()=>W([])}}),le={[$]:g,[A]:g},T=Symbol("collapseContextKey"),oe=(s,o)=>{const a=f(K(s.modelValue)),n=l=>{a.value=l;const i=s.accordion?a.value[0]:a.value;o($,i),o(A,i)},t=l=>{if(s.accordion)n([a.value[0]===l?"":l]);else{const i=[...a.value],c=i.indexOf(l);c>-1?i.splice(c,1):i.push(l),n(i)}};return G(()=>s.modelValue,()=>a.value=K(s.modelValue),{deep:!0}),L(T,{activeNames:a,handleItemClick:t}),{activeNames:a,setActiveNames:n}},ne=()=>{const s=B("collapse");return{rootKls:r(()=>s.b())}},ie=C({name:"ElCollapse"}),ce=C({...ie,props:te,emits:le,setup(s,{expose:o,emit:a}){const n=s,{activeNames:t,setActiveNames:l}=oe(n,a),{rootKls:i}=ne();return o({activeNames:t,setActiveNames:l}),(c,u)=>(F(),H("div",{class:m(e(i))},[N(c.$slots,"default")],2))}});var re=M(ce,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse/src/collapse.vue"]]);const de=x({title:{type:String,default:""},name:{type:V([String,Number]),default:()=>S()},disabled:Boolean}),ue=s=>{const o=U(T),a=f(!1),n=f(!1),t=f(S()),l=r(()=>o==null?void 0:o.activeNames.value.includes(s.name));return{focusing:a,id:t,isActive:l,handleFocus:()=>{setTimeout(()=>{n.value?n.value=!1:a.value=!0},50)},handleHeaderClick:()=>{s.disabled||(o==null||o.handleItemClick(s.name),a.value=!1,n.value=!0)},handleEnterClick:()=>{o==null||o.handleItemClick(s.name)}}},pe=(s,{focusing:o,isActive:a,id:n})=>{const t=B("collapse"),l=r(()=>[t.b("item"),t.is("active",e(a)),t.is("disabled",s.disabled)]),i=r(()=>[t.be("item","header"),t.is("active",e(a)),{focusing:e(o)&&!s.disabled}]),c=r(()=>[t.be("item","arrow"),t.is("active",e(a))]),u=r(()=>t.be("item","wrap")),b=r(()=>t.be("item","content")),h=r(()=>t.b(`content-${e(n)}`)),_=r(()=>t.b(`head-${e(n)}`));return{arrowKls:c,headKls:i,rootKls:l,itemWrapperKls:u,itemContentKls:b,scopedContentId:h,scopedHeadId:_}},me=["id","aria-expanded","aria-controls","aria-describedby","tabindex"],ve=["id","aria-hidden","aria-labelledby"],fe=C({name:"ElCollapseItem"}),Ce=C({...fe,props:de,setup(s,{expose:o}){const a=s,{focusing:n,id:t,isActive:l,handleFocus:i,handleHeaderClick:c,handleEnterClick:u}=ue(a),{arrowKls:b,headKls:h,rootKls:_,itemWrapperKls:O,itemContentKls:P,scopedContentId:E,scopedHeadId:k}=pe(a,{focusing:n,isActive:l,id:t});return o({isActive:l}),(v,d)=>(F(),H("div",{class:m(e(_))},[w("button",{id:e(k),class:m(e(h)),"aria-expanded":e(l),"aria-controls":e(E),"aria-describedby":e(E),tabindex:v.disabled?-1:0,type:"button",onClick:d[0]||(d[0]=(...p)=>e(c)&&e(c)(...p)),onKeydown:d[1]||(d[1]=Q(R((...p)=>e(u)&&e(u)(...p),["stop","prevent"]),["space","enter"])),onFocus:d[2]||(d[2]=(...p)=>e(i)&&e(i)(...p)),onBlur:d[3]||(d[3]=p=>n.value=!1)},[N(v.$slots,"title",{},()=>[q(z(v.title),1)]),y(e(J),{class:m(e(b))},{default:I(()=>[y(e(X))]),_:1},8,["class"])],42,me),y(e(ae),null,{default:I(()=>[Y(w("div",{id:e(E),role:"region",class:m(e(O)),"aria-hidden":!e(l),"aria-labelledby":e(k)},[w("div",{class:m(e(P))},[N(v.$slots,"default")],2)],10,ve),[[Z,e(l)]])]),_:3})],2))}});var D=M(Ce,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse/src/collapse-item.vue"]]);const Ee=ee(re,{CollapseItem:D}),we=se(D);export{we as E,Ee as a,S as g};

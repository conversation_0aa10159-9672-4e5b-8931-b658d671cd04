import{b as Vn,i as jn,d as Gn,e as Yn,f as Qn,g as Wn,h as zn,j as Jn}from"./index-bc4d5ef3.js";import{di as Zn,i as Xn,I as B,er as hs,ai as ys,eq as Kn,r as X,h as We,ee as At,aS as vs,ae as Fe,fP as we,fr as ea,f as nt,b1 as ta,o as kt,j as m,k as He,w as re,x as pe,G as u,H as f,m as b,a8 as le,a9 as ue,a1 as Wt,aE as sa,aI as na,ag as aa,aJ as os,ed as ra,W as ms,l as k,p as ve,aj as zt,aa as ye,z as ae,v as bs,q as _s,a7 as oa,ab as Cs,aC as As,ac as Jt,ad as Zt,s as It,aq as ia,E as la,aB as ca,a2 as da,aT as Is,e9 as ua,ea as ga,b0 as pa,ap as fa,fQ as Qt,bz as ha,fR as is,aH as ls}from"./index-5ab303b6.js";import{g as xt,s as ya}from"./operatorDB-a8951d00.js";import{q as va}from"./dictApi-470e57c4.js";import{f as bt,a as tt}from"./common-98632f19.js";import{a as ma}from"./add-e7c966c8.js";import{E as ba}from"./index-52e5474f.js";import{_ as _a}from"./theme-light_empty-0081a108.js";import{_ as Nt}from"./_plugin-vue_export-helper-c27b6911.js";import{c as De}from"./cloneDeep-315a2c4c.js";import{S as Ca,c as Aa}from"./isEqual-4a5cdd78.js";import{c as Ia}from"./index-00518de1.js";import{b as Ts}from"./index-b0b7b170.js";import{b as ks,i as Tt}from"./isArrayLikeObject-78cb5abd.js";import{E as Ta,a as ka}from"./index-8808b036.js";import{E as Xt}from"./index-f150fc6a.js";import{E as xs,a as xa}from"./index-9e80bda4.js";import{E as Na}from"./index-0d57d4fc.js";import{E as Da}from"./index-f8c2f756.js";import{j as cs,C as Fa}from"./ticketOperationApi-63c815f5.js";import{a as wa}from"./time-767b803c.js";import{a6 as Ra}from"./regular-crs-4398c105.js";import{a as Ea}from"./config-bf0271ab.js";import{E as Sa,a as $a}from"./index-3c1d531c.js";import{E as Pa}from"./index-e3de2551.js";function La(e){return e!==e}function Ma(e,a,r){for(var o=r-1,l=e.length;++o<l;)if(e[o]===a)return o;return-1}function Oa(e,a,r){return a===a?Ma(e,a,r):Ia(e,La,r)}function Ba(e,a){var r=e==null?0:e.length;return!!r&&Oa(e,a,0)>-1}function Ha(e,a,r){for(var o=-1,l=e==null?0:e.length;++o<l;)if(r(a,e[o]))return!0;return!1}var Ua=200;function Ns(e,a,r,o){var l=-1,t=Ba,d=!0,y=e.length,h=[],v=a.length;if(!y)return h;r&&(a=Zn(a,Vn(r))),o?(t=Ha,d=!1):a.length>=Ua&&(t=Aa,d=!1,a=new Ca(a));e:for(;++l<y;){var C=e[l],x=r==null?C:r(C);if(C=o||C!==0?C:0,d&&x===x){for(var F=v;F--;)if(a[F]===x)continue e;h.push(C)}else t(a,x,o)||h.push(C)}return h}var qa=ks(function(e,a){return Tt(e)?Ns(e,Ts(a,1,Tt,!0)):[]});const Va=qa;function ja(e){var a=e==null?0:e.length;return a?e[a-1]:void 0}var Ga=ks(function(e,a){var r=ja(a);return Tt(r)&&(r=void 0),Tt(e)?Ns(e,Ts(a,1,Tt,!0),void 0,r):[]});const Ya=Ga;var Qa="[object Map]",Wa="[object Set]",za=Object.prototype,Ja=za.hasOwnProperty;function ru(e){if(e==null)return!0;if(jn(e)&&(Xn(e)||typeof e=="string"||typeof e.splice=="function"||Gn(e)||Yn(e)||Qn(e)))return!e.length;var a=Wn(e);if(a==Qa||a==Wa)return!e.size;if(zn(e))return!Jn(e).length;for(var r in e)if(Ja.call(e,r))return!1;return!0}function Za(e){return{all:e=e||new Map,on:function(a,r){var o=e.get(a);o?o.push(r):e.set(a,[r])},off:function(a,r){var o=e.get(a);o&&(r?o.splice(o.indexOf(r)>>>0,1):e.set(a,[]))},emit:function(a,r){var o=e.get(a);o&&o.slice().map(function(l){l(r)}),(o=e.get("*"))&&o.slice().map(function(l){l(a,r)})}}}const Ds=Za(),et=(e,a)=>{Ds.emit(e,a)},ou=Ds;var Y=(e=>(e.FLIGHT_SS="addFlightType",e.FLIGHT_STATUS="flightStatus",e.COMPANY_CODE="companyCode",e.FLIGHT_NUMBER="flightNumber",e.CABIN_CODE="cabinCode",e.DEPARTURE_DATE="departureDate",e.DEPARTURE_AIRPORT="departureAirport",e.DATE_TIME_RANGE="dateTimeRange",e.ARRIVAL_AIRPORT="arrivalAirport",e.STOP_QUANTITY="stopQuantity",e.STOP_FLAG="stopFlag",e.AIR_EQUIPMENT_TYPE="airEquipmentType",e.OC_AIRLINE="ocAirline",e.OPEN_AND_CLOSE_CABINS="openAndCloseCabins",e.OPEN_CABINS="openCabins",e.CLOSE_CABINS="closeCabins",e.GLOBAL_INDICATOR="globalIndicator",e))(Y||{});const Ut=new Map([[Y.FLIGHT_SS,/^SS/],[Y.FLIGHT_STATUS,/[SLOU]/],[Y.COMPANY_CODE,/([a-zA-Z]{1}[0-9]{1})|^([0-9]{1}[a-zA-Z]{1})|^([A-Za-z]{2})/],[Y.FLIGHT_NUMBER,/\d{3,7}/],[Y.CABIN_CODE,/[A-Za-z]{1}|\//],[Y.DEPARTURE_DATE,/\d{2}[A-Za-z]{3}\d{2}|\d{2}[A-Za-z]{3}/],[Y.DEPARTURE_AIRPORT,/[A-Za-z]{3}/],[Y.DATE_TIME_RANGE,/\d{4}(<|>|\+)?\d{4}/],[Y.ARRIVAL_AIRPORT,/[A-Za-z]{3}/],[Y.STOP_QUANTITY,/\d+/],[Y.STOP_FLAG,/[SXU]/],[Y.GLOBAL_INDICATOR,/#V.{2}/],[Y.AIR_EQUIPMENT_TYPE,/[a-zA-Z0-9]{3}/],[Y.OC_AIRLINE,/^#O(?:(?![0-9]{2})[A-Z0-9]{2})?/],[Y.OPEN_AND_CLOSE_CABINS,/#D[a-zA-Z]*#C[a-zA-Z]*|#C[a-zA-Z]*#D[a-zA-Z]*|#C[a-zA-Z]*|#D[a-zA-Z]*/],[Y.OPEN_CABINS,/#D[a-zA-Z]*/],[Y.CLOSE_CABINS,/#C[a-zA-Z]*/]]),iu=async e=>{if((e??[]).length<=0)return!1;const a=await xt("searchLocalData"),r=JSON.parse((a==null?void 0:a.localData)??"")??[];return r.length===0?!1:[...new Set(e)].some(l=>{const t=r.find(d=>d.airportCode===l);return((t==null?void 0:t.firstLevel)??"")==="国际"})},Gt=(e,a)=>e.some(r=>r.segments.some(o=>!!(a[o.arrivalAirportCode]&&a[o.arrivalAirportCode].firstLevel==="国际"||a[o.departureAirportCode]&&a[o.departureAirportCode].firstLevel==="国际"))),Fs=e=>e&&["MISS","MSTR"].includes(e)?e:"",Xa=(e="",a="ADT",r="")=>{if(a.includes("_")){const o=a.split("_")[1];return`${e} ${Fs(r)} ${o}`}return a==="LBR"||a==="SEA"?`${e} ${a}`:e},lu=(e,a,r="",o)=>{const l={pnrNo:r||a.pnrNo,operationType:e,passengers:[],segments:[],infants:[]};return o&&Number(a.groupDto.totalSeats)>9?l.passengers=[{name:"--空--",type:""}]:(a.passengers??[]).forEach(t=>{var d,y;l.passengers.push({name:r?Xa((t==null?void 0:t.fullName)??"",(t==null?void 0:t.passengerType)??"",t.nameSuffix):(t==null?void 0:t.fullName)??"",type:(t==null?void 0:t.passengerType)??""}),t.infantDetail&&JSON.stringify(t.infantDetail)!=="null"&&l.infants.push({name:`${((d=t==null?void 0:t.infantDetail)==null?void 0:d.fullName)??""} ${Fs(((y=t==null?void 0:t.infantDetail)==null?void 0:y.nameSuffix)??"")}`,type:"INF"})}),a.flight.forEach(t=>{var d,y,h,v;l.segments.push({departureAirport:((y=(d=t.segments)==null?void 0:d[0])==null?void 0:y.departureAirportCode)??"",arrivalAirport:((v=(h=t.segments)==null?void 0:h[t.segments.length-1])==null?void 0:v.arrivalAirportCode)??""})}),l},Ke=e=>`${e.origin}${e.destination}${B(e.departureDate).format("DD/MM/YYYY")}`.toUpperCase(),ze=(e,a)=>{var t,d,y;const r=h=>h&&B(h)&&B(h).isAfter(new Date("2000-01-01"))?B(h).format("YYYY-MM-DD"):"",o=(e.segments??[]).map(h=>{var C,x,F;return`${a?`${((C=h.cabins)==null?void 0:C[0]).cabinName??""}${h.marriedSegmentNumber??""}`:""}${h.departureAirportCode??""}${h.arrivalAirportCode??""}${r(h.departureDate)}${r(h.arrivalDate)}${((x=h.airlines)==null?void 0:x.airCode)??""}${((F=h.airlines)==null?void 0:F.flightNo)??""}`}).join(""),l=["OPEN","ARNK"].includes((y=(d=(t=e==null?void 0:e.segments)==null?void 0:t[0])==null?void 0:d.airlines)==null?void 0:y.flightNo.toUpperCase())?`open-arnk-${Math.random()*1e3}`:"";return`${o}${l}`.toUpperCase()},Ka=(e,a)=>{var o;const r=B(a||e.departureDate).format("YYYY-MM-DD");return`${e.departureAirportCode}/${e.arrivalAirportCode}/${(o=e.airlines)==null?void 0:o.flightNo.toUpperCase()}/${r}`},er=(e,a)=>["Q","A"].includes(e)||/^\d+$/.test(e)?"NN":["L","S"].includes(e)?"LL":e==="C"&&a?"GK":"NN";var tr=(e=>(e.SK_QUERY="SKQuery",e.NFD_QUERY="queryNetPrice",e.FD_QUERY="netFare",e.FSD_QUERY="InternationalPublishedRatesQuery",e))(tr||{}),sr=(e=>(e.AD="adult",e.CH="child",e.IN="baby",e))(sr||{}),ws=(e=>(e.SEARCH="SEARCH",e.TAB_TO_SEARCH="TAB_TO_SEARCH",e))(ws||{});const nr=hs("fastQuery",{state:()=>({historyAirline:{domesticHistory:[],internationalHistory:[]},queryHistory:new Map,targetInfo:{},showManualRefund:!1,rtktDetailInfoWindows:{active:"",list:[]},isOpened:!1}),getters:{getHistoryAirline(e){return e.historyAirline},getRtktDetailInfoWindows:e=>e.rtktDetailInfoWindows},actions:{setHistoryAirLine(e){this.historyAirline=e,localStorage.setItem("agentHistoryAirline",JSON.stringify(e))},setIsOpened(e){this.isOpened=e},setQueryHistory(e,a,r){var d,y;const o=De(a),l=Ke(o);if((d=this.queryHistory.get(e))!=null&&d.activeTagKey){r||(this.queryHistory.get(e).activeTagKey=l);const h=((y=this.queryHistory.get(e))==null?void 0:y.list.findIndex(v=>Ke(v)===l))??-1;if(h>-1){this.queryHistory.get(e).list.splice(h,1,o);return}this.queryHistory.get(e).list.push(o);return}const t={activeTagKey:l,list:[o]};this.queryHistory.set(e,t)},setQueryHistoryBySk(e,a){var t,d;const r=De(a.queryForm),o=Ke(r);if((t=this.queryHistory.get(e))!=null&&t.activeTagKey){if(this.queryHistory.get(e).activeTagKey=o,a.queryType===ws.TAB_TO_SEARCH)return;const y=((d=this.queryHistory.get(e))==null?void 0:d.list.findIndex(h=>Ke(h)===o))??-1;y>-1&&this.queryHistory.get(e).list.splice(y,1),this.queryHistory.get(e).list.unshift(r);return}const l={activeTagKey:o,list:[r]};this.queryHistory.set(e,l)},delQueryHistory(e,a){var o,l,t;const r=((o=this.queryHistory.get(e))==null?void 0:o.list.findIndex(d=>Ke(d)===Ke(a)))??-1;if(r>-1){if(((l=this.queryHistory.get(e))==null?void 0:l.activeTagKey)===Ke(a)&&this.queryHistory.get(e).list.length>1){const d=r+1<(((t=this.queryHistory.get(e))==null?void 0:t.list.length)??0)?r+1:0;this.queryHistory.get(e).activeTagKey=Ke(this.queryHistory.get(e).list[d])}this.queryHistory.get(e).list.splice(r,1),this.queryHistory.get(e).list.length||(this.queryHistory.get(e).activeTagKey="")}},setFastQueryTargetInfo(e){this.targetInfo={queryFlag:!0,targetInfo:e}},closeFastQuery(){this.targetInfo={queryFlag:!1,targetInfo:{}},this.setIsOpened(!1)},setShowManualRefund(e){this.showManualRefund=e},setActiveRtktDetailInfoWindows(e){this.rtktDetailInfoWindows.active=e},setRtktDetailInfoWindowsList(e){const a=e.id;this.setActiveRtktDetailInfoWindows(a),this.rtktDetailInfoWindows.list.push({...e,id:a,isShow:!0})},delRtktDetailInfoWindowsList(e){this.rtktDetailInfoWindows.list=this.rtktDetailInfoWindows.list.filter(a=>a.id!==e)}}}),ar=nr,Ve={PASSENGER_REMOVE_CHECK_SERVICE:"passenger_remove_check_service",ADD_PASSENGER:"AddPassenger.vue",SINGLE_ADD_INF:"SingleAddInf.vue",USE_CHANGE_QUERY_DOM:"RefundTicketFareDom.vue",USE_CHANGE_QUERY_INTER:"RefundTicketFareInter.vue",PNR_MANAGEMENT:"PnrManagement.vue",UPDATE_FLIGHT_LIST:"updateFlightList",UPDATE_PASSENGER_FLIGHT_LIST:"updatePassengerFlightList",ADD_SPECIALSEVICE:"ServiceInfoDialog.vue",UPDATE_PAYMENT_METHOD:"updatePaymentMethod",UPDATE_PAY_METHOD_TYPE:"updatePayMethodType",RT:"RT",FLIGHT_NOT_NEED_SORT:"FLIGHT_NOT_NEED_SORT",DELETE_LEFT_FLIGHT:"DELETE_LEFT_FLIGHT",DELETE_LEFT_PLACEHOLDER_FLIGHT:"DELETE_LEFT_PLACEHOLDER_FLIGHT",TO_VIEW_PLACEHOLDER_FLIGHT:"TO_VIEW_PLACEHOLDER_FLIGHT",PLACEHOLDER_FLIGHT:"PLACEHOLDER_FLIGHT",FAST_QUERY_AV_PLACEHOLDER_FLIGHT:"FAST_QUERY_AV_PLACEHOLDER_FLIGHT",EDIT_FLIGHT_PLACEHOLDER:"EDIT_FLIGHT_PLACEHOLDER",CLEAR_FLIGHT_PLACEHOLDER:"CLEAR_FLIGHT_PLACEHOLDER",OPERATE_LEFT_FLIGHT:"OPERATE_LEFT_FLIGHT",BATCH_EDIT_PASSENGER:"BatchPassenger.vue",QUERY_MARRIED_FLIGHT:"QUERY_MARRIED_FLIGHT",PREVIEW_TICKET:"PreviewTicket.vue",SPECIAL_SERVICE_PSG_SEG_SELECT:"PassengerSegSelectNew.vue"},ds=e=>e===""?"":B(e).format("YYYY-MM-DDTHH:mm"),cu=(e,a)=>{const r={bookAirSegs:[]};return a.forEach(o=>{o.segments.forEach(l=>{var d,y,h;if(l.segmentType==="2"||o.openFlag||(((d=l==null?void 0:l.airlines)==null?void 0:d.flightNo)??"")==="ARNK")return;const t={fltClass:((y=l.cabins[0])==null?void 0:y.cabinName)??"",orgCity:l.departureAirportCode,desCity:l.arrivalAirportCode,departureTime:ds((l==null?void 0:l.departureDate)??""),arrivalTime:ds((l==null?void 0:l.arrivalDate)??""),airCode:l.airlines.airCode+l.airlines.flightNo,tktNum:e.toString(),equipmentCode:l.airlines.planeType,etInd:l.etInd??!1,departureTerminal:l.departureTerminal,arrivalTerminal:l.arrivalTerminal,actionCode:er(((h=l.cabins[0])==null?void 0:h.state)??"",!0)};r.bookAirSegs.push(t)})}),r},du=async(e,a,r)=>{const o=vt(),l=o.orderInfo;let t=!1;const d=Array.from(l.keys()),y=d==null?void 0:d.find(h=>{var v,C;return h!==e&&((C=(v=l.get(h))==null?void 0:v.specialContent)==null?void 0:C.includes(a("app.basic.occupy")))});if(y){const v=l.get(y).tagName;await ys.confirm(r("div",{class:"seizeseat-info-tip-box"},a("app.pnrManagement.flight.seizeSeatTip",{tagName:v})),{icon:r("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"flight-info-msg-detele crs-btn-ui",confirmButtonText:a("app.pnrManagement.flight.confirm"),cancelButtonText:a("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{t=!0,o.setActiveTag(y)})}return t},rr=(e,a)=>{if(!e)return;a.deptArrdays="",a.arrivalArrdays="";const r=o=>{let l=0;o&&B(o).isValid()&&(l=B(o).diff(e,"day")??0);let t="";return l&&(t=l>0?`+${l}`:`-${l}`),t};a.deptArrdays=r(a.departureDate),a.arrivalArrdays=r(a.arrivalDate)},or=e=>{var a;if(!e.key){if(((a=e.segments)==null?void 0:a[0].segmentType)==="2"){e.key=Ka(e.segments[0]);return}e.key=ze(e)}},ir=(e,a)=>{var l,t,d,y;const r=((t=(l=a==null?void 0:a.segments)==null?void 0:l[0])==null?void 0:t.departureAirportCode)??"";return((d=e==null?void 0:e.segments)!=null&&d.length?(y=e.segments[e.segments.length-1])==null?void 0:y.arrivalAirportCode:r)!==r},uu=(e,a,r,o)=>{const l=De(e),t=[];let d=0;return l.forEach((h,v)=>{var x,F;or(h),h.isMarried=!1,h.notchPath=!1,h.notFormNavigation=!1;const C=B(h.segments[0].departureDate).isValid()?B(h.segments[0].departureDate).format("YYYY-MM-DD"):"";if(h.segments.forEach(N=>{var E;rr(C,N),(!B(N.departureDate).isValid()||B(N.departureDate).isBefore(new Date("2000-01-01"),"year"))&&(N.departureDate=""),((E=N.airlines)==null?void 0:E.flightNo)!=="ARNK"&&(d++,N.orderNumber=d,h.showEditBtn=!h.openFlag||!N.seatTag||!!N.marriedSegmentNumber),o&&!Number(N.segmentType)&&(N.orderNumber=0),N.marriedSegmentNumber&&(t.includes(N.marriedSegmentNumber)?h.isMarried=!0:t.push(N.marriedSegmentNumber))}),v){const N=lr(e,v),E=h;h.notchPath=!h.disabled&&ir(N,E),JSON.stringify(N)!=="{}"&&!E.disabled&&((F=(x=E.segments[0])==null?void 0:x.airlines)==null?void 0:F.flightNo)!=="ARNK"?h.notFormNavigation=!h.openFlag&&cr(e,v,h):h.notFormNavigation=!1;return}}),{flightAll:l,flightNumber:d}},lr=(e,a)=>{let r={};return e.slice(0,a).reverse().some(l=>l.disabled?!1:(r=l,!0)),r},cr=(e,a,r)=>{const l=e.slice(0,a).reverse().find(t=>{var d,y;return!t.disabled&&((y=(d=t.segments[0])==null?void 0:d.airlines)==null?void 0:y.flightNo)!=="ARNK"});return l?B(r.segments[0].departureUTCDate).diff(B(l.segments[l.segments.length-1].arriveUTCDate))<=0:!1},us=e=>{if(!e)return!0;const a=B(e).hour()===0,r=B(e).minute()===0;return a&&r},dr=(e,a)=>{const r=e.segments[0].departureAirportCode,o=e.segments[0].arrivalAirportCode,l=a.segments[0].departureAirportCode,t=a.segments[0].arrivalAirportCode;return r===t?1:l===o?-1:0},ur=(e,a)=>{var r;if(B(e.segments[0].departureUTCDate).isSame(B(a.segments[0].departureUTCDate),"day")){let o,l,t;const d=[];e.openFlag&&(o=e.openFlag,d.push(e),l=us(e.segments[0].departureUTCDate)),a.openFlag&&(d.push(a),t=us((r=a.segments[0])==null?void 0:r.departureUTCDate));const y=d.length===1,h=dr(e,a);return y?!l&&!t?0:h||(o?1:-1):h}return B(e.segments[0].departureDate).diff(B(a.segments[0].departureDate))},gr=e=>{e.sort((a,r)=>{var l,t;return!a.openFlag&&!r.openFlag?B(a.segments[0].departureUTCDate).diff(B(r.segments[0].departureUTCDate)):st(!0,((l=a.segments[0])==null?void 0:l.departureUTCDate)??"")&&!st(!0,((t=r.segments[0])==null?void 0:t.departureUTCDate)??"")?-1:ur(a,r)})},pr=(e,a)=>{var t;const l=(((t=vt().originPnrData.get(a))==null?void 0:t.flight)??[]).find(d=>d.segments.find(y=>y.lineIndex&&y.lineIndex===e.segments[0].lineIndex));if(l){const d=l.segments[0],y=e.segments[0];if(B(d.departureDate).isValid()&&B(y.departureDate).isValid())return!B(d.departureDate).isSame(B(y.departureDate),"day")}return!1},gu=(e,a)=>{const r=[],o=[];return e.forEach(l=>{var t;if(((t=l.segments[0])==null?void 0:t.segmentType)==="1"&&!pr(l,a)){r.push(l);return}o.push(l)}),yr(r,o)},st=(e,a,r="2000-01-01")=>e?B(a).isValid()&&B(a).isAfter(B(new Date(r)),"day"):B(a).isValid()&&B(a).isAfter(new Date(r)),Rs=(e,a)=>{if(!a.length)return e;let r=[];return e.forEach((o,l)=>{var y;let t=[],d=o.openFlag;if(st(d,((y=o.segments[0])==null?void 0:y.departureUTCDate)??"")){let h=o.segments[0].departureUTCDate;if(d){const x=e.slice(l+1,e.length).find(F=>{if(F.openFlag)return!1;const N=F.segments[0];return N.airlines.flightNo!=="ARNK"&&B(N.departureUTCDate).isSame(B(o.segments[0].departureUTCDate),"day")});x&&(h=x.segments[0].departureUTCDate,d=!1)}let v=a.findIndex(C=>{var x;return st(d,((x=C.segments[0])==null?void 0:x.departureUTCDate)??"",h)});v<0&&(v=a.findIndex(C=>{var x;return!st(d,((x=C.segments[0])==null?void 0:x.departureUTCDate)??"")})),t=v>-1?a.splice(0,v):a.splice(0)}else if(!e.slice(l+1,e.length).some(C=>{var x;return st(d,((x=C.segments[0])==null?void 0:x.departureUTCDate)??"")})){const C=r[r.length-1],x=C==null?void 0:C.segments[C.segments.length-1].arrivalAirportCode,F=o.segments[0].departureAirportCode;x!==F&&(t=a.splice(0))}r=[...r,...t],r.push(o)}),r=[...r,...a],r},Es=(e,a,r)=>B(r).isValid()?!B(r).isAfter(B(a),"day")&&!B(r).isBefore(B(e),"day"):!1,fr=(e,a,r,o,l)=>{const t=a.find(N=>!N.disabled),d=a.find(N=>{var E;return st(!0,(E=N==null?void 0:N.segments)==null?void 0:E[0].departureDate)}),y=r.find(N=>{var E;return st(!0,(E=N==null?void 0:N.segments)==null?void 0:E[0].departureDate)});let h="",v="";d!=null&&d.segments.length&&(h=d.segments[d.segments.length-1].departureDate),y!=null&&y.segments.length&&(v=y.segments[0].departureDate);const C=t?t.segments[t.segments.length-1].arrivalAirportCode:"",x=e.segments[0].departureAirportCode;return!B(o.departureDate).isValid()||Es(h,v,o.departureDate)?l?o.departureAirportCode===C&&o.arrivalAirportCode===x:o.departureAirportCode===C||o.arrivalAirportCode===x:!1},Ht=(e,a,r)=>{const o=[];return a.forEach(l=>{const t=e.findIndex((d,y)=>{if(!d.disabled){const h=e.slice(0,y).reverse(),v=e.slice(y),C=h.find(x=>!x.disabled);if(C){const x=C.segments[C.segments.length-1].arrivalAirportCode,F=d.segments[0].departureAirportCode;if(x!==F)return fr(d,h,v,l.segments[0],r)}}return!1});if(t>-1){l.associationKey=`ARNK/${e[t].key}/${e[t].key}`,e.splice(t,0,l);return}o.push(l)}),o},hr=(e,a)=>{const r=[],o=[];a.forEach(F=>{var N;return B((N=F.segments)==null?void 0:N[0].departureDate).isValid()?o.push(F):r.push(F)});const l=o.sort((F,N)=>{var E,P;return B((E=F.segments)==null?void 0:E[0].departureDate).isBefore(B((P=N.segments)==null?void 0:P[0].departureDate))?-1:1}),t=Ht(e,l,!0),d=Ht(e,t),y=Ht(e,r,!0),h=Ht(e,y),v=[...d,...h],C=v.findIndex(F=>{var N,E,P,O,j,ee,L;return!B((N=F==null?void 0:F.segments)==null?void 0:N[0].departureDate).isValid()||Es("",(P=(E=e==null?void 0:e[0])==null?void 0:E.segments)==null?void 0:P[0].departureDate,(O=F==null?void 0:F.segments)==null?void 0:O[0].departureDate)?((j=F==null?void 0:F.segments)==null?void 0:j[0].arrivalAirportCode)===((L=(ee=e==null?void 0:e[0])==null?void 0:ee.segments)==null?void 0:L[0].departureAirportCode):!1});return C>-1&&(e.unshift(v[C]),v.splice(C,1)),Rs(e,v)},yr=(e,a)=>{const r=De(e);if(!a.length)return r;const o=De(a),l=[],t=[];gr(o),o.forEach(y=>{y.segments[0].airlines.flightNo==="ARNK"?t.push(y):l.push(y)});const d=Rs(r,l);return hr(d,t)},Ss=(e,a)=>{let r=[];const o=[];return e.forEach(l=>{const t=l.segments.filter(d=>d.marriedSegmentNumber&&d.marriedSegmentNumber===a);t.length&&(r=r.concat(t),o.push(l.key??""))}),{segments:r,flightKeys:o}},pu=(e,a)=>{const{segments:r}=Ss(e,a);return r??[]},fu=(e,a)=>{const r=a.segments[0].marriedSegmentNumber;let o=[],l=[];if(r){const{segments:d,flightKeys:y}=Ss(e,r);l=d,o=y}else l=a.segments,o.push(a.key??"");const t={segs:[]};return l.forEach(d=>{const y={fltClass:d.cabins[0].cabinName??"",orgCity:d.departureAirportCode,desCity:d.arrivalAirportCode,departureTime:B(d.departureDate).format("YYYY-MM-DDTHH:mm:ss"),actionCode:d.seatTag??"",airCode:`${d.airlines.airCode}${d.airlines.flightNo}`,tktNum:d.tktNum??"",lineIndex:d.lineIndex??""};t.segs.push(y)}),{params:t,flightKeys:o}},vr=(e,a,r,o)=>{const l=[];return a.segments.forEach((t,d)=>{if(r===d)return;const y={...a};y.segments=[{...t}];const h=ze(y);y.key=h,l.push(y)}),e.splice(o,1,...l),e},mr=(e,a,r)=>{const o=(e.segments??[]).filter(h=>h.marriedSegmentNumber).map(h=>h.marriedSegmentNumber);if(o.length)return(a??[]).filter(h=>!h.segments.some(v=>o.includes(v.marriedSegmentNumber)));const l=(a??[]).findIndex(h=>h.key===e.key&&h.segments[0].segmentType===e.segments[0].segmentType),t=a[l],d=(t==null?void 0:t.segments)??[];return t&&typeof r=="number"&&d.length>1&&r>=0?vr(a,t,r,l):(a??[]).filter(h=>!(h.key===e.key&&h.segments[0].segmentType===e.segments[0].segmentType))},br=(e,a,r)=>mr(a,e,r),hu=(e,a)=>(e??[]).filter(r=>{var o,l;return!(r.key===a.key&&r.segments[0].segmentType===a.segments[0].segmentType&&((o=r.segments[0].cabins)==null?void 0:o[0]).cabinName===((l=a.segments[0].cabins)==null?void 0:l[0]).cabinName)}),_r=(e,a,r,o)=>{const l=vt(),t=br(e,a);l.setCheckedFlight(r,o,t)},yu=(e,a,r,o,l,t)=>{const d=vt();a&&_r(r,o,l,t),e==="0"&&d.setDefaultJumpHistory(l)},vu=(e,a)=>{const r=a.segments.filter(o=>o.marriedSegmentNumber).map(o=>o.marriedSegmentNumber);e.forEach(o=>{if(r.length){let l=!1;o.segments.forEach(t=>{l||(l=r.includes(t.marriedSegmentNumber)),l&&(t.isCheck=a.isChecked)}),l&&(o.isChecked=a.isChecked)}else a.key&&o.key===a.key&&(o.isChecked=a.isChecked,o.segments.forEach(l=>l.isCheck=a.isChecked))})},Cr=(e,a)=>{var r,o;return e.key===a.key&&((r=e.segments[0])==null?void 0:r.actionCode)===((o=a.segments[0])==null?void 0:o.actionCode)},Ar=(e,a,r)=>{var o,l;return e.segments.some(t=>r.includes(t.marriedSegmentNumber))&&((o=e.segments[0])==null?void 0:o.actionCode)===((l=a.segments[0])==null?void 0:l.actionCode)},gs=(e,a)=>{const r=a.segments.filter(l=>l.marriedSegmentNumber).map(l=>l.marriedSegmentNumber);let o=[];return o=e.map(l=>{const t=r.length?Ar(l,a,r):Cr(l,a);return Object.assign({},l,{disabled:t?!l.disabled:l.disabled})}),o},mu=(e,a,r)=>{a.flight=e,vt().setFlights(r,a)},ps=e=>{var r;let a=0;return e!=null&&e.isVoluntaryRescheduling&&(a=1),(r=e==null?void 0:e.newChange)!=null&&r.isNewVoluntaryRescheduling&&(a=2),a},Yt=(e,a)=>{let r="",o=!1;const l=d=>{var y;return d!=null&&d.cabins.length?(y=d.cabins[0])==null?void 0:y.cabinName:""},t=e.find(d=>{var h,v,C,x;const y=((h=d.segments)==null?void 0:h[0].segmentType)!=="2"&&((v=d.segments)==null?void 0:v[0].airlines.flightNo)!=="ARNK"&&!d.disabled&&ze(d)===ze(a);if(y){if(o=d.key===a.key,((C=d.segments)==null?void 0:C[0].segmentType)==="1"){const N=d.segments.map(j=>l(j)).join(""),E=a.segments.map(j=>l(j)).join(""),O=!["RR","HK"].includes(((x=d.segments)==null?void 0:x[0].actionCode)??"");if(r=d.segments[0].marriedSegmentNumber,r||N!==E||O)return d.disabled=!0,!1}d.segments.forEach((N,E)=>N.cabins=a.segments[E].cabins)}return y});return r&&e.forEach(d=>{d.segments.some(h=>r===h.marriedSegmentNumber)&&(d.disabled=!0)}),{isSame:!!t,delFlag:!o}},bu=(e,a)=>{let r="";const o=e.segments.some(t=>t.marriedSegmentNumber?(r=t.marriedSegmentNumber,!0):!1),l=[];return o&&a.forEach(t=>{t.segments.forEach(d=>{d.marriedSegmentNumber===r&&l.push(d)})}),l},_u=e=>{const a=e.length-1;return{destName:e[a].arrivalAirportCN,originName:e[0].departureAirportCN,origin:e[0].departureAirportCode,destination:e[a].arrivalAirportCode,departureDate:B(e[0].departureDate).format("YYYY-MM-DD"),departureDateTime:"",departureDateTimes:"",flightNumber:"",onlyDirectFlight:!1,airlines:"",onlyCompany:"",seamlessOrDa:"",unsharedFlight:!1,carrierFlight:!1,lowestPrice:!1,transitTerminal:"",transitTerminalName:""}},Cu=async(e,a,r)=>{var y,h;const o=vt(),l=o.orderInfo,t=o.activeTag;if(r===t||!((h=(y=l.get(t))==null?void 0:y.specialContent)!=null&&h.includes(e("app.basic.occupy"))))return!1;const d=a("div",{class:"seizeseat-info-tip-warning-text"},[a("div",e("app.pnrManagement.flight.placeholderWarningTip1")),a("div",e("app.pnrManagement.flight.placeholderWarningTip2"))]);return await ys.confirm(a("div",{class:"seizeseat-info-tip-box seizeseat-info-tip-warning"},d),{icon:a("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"flight-info-msg-placeholder flight-info-msg-suffixes seizeseat-info-tip-warning crs-btn-ui",confirmButtonText:e("app.pnrManagement.flight.clearPlaceholderConfirm"),cancelButtonText:e("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{et(Ve.UPDATE_FLIGHT_LIST,Ve.CLEAR_FLIGHT_PLACEHOLDER)}),!0},Au=e=>e.flight.map(a=>{const r=a.segments.map(l=>{var d;const t={...l,flightDistance:"",connectLevel:"",asr:"",stopCity:"",tktNum:((d=e.groupDto)==null?void 0:d.totalSeats)??`${(e.passengers??[]).length}`};return t.cabins=l.cabins.map(y=>({...y,state:""})),t.airlines={...l.airlines,planeType:"",alliance:"",airService:[{code:"",description:""}]},t}),o={...a,segments:r};return o.key=ze(o),o}),Iu=e=>{const{height:a}=Kn(e),r=document.body.clientHeight,o=a.value+117;return r-o+"px"},Ir={flight:[],pnrNo:"",issueStatus:"",passengers:[],remarkArea:{remarks:[],remarkOsis:[],ckins:[],clids:[],others:[],ssrContents:[]},originalLineContents:[],historyLineContents:[],basicArea:{},pnrCanceled:!1,international:!1,key:"",rightComponentName:""},Tu=e=>{const a=window.location.href,r=a.indexOf("?");if(r===-1)return;const o=a.substring(0,r),l=a.substring(r+1);e&&(l!=null&&l.includes(e))&&window.history.replaceState({path:o},"",o),e||window.history.replaceState({path:o},"",o)},ku=(e,a)=>{const r=new URL(window.location.href),o=r.searchParams;o.has(e)||(o.append(e,a),history.pushState({},"",r.href))},xu=e=>{const a=new URL(window.location.href),r=new URLSearchParams(a.search);r.delete(e),a.search=r.toString();const o=a.toString();window.history.replaceState({path:o},"",o)},Nu=e=>{const a=new URLSearchParams(e);return{pnrNo:a.get("pnrNo"),time:a.get("time"),type:a.get("type"),rightComponentName:a.get("rightComponentName")}},Tr=hs("pnrManagement",()=>{const e=X(""),a=X(0),r=X({flightKeys:[],params:{bookAirSegs:[]}}),o=X({}),l=X(""),t=X(new Map),d=X(new Map),y=X({}),h=We(()=>t.value),v=X(0),C=X(new Map),x=X(0),F=s=>{l.value=s},N=(s,n,i)=>{if(s==null?void 0:s.passengers.every(g=>{var T;return g.specialPassengerType===((T=s==null?void 0:s.passengers)==null?void 0:T[0].specialPassengerType)})){const g=(s.passengers.filter(T=>T.specialPassengerType===n.passengerType)??[]).length;return!(n.passengerIds.length===0&&!(n.lineIndex&&n.lineIndex>0)&&g===i.passengerIds.length)}return!0},E=(s,n,i)=>{if(s==null?void 0:s.passengers.every(g=>{var T;return g.specialPassengerType===((T=s==null?void 0:s.passengers)==null?void 0:T[0].specialPassengerType)})){const g=(s.passengers.filter(T=>T.specialPassengerType===n.passengerType)??[]).length;return!(n.passengerIds.length===0&&!(n.lineIndex&&n.lineIndex>0)&&g===i.passengerIds.length)}return!0},P=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea.fareBoxes||(i.pricingArea.fareBoxes=[]),n.passengerIds.length===0?i.pricingArea.fareBoxes=(i.pricingArea.fareBoxes??[]).filter(c=>c.passengerType!=="INF"&&n.passengerType!=="INF"||c.passengerType==="INF"&&n.passengerType==="INF"?c.passengerType!==n.passengerType:c):i.pricingArea.fareBoxes=(i.pricingArea.fareBoxes??[]).filter(c=>c.passengerType!=="INF"&&n.passengerType!=="INF"||c.passengerType==="INF"&&n.passengerType==="INF"?JSON.stringify(c.passengerIds)!==JSON.stringify(n.passengerIds)&&!c.passengerIds.some(g=>n.passengerIds.includes(g))&&N(i,c,n):c),i.pricingArea.fareBoxes.push(n)},O=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea.endorsementInfos||(i.pricingArea.endorsementInfos=[]),i.pricingArea.endorsementInfos=(i.pricingArea.endorsementInfos??[]).filter(c=>c.infantInd===n.infantInd?JSON.stringify(c.passengerIds)!==JSON.stringify(n.passengerIds)&&!c.passengerIds.some(g=>n.passengerIds.includes(g)):c),n.text&&i.pricingArea.endorsementInfos.push(n)},j=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea.fareCaluations||(i.pricingArea.fareCaluations=[]),n.passengerIds.length===0?i.pricingArea.fareCaluations=(i.pricingArea.fareCaluations??[]).filter(c=>c.passengerType!=="INF"&&n.passengerType!=="INF"||c.passengerType==="INF"&&n.passengerType==="INF"?c.passengerType!==n.passengerType:c):i.pricingArea.fareCaluations=(i.pricingArea.fareCaluations??[]).filter(c=>c.passengerType!=="INF"&&n.passengerType!=="INF"||c.passengerType==="INF"&&n.passengerType==="INF"?JSON.stringify(c.passengerIds)!==JSON.stringify(n.passengerIds)&&!c.passengerIds.some(g=>n.passengerIds.includes(g))&&E(i,c,n):c),i.pricingArea.fareCaluations.push(n)},ee=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea.formOfPayments||(i.pricingArea.formOfPayments=[]),i.pricingArea.formOfPayments=(i.pricingArea.formOfPayments??[]).filter(c=>c.infantInd===n.infantInd?JSON.stringify(Number(c.passengerIds))!==JSON.stringify(Number(n.passengerIds))&&!c.passengerIds.some(g=>n.passengerIds.includes(g)):c),n.text&&i.pricingArea.formOfPayments.push(n)},L=s=>s.startsWith("TC/IN/"),U=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea.tourCodes||(i.pricingArea.tourCodes=[]),i.pricingArea.tourCodes=(i.pricingArea.tourCodes??[]).filter(c=>L(c.text)===L(n.text)?JSON.stringify(c.passengerIds)!==JSON.stringify(n.passengerIds)&&!c.passengerIds.some(g=>n.passengerIds.includes(g)):c),n.text&&i.pricingArea.tourCodes.push(n)},W=s=>s.includes("IN/"),Z=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea.operateInfos||(i.pricingArea.operateInfos=[]),i.pricingArea.operateInfos=(i.pricingArea.operateInfos??[]).filter(c=>W(c.text)===W(n.text)?JSON.stringify(c.passengerIds)!==JSON.stringify(n.passengerIds)&&!c.passengerIds.some(g=>n.passengerIds.includes(g)):c),n.text&&i.pricingArea.operateInfos.push(n)},ke=(s,n)=>{C.value.set(s,n)},xe=s=>C.value.get(s)??!1,Ue=s=>{const n=s??[];return n&&n.length===0?!1:n.filter(c=>{if(c!=null&&c.text.startsWith("FN/A/IN/"))return(c==null?void 0:c.text.substring(8,9))!=="R";if(c!=null&&c.text.startsWith("FN/A/"))return(c==null?void 0:c.text.substring(5,6))!=="R";if(c!=null&&c.text.startsWith("FN/IN/"))return(c==null?void 0:c.text.substring(6,7))!=="R";if(c!=null&&c.text.startsWith("FN/"))return(c==null?void 0:c.text.substring(3,4))!=="R"}).length===0},G=async(s,n,i,c,g,T=!1,A=!1)=>{var Q,w,$,z,V,J,K,ce,qe,_t,ns,as,rs;const q=`${At.global.t("app.basic.pnrList")}-0`;let M="";const D={type:s,tagName:n,specialContent:c,...i,flightNotNeedSort:!1};if(s==="2"){const Ye=[],Ze=[],Xe=`${At.global.t("app.basic.pnrList")}`;h.value.forEach(te=>{!vs.test(te.tagName)&&Xe!==te.tagName&&Ye.push(te.tagName)}),h.value.forEach(te=>Ze.push(te.tagName));const Te=Ye.map(te=>parseInt(te.match(/\d+/)[0]));Te.sort((te,Qe)=>te-Qe);const Be=[];for(let te=1;te<Te.length;te++)if(Te[te]-Te[te-1]>1)for(let Qe=Te[te-1]+1;Qe<Te[te];Qe++)Be.push(Qe);const Ct=De((Q=Te??[])==null?void 0:Q[0])-1;if(Ct)for(let te=1;te<=Ct;te++)Be.push(te);if(Be.sort((te,Qe)=>te-Qe),Be.length>0)x.value=(w=Be??[])==null?void 0:w[0];else if(Ze.length===1&&Ze.includes(Xe)||Ze.length===0||Ye.length===0)x.value=1;else{const Qe=(Te==null?void 0:Te.pop())??0;x.value=Te.length>0?Qe+1:x.value+1}D.tagName=`${D.tagName} ${x.value}`,M=bt(),g!=="NEW"&&(v.value+=1),D.rightComponentName="AddSegment"}if(s==="0"&&(M=q,D.sortId=0),(s==="1"||s==="3")&&(M=n.toUpperCase(),g!=="NEW"&&g!=="REFRESH"&&(v.value+=1),s==="1")){const{basicArea:Ye,basicArea:{contact:Ze,cts:Xe},remarkArea:Te}=D;Ye.contact=Ze.map(Re=>({...Re,id:bt()}))??[],Object.keys(Te).forEach(Re=>{Te[Re]=(Te[Re]??{}).map(te=>({...te,id:bt()}))});const Be=[];if(Xe==null||Xe.forEach(Re=>{if(Re!=null&&Re.ct){const te=Re;te.id=bt(),Be.push(te)}if(typeof Re=="string"){const te={ct:Re,id:bt()};Be.push(te)}}),Ye.cts=Be,D.rightComponentName="PnrOriginalText",(($=t.value.get(M))==null?void 0:$.isVoluntaryRescheduling)??!1){const Re=De((z=t.value.get(M))==null?void 0:z.rebook),te=De((V=t.value.get(M))==null?void 0:V.isVoluntaryRescheduling);D.rebook=Re,D.isVoluntaryRescheduling=te,D.fareUUid=bt()}else D.isVoluntaryRescheduling=!1;D.pricingArea.saveSaleOrChange=Ue((J=D==null?void 0:D.pricingArea)==null?void 0:J.fareBoxes)?"CHANGE":"SALE"}if(s==="4"||s==="5"){s==="4"&&(M=`${At.global.t("app.basic.change")}`+n.toUpperCase(),D.rightComponentName="PnrOriginalText"),s==="5"&&(M=`${At.global.t("app.basic.involuntary")}`+n.toUpperCase()),v.value+=1;const Ye=((K=t.value.get(M))==null?void 0:K.newChange.isNewVoluntaryRescheduling)??!1,Ze=((ce=t.value.get(M))==null?void 0:ce.newChange.isNewInVoluntaryRescheduling)??!1,Xe=((qe=t.value.get(M))==null?void 0:qe.newChange.currentStep)??1;if(Ye){const Te=De((_t=t.value.get(M))==null?void 0:_t.rebook),Be=De((ns=t.value.get(M))==null?void 0:ns.newChange.isNewVoluntaryRescheduling),Ct=De((as=t.value.get(M))==null?void 0:as.newChange.isNewInVoluntaryRescheduling),Re=De((rs=t.value.get(M))==null?void 0:rs.newChange.currentStep);D.rebook={...Te,passenger:D.passengers},D.newChange={isNewVoluntaryRescheduling:Be,isNewInVoluntaryRescheduling:Ct,currentStep:Re}}else if(D.newChange={isNewVoluntaryRescheduling:Ye,isNewInVoluntaryRescheduling:Ze,currentStep:Xe},!D.rebook&&s==="4"){D.rebook={fare:{},manualFare:{},passenger:[],flight:[],isInternational:!1,checkedFlight:[],fareChangeType:"AUTO"};const{basicArea:Te,passengers:Be}=D;D.rebook.agentInformation=Te,D.rebook.passenger=Be}}if(D.key=M,s!=="0"&&g!=="NEW"&&(D.sortId=v.value),e.value=M,t.value.set(M,D),d.value.set(M,De(D)),g==="CHANGE"&&es(M,g==="CHANGE",T,A),g==="REFRESH"&&D.rebook){D.rebook.passenger=D.passengers,et(Ve.UPDATE_FLIGHT_LIST,Ve.RT);return}et(Ve.UPDATE_FLIGHT_LIST,"")},p=s=>{(t.value.get(s)??{}).pricingArea.isDelete=!0},oe=(s,n)=>{(t.value.get(s)??{}).passengers.push(n)},ge=s=>(t.value.get(s)??{}).manualFnFcCaches,I=(s,n)=>{const i=t.value.get(s);i.manualFnFcCaches,i.manualFnFcCaches=n,t.value.set(s,i)},_=s=>(t.value.get(s)??{}).manualFnFcCachesNew,H=(s,n)=>{const i=t.value.get(s);i.manualFnFcCachesNew,i.manualFnFcCachesNew=n,t.value.set(s,i)},S=s=>(t.value.get(s)??{}).manualFnFcInfCaches,se=(s,n)=>{const i=t.value.get(s);i.manualFnFcInfCaches,i.manualFnFcInfCaches=n,t.value.set(s,i)},ne=s=>(t.value.get(s)??{}).fareType,ie=(s,n)=>{const i=t.value.get(s);i.fareType=n},de=s=>(t.value.get(s)??{}).fareUUid,Ee=(s,n,i)=>{const c=t.value.get(s);switch(n){case"fp":c.pricingArea.formOfPayments=i;break;case"ei":c.pricingArea.endorsementInfos=i;break;case"tc":c.pricingArea.tourCodes=i;break;case"oi":c.pricingArea.operateInfos=i;break;case"fc":c.pricingArea.fareCaluations=i;break;case"fn":c.pricingArea.fareBoxes=i;break}},Pe=s=>(t.value.get(s)??{}).miscItem,Le=(s,n)=>{const i=t.value.get(s);i.miscItem=n},Me=(s,n)=>{(t.value.get(s)??{}).passengers.splice(n,1)},Se=(s,n)=>{let i=[];n.forEach(c=>{if(c.passengerType==="ACCOMPANY_INF"){const g=[];(t.value.get(s)??{}).passengers.forEach(T=>{T.infantDetail&&T.infantDetail.fullName===c.name&&(T.infantDetail=null),g.push(T),i=Array.from(new Set(g))})}else i=(t.value.get(s)??{}).passengers.filter(g=>!n.some(T=>(T==null?void 0:T.name)===(g==null?void 0:g.fullName)));(t.value.get(s)??{}).passengers=i})},Oe=(s,n)=>{var g;const i=De(n);i.key||(i.key=ze(i));const c=t.value.get(s);c.flightNotNeedSort=!1,c.type==="2"&&Ge(s,{}),ps(c)?((g=c.rebook)!=null&&g.flight||Object.assign(c,{rebook:{flight:[],checkedFlight:[]}}),(n.openFlag||!Yt(c.rebook.flight,n).isSame)&&c.rebook.flight.push(i)):(n.openFlag||!Yt(c.flight,n).isSame)&&c.flight.push(i),t.value.set(s,c)},je=(s,n,i)=>{t.value.set(s,n),t.value.get(s).type==="2"&&Ge(s,{}),et(Ve.UPDATE_FLIGHT_LIST,i??"")},at=(s,n)=>{const i=t.value.get(s);i.flight=n,i.flightNotNeedSort=!1,et(Ve.UPDATE_FLIGHT_LIST,"")},rt=(s,n)=>{const i=t.value.get(s),c=n.map(A=>A.key??"");let g=[];const T=[...new Set(n.flatMap(A=>A.segments.map(q=>q.marriedSegmentNumber)).filter(A=>A))];T.length&&(g=i.flight.filter(A=>A.key&&!c.includes(A.key)?A.segments.some(q=>T.includes(q.marriedSegmentNumber)):!1)),[...n,...g].map(A=>{const q=A.segments.map(D=>({...D,actionCodes:"",marriedSegmentNumber:""})),M={...A,segments:q,disabled:!1};fe(s,M)}),et(Ve.UPDATE_FLIGHT_LIST,"")},ot=(s,n,i)=>{const c=t.value.get(s);if(!c.rebook)return;const g=[...i].filter(T=>T.isChecked).map((T,A)=>({...T,order:A}))??[];c.rebook.checkedFlight=g,c.rebook.isInternational=Gt(g,n),t.value.set(s,c),he(s),Je(s)},it=(s,n)=>{const i=t.value.get(s);i.flight=De(n)},lt=(s,n,i)=>{const c=t.value.get(s),g=[...i].filter(T=>!T.disabled).map((T,A)=>({...T,order:A}))??[];c.rebook.isInternational=Gt(g,n),c.rebook.checkedFlight=g,t.value.set(s,c)},ct=(s,n,i)=>{(t.value.get(s)??{}).passengers[i]=n},dt=s=>{t.value.delete(s)},ut=(s,n)=>{var c,g;const i=t.value.get(s);i.flightNotNeedSort=!1,ps(i)?i.rebook.flight=((g=(c=i.rebook)==null?void 0:c.flight)==null?void 0:g.filter(T=>T.key!==n))??[]:i.flight=i.flight.filter(T=>T.key!==n),i.type==="2"&&Ge(s,{}),t.value.set(s,i)},gt=s=>{const n=t.value.get(s);n.rebook.flight=[],n.rebook.checkedFlight=[],n.rebook.isInternational=!1,t.value.set(s,n),et(Ve.UPDATE_FLIGHT_LIST,"")},Je=s=>{const n=t.value.get(s);n.rebook.fare={changeFareDom:[],changeFareInter:[],payType:""},t.value.set(s,n)},pt=s=>{const n=t.value.get(s);n.pricingArea={},n.rebookFare={changeFareDom:[],changeFareInter:[],payType:""},t.value.set(s,n)},ft=s=>{const n=t.value.get(s)??{};n.pricingArea.fareBoxes||(n.pricingArea.fareBoxes=[]),n.pricingArea.fareCaluations||(n.pricingArea.fareCaluations=[]),n.pricingArea.formOfPayments||(n.pricingArea.formOfPayments=[]),n.pricingArea.endorsementInfos||(n.pricingArea.endorsementInfos=[]),n.pricingArea.tourCodes||(n.pricingArea.tourCodes=[]),n.pricingArea.fareBoxes=[],n.pricingArea.fareCaluations=[],n.pricingArea.endorsementInfos=n.pricingArea.endorsementInfos.filter(i=>((i==null?void 0:i.fareType)??"")!=="A"),n.pricingArea.formOfPayments=n.pricingArea.formOfPayments.filter(i=>((i==null?void 0:i.fareType)??"")!=="A"),n.pricingArea.tourCodes=n.pricingArea.tourCodes.filter(i=>((i==null?void 0:i.fareType)??"")!=="A"),n.rebookFare={changeFareDom:[],changeFareInter:[],payType:""},t.value.set(s,n)},ht=(s,n)=>{const i=t.value.get(s);i.rebook.fare=n,t.value.set(s,i)},yt=s=>{e.value=s;const n=t.value.get(s);n!=null&&n.type||G("0",At.global.t("app.basic.pnrList"),Ir),et(Ve.UPDATE_FLIGHT_LIST,"setActiveTag")},R=()=>{a.value=0},$e=s=>{x.value=s},me=()=>{v.value=0,a.value=0,t.value.clear(),d.value.clear()},be=s=>{const n=t.value.get(e.value);n.rightComponentName=s},fe=(s,n)=>{var g,T;t.value.get(s).type==="2"&&Ge(s,{}),t.value.get(s).flightNotNeedSort=!1;const i=Yt(t.value.get(s).flight,n);if(i.isSame){if(i.delFlag){if(((g=n.segments)==null?void 0:g[0].segmentType)==="1"){t.value.get(s).flight=gs(t.value.get(s).flight,n);return}t.value.get(s).flight=t.value.get(s).flight.filter(A=>A.key!==n.key)}return}let c={};(t.value.get(s).flight??[]).find(A=>{var M,D;const q=A.key===n.key;if(q){const w=!["RR","HK"].includes(((M=A.segments)==null?void 0:M[0].actionCode)??""),$=A.segments.map(J=>{var K;return(K=J.cabins)!=null&&K.length?J.cabins[0].cabinName.toUpperCase():""}),z=n.segments.map(J=>{var K;return(K=J.cabins)!=null&&K.length?J.cabins[0].cabinName.toUpperCase():""});if(`${ze(n)}${z}${n.segments[0].marriedSegmentNumber}`!=`${ze(A)}${$}${A.segments[0].marriedSegmentNumber}`||w){if(((D=A.segments)==null?void 0:D[0].segmentType)==="1"){c=De(A);return}A.segments=n.segments}}return q}),(T=c==null?void 0:c.segments)!=null&&T.length&&(t.value.get(s).flight=gs(t.value.get(s).flight,c),c.segments.forEach((A,q)=>{Object.assign(A,n.segments[q]),A.segmentType="0",A.actionCode="",A.marriedSegmentNumber=""}),c.key=ze(c,!0),c.disabled=!1,Oe(s,c))},_e=(s,n)=>{var i,c,g,T,A,q,M,D,Q,w,$,z,V;(c=(i=t.value.get(s)??{})==null?void 0:i.remarkArea)==null||c.others.push(...n.others),(T=(g=t.value.get(s)??{})==null?void 0:g.remarkArea)==null||T.ckins.push(...n.ckins),(q=(A=t.value.get(s)??{})==null?void 0:A.remarkArea)==null||q.clids.push(...n.clids),(D=(M=t.value.get(s)??{})==null?void 0:M.remarkArea)==null||D.remarkOsis.push(...n.remarkOsis),(w=(Q=t.value.get(s)??{})==null?void 0:Q.remarkArea)==null||w.remarks.push(...n.remarks),(V=(z=($=t.value.get(s)??{})==null?void 0:$.remarkArea)==null?void 0:z.ssrContents)==null||V.push(...n.ssrContents)},Ce=(s,n)=>{var c,g,T,A,q;((T=(g=(c=t.value.get(s)??{})==null?void 0:c.remarkArea)==null?void 0:g.remarks)==null?void 0:T.some(M=>M.text===n.remarks[0].text))||(q=(A=t.value.get(s)??{})==null?void 0:A.remarkArea)==null||q.remarks.push(...n.remarks)},Ae=(s,n)=>{var i,c;(c=(i=t.value.get(s)??{})==null?void 0:i.remarkArea)==null||c.remarks.push(...n)},Ie=(s,n,i)=>{if(s){const c={fare:{},passenger:{},flight:[],isInternational:!1,fareChangeType:i};t.value.get(e.value).rebook=c}t.value.get(e.value).isVoluntaryRescheduling=s,t.value.get(e.value).isInVoluntaryRescheduling=n},he=s=>{t.value.get(s).rightComponentName="PnrOriginalText"},Ge=(s,n)=>{t.value.get(s).selectFare=n},Dt=(s,n)=>{var i;((i=t.value.get(s).selectFare)==null?void 0:i.fareType)!==void 0?(t.value.get(s).selectFare.passengerFares=(t.value.get(s).selectFare.passengerFares??[]).filter(c=>c.code!=="INF"&&n.code!=="INF"||c.code==="INF"&&n.code==="INF"?JSON.stringify(c.pasgId)!==JSON.stringify(n.pasgId)&&!c.pasgId.some(g=>n.pasgId.includes(g)):c),t.value.get(s).selectFare.passengerFares.push(n)):(t.value.get(s).selectFare={},t.value.get(s).selectFare.ticketType=null,t.value.get(s).selectFare.fareType="A",t.value.get(s).selectFare.passengerFares=[n])},Ft=(s,n)=>{t.value.get(s).selectFare.passengerFares=n},wt=(s,n)=>{t.value.get(s).pricingArea.fareBoxes=n},Rt=(s,n)=>{t.value.get(s).pricingArea.isSingleDel=n},Et=(s,n)=>{t.value.get(s).remarkArea.remarks=n},St=(s,n)=>{t.value.get(s).pricingArea.tourCodes=n},$t=(s,n)=>{t.value.get(s).pricingArea.fareCaluations=n},Pt=(s,n)=>{t.value.get(s).groupDto=n},Lt=(s,n)=>{const i=t.value.get(s)??{};i.psgType||Object.assign(i,{psgType:"0"}),t.value.get(s).psgType=n},Mt=(s,n)=>{t.value.get(s).groupPassengerType=n},Ot=(s,n)=>{t.value.get(s).discountCode=n},Bt=(s,n)=>{t.value.get(s).queryParam=n},Ne=(s,n)=>{t.value.get(s).queryDomParam=n},jt=(s,n)=>{t.value.get(s).nameOfType=n},Ms=(s,n)=>{t.value.get(s).payMethod=n},Os=(s,n)=>{t.value.get(s).fareParam=n},Bs=s=>{const n={airlines:"",keyCustomerCode:"",fareType:"",farebasic:"",classType:"Both",placeOfSale:"",currencyCode:"",exclusiveNegotiated:!1,minimumPrice:!1,allBrands:!1,isGovernment:!1};t.value.get(s).fareParam=n},Hs=(s,n)=>{t.value.get(s).fareParamByInter=n},Us=(s,n)=>{t.value.get(s).saveFareParamByInter=n},qs=(s,n)=>{const i=t.value.get(s)??{};i.rebook||Object.assign(i,{rebook:{flight:[]}}),i.rebook.passenger=n,t.value.set(s,i)},Vs=(s,n)=>{t.value.get(s).rebook.fareChangeType="manual",t.value.get(s).rebook.manualFare=n},js=s=>{var T;const n=t.value.get(s)??{},i=n.flight??[],c=((T=n.rebook)==null?void 0:T.flight)??[];return[...i,...c].filter(A=>A.isChecked)},Gs=s=>{var T;const n=t.value.get(s)??{},i=n.flight??[],c=((T=n.rebook)==null?void 0:T.flight)??[];return[...i,...c]},Ys=s=>{const n=t.value.get(s)??{};n.rebook.passenger=[],t.value.set(s,n)},es=(s,n,i,c)=>{t.value.get(s)&&(t.value.get(s).isNewChange=n,t.value.get(s).newChange={isNewVoluntaryRescheduling:i,isNewInVoluntaryRescheduling:c,currentStep:1})},Qs=s=>(t.value.get(s)??{}).newChange,Ws=s=>(t.value.get(s)??{}).isNewChange,zs=(s,n)=>{t.value.get(s)&&(t.value.get(s).newChange.currentStep=n)},Js=s=>{t.value.get(s)&&t.value.get(s).newChange.currentStep>1&&(t.value.get(s).newChange.currentStep-=1)},Zs=s=>(t.value.get(s)??{}).newChange.currentStep??1,Xs=(s,n)=>{t.value.get(s)&&(t.value.get(s).newInvoluntaryInfo=n)},Ks=(s,n)=>{t.value.get(s)&&(t.value.get(s).paymentMethod=n)},en=s=>(t.value.get(s)??{}).paymentMethod,tn=s=>(t.value.get(s)??{}).newInvoluntaryInfo,sn=(s,n,i,c,g)=>{const T=t.value.get(s)??{};if(T.fareInfoByManual||(T.fareInfoByManual={}),n==="FP"){const A=(T.fareInfoByManual.CC??[]).length>0,q=(i??[]).find(D=>{var Q;return(Q=D.text)==null?void 0:Q.includes("IN/CC/")}),M=((i??[]).filter(D=>{var Q;return!((Q=D.text)!=null&&Q.includes("IN/CC/"))})??[]).find(D=>{var Q,w;return((Q=D.text)==null?void 0:Q.includes("CC/"))&&((w=D.passengerIds)==null?void 0:w.length)<=0});A&&q&&(T.fareInfoByManual.CC=(T.fareInfoByManual.CC??[]).filter(D=>{var Q;return!((Q=D.text)!=null&&Q.includes("IN/CC/"))})),A&&M&&(T.fareInfoByManual.CC=(T.fareInfoByManual.CC??[]).filter(D=>{var Q;return(Q=D.text)==null?void 0:Q.includes("IN/CC/")}))}c?(i[0].lineIndex=g,T.fareInfoByManual[n][g-1]=i[0]):T.fareInfoByManual[n]=i},nn=(s,n,i)=>{const c=t.value.get(s)??{};c.fareInfoByManual||(c.fareInfoByManual={}),(c.fareInfoByManual.FP??[]).find(T=>{var A,q;return((A=T.text)==null?void 0:A.includes("CC/"))&&((q=T.passengerIds)==null?void 0:q.length)<=0})&&(c.fareInfoByManual.FP=(c.fareInfoByManual.FP??[]).filter(T=>{var A,q;return!((A=T.text)!=null&&A.includes("CC/")&&((q=T.passengerIds)==null?void 0:q.length)<=0)})),c.fareInfoByManual[n]=i},an=s=>(t.value.get(s)??{}).fareInfoByManual,rn=(s,n)=>{t.value.get(s)&&(t.value.get(s).fareInfoByManual=n)},on=(s,n,i,c)=>{t.value.get(s).previewTicketId=n,t.value.get(s).previewTicketType=i,t.value.get(s).previewTicketShow=c},ln=(s,n)=>{t.value.get(s).connectTKNE=n},cn=(s,n,i)=>{const c=t.value.get(s),g=i.filter(T=>!T.disabled);c.international=Gt(g,n)},dn=(s,n)=>{const i=t.value.get(s);i.commandsSeatSuccess=n},un=(s,n)=>{const i=t.value.get(s);i.commandsSeatCheckSuccess=n},gn=(s,n)=>{const i=t.value.get(s);i.commandsAVCheckSuccess=n},pn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualRmkList||(t.value.get(s).manualRmkList=[]),t.value.get(s).manualRmkList=n)},fn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualEiList||(t.value.get(s).manualEiList=[]),t.value.get(s).manualEiList.push(n))},hn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualSvcList||(t.value.get(s).manualSvcList=[]),t.value.get(s).manualSvcList.push(n))},yn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualEiList||(t.value.get(s).manualEiList=[]),t.value.get(s).manualEiList=n)},vn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualTcList||(t.value.get(s).manualTcList=[]),t.value.get(s).manualTcList=n)},mn=s=>{t.value.get(s)&&(t.value.get(s).manualEiList||(t.value.get(s).manualEiList=[]),t.value.get(s).manualEiList.length=0)},bn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualFpList||(t.value.get(s).manualFpList=[]),t.value.get(s).manualFpList=n)},_n=s=>{t.value.get(s)&&(t.value.get(s).manualFpList||(t.value.get(s).manualFpList=[]),t.value.get(s).manualFpList.length=0)},Cn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualTcList||(t.value.get(s).manualTcList=[]),t.value.get(s).manualTcList.push(n))},An=s=>{t.value.get(s)&&(t.value.get(s).manualTcList||(t.value.get(s).manualTcList=[]),t.value.get(s).manualTcList.length=0)},In=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualTcList||(t.value.get(s).manualTcList=[]),t.value.get(s).manualTcList.splice(n,1))},Tn=(s,n)=>{t.value.get(s)&&(t.value.get(s).manualEiList||(t.value.get(s).manualEiList=[]),t.value.get(s).manualEiList.splice(n,1))},mt=s=>{const n=t.value.get(s)??{};return n.pricingArea||(n.pricingArea={}),n},kn=(s,n,i,c)=>{var T,A,q,M,D,Q;const g=mt(s);if(i)if(n!=null&&n.texts&&(n!=null&&n.texts[0])){const w=n.texts[0],z={passengerIds:((T=n.psgInfo[0])==null?void 0:T.psgInfo.filter(V=>V.index!=="").map(V=>{var J;return Number((J=V.index)==null?void 0:J.split("P")[1])}))??[],text:`EI/${w}`,infantInd:w.includes("/IN/"),lineIndex:c<500?Math.floor(Math.random()*9500)+500:c};g.pricingArea.endorsementInfos.forEach((V,J)=>{c===V.lineIndex&&(g.pricingArea.endorsementInfos[J]=z)})}else g.pricingArea.endorsementInfos.forEach((w,$)=>{c===w.lineIndex&&g.pricingArea.endorsementInfos.splice($,1)});else{const w=(A=g.pricingArea.endorsementInfos)==null?void 0:A.filter($=>($.fareType??"")==="A");g.pricingArea.endorsementInfos=[],(w==null?void 0:w.length)>0&&g.pricingArea.endorsementInfos.push(...w);for(let $=0;$<n.texts.length;$++){const z=n.texts[$];let V=[];(M=(q=n.psgInfo[$])==null?void 0:q.psgInfo)!=null&&M.some(K=>K.index==="")||(V=(Q=(D=n.psgInfo[$])==null?void 0:D.psgInfo)==null?void 0:Q.map(K=>{var ce;return Number((ce=K.index)==null?void 0:ce.split("P")[1])}));const J={passengerIds:V,text:`EI/${z}`,infantInd:z.includes("/IN/"),lineIndex:Math.floor(Math.random()*9500)+500};g.pricingArea.endorsementInfos.push(J)}}},xn=(s,n,i,c)=>{var T,A,q,M,D,Q;const g=mt(s);if(i)if(n!=null&&n.texts&&(n!=null&&n.texts[0])){const w=n.texts[0],z={passengerIds:((T=n.psgInfo[0])==null?void 0:T.psgInfo.filter(V=>V.index!=="").map(V=>{var J;return Number((J=V.index)==null?void 0:J.split("P")[1])}))??[],text:w,infantInd:w.includes("/IN/"),lineIndex:c<500?Math.floor(Math.random()*9500)+500:c};g.pricingArea.svcInfos.forEach((V,J)=>{c===V.lineIndex&&(g.pricingArea.svcInfos[J]=z)})}else g.pricingArea.svcInfos.forEach((w,$)=>{c===w.lineIndex&&g.pricingArea.svcInfos.splice($,1)});else{const w=(A=g.pricingArea.svcInfos)==null?void 0:A.filter($=>($.fareType??"")==="A");g.pricingArea.svcInfos=[],(w==null?void 0:w.length)>0&&g.pricingArea.svcInfos.push(...w);for(let $=0;$<n.texts.length;$++){const z=n.texts[$];let V=[];(M=(q=n.psgInfo[$])==null?void 0:q.psgInfo)!=null&&M.some(K=>K.index==="")||(V=(Q=(D=n.psgInfo[$])==null?void 0:D.psgInfo)==null?void 0:Q.map(K=>{var ce;return Number((ce=K.index)==null?void 0:ce.split("P")[1])}));const J={passengerIds:V,text:z,infantInd:z.includes("/IN/"),lineIndex:Math.floor(Math.random()*9500)+500};g.pricingArea.svcInfos.push(J)}}},Nn=(s,n,i,c)=>{var T,A,q,M,D,Q;const g=mt(s);if(i)if(n!=null&&n.texts&&(n!=null&&n.texts[0])){const w=n.texts[0],z={passengerIds:(T=n.psgInfo[0])==null?void 0:T.psgInfo.filter(V=>V.index!=="").map(V=>{var J;return Number((J=V.index)==null?void 0:J.split("P")[1])}),text:`FP/${w}`,infantInd:w.includes("/IN/"),lineIndex:c<500?Math.floor(Math.random()*9500)+500:c};g.pricingArea.formOfPayments.forEach((V,J)=>{c===V.lineIndex&&(g.pricingArea.formOfPayments[J]=z)})}else g.pricingArea.formOfPayments.forEach((w,$)=>{c===w.lineIndex&&g.pricingArea.formOfPayments.splice($,1)});else{const w=(A=g.pricingArea.formOfPayments)==null?void 0:A.filter($=>($.fareType??"")==="A");g.pricingArea.formOfPayments=[],(w==null?void 0:w.length)>0&&g.pricingArea.formOfPayments.push(...w);for(let $=0;$<n.texts.length;$++){const z=n.texts[$];let V=[];(M=(q=n.psgInfo[$])==null?void 0:q.psgInfo)!=null&&M.some(K=>K.index==="")||(V=(Q=(D=n.psgInfo[$])==null?void 0:D.psgInfo)==null?void 0:Q.map(K=>{var ce;return Number((ce=K.index)==null?void 0:ce.split("P")[1])}));const J={passengerIds:V,text:`FP/${z}`,infantInd:z.includes("/IN/"),lineIndex:Math.floor(Math.random()*9500)+500};g.pricingArea.formOfPayments.push(J)}}},Dn=(s,n,i,c)=>{var T,A,q,M,D,Q;const g=mt(s);if(i)(T=g.pricingArea.CreditCardPayments)==null||T.forEach((w,$)=>{c===w.lineIndex&&g.pricingArea.CreditCardPayments.splice($,1)});else{const w=(A=g.pricingArea.CreditCardPayments)==null?void 0:A.filter($=>($.fareType??"")==="A");g.pricingArea.CreditCardPayments=[],(w==null?void 0:w.length)>0&&g.pricingArea.CreditCardPayments.push(...w);for(let $=0;$<n.texts.length;$++){const z=n.texts[$];let V=[];(M=(q=n.psgInfo[$])==null?void 0:q.psgInfo)!=null&&M.some(K=>K.index==="")||(V=(Q=(D=n.psgInfo[$])==null?void 0:D.psgInfo)==null?void 0:Q.map(K=>Number(K.index)));const J={passengerIds:V,text:z,infantInd:z.includes("/IN/"),lineIndex:Math.floor(Math.random()*9500)+500,fpCredit:n.fpCredit};g.pricingArea.CreditCardPayments.push(J)}}},Fn=(s,n,i,c)=>{var T,A,q,M,D,Q,w,$;const g=mt(s);if(i)if(n!=null&&n.texts&&(n!=null&&n.texts[0])){const z=n.texts[0],V=(T=n.psgInfo[0])==null?void 0:T.psgInfo.filter(K=>K.index!=="").map(K=>{var ce;return Number((ce=K.index)==null?void 0:ce.split("P")[1])}),J={text:`TC/${z}`,infantInd:z.includes("/IN/"),fareType:"M",passengerIds:V??[],lineIndex:c<500?Math.floor(Math.random()*9500)+500:c};(A=g.pricingArea.tourCodes)==null||A.forEach((K,ce)=>{c===K.lineIndex&&(g.pricingArea.tourCodes[ce]=J)})}else(q=g.pricingArea.tourCodes)==null||q.forEach((z,V)=>{c===z.lineIndex&&g.pricingArea.tourCodes.splice(V,1)});else{const z=(M=g.pricingArea.tourCodes)==null?void 0:M.filter(V=>(V.fareType??"")==="A");g.pricingArea.tourCodes=[],(z==null?void 0:z.length)>0&&g.pricingArea.tourCodes.push(...z);for(let V=0;V<n.texts.length;V++){const J=n.texts[V];let K=[];(Q=(D=n.psgInfo[V])==null?void 0:D.psgInfo)!=null&&Q.some(qe=>qe.index==="")||(K=($=(w=n.psgInfo[V])==null?void 0:w.psgInfo)==null?void 0:$.map(qe=>{var _t;return Number((_t=qe.index)==null?void 0:_t.split("P")[1])}));const ce={text:`TC/${J}`,infantInd:J.includes("IN/"),fareType:"M",passengerIds:K,lineIndex:Math.floor(Math.random()*9500)+500};g.pricingArea.tourCodes.push(ce)}}},wn=(s,n,i,c)=>{var T,A;const g=mt(s);if(i)if(n&&n.oiInfos){const q=n.oiInfos,M=q[0].text,D=[];q[0].passengerId&&D.push(Number(q[0].passengerId));const Q={passengerIds:D,text:`${M.startsWith("OI/")?M:`OI/${M}`}`,passengerName:q[0].passengerName,lineIndex:c<500?Math.floor(Math.random()*9500)+500:c};g.pricingArea.operateInfos.forEach((w,$)=>{c===w.lineIndex&&(g.pricingArea.operateInfos[$]=Q)})}else g.pricingArea.operateInfos.forEach((q,M)=>{c===q.lineIndex&&g.pricingArea.operateInfos.splice(M,1)});else{const q=n==null?void 0:n.oiInfos,M=(T=g.pricingArea.operateInfos)==null?void 0:T.filter(D=>(D.fareType??"")==="A");(A=g.pricingArea.operateInfos)==null||A.forEach((D,Q)=>{q.filter(w=>w.lineIndex===D.lineIndex)&&g.pricingArea.operateInfos.splice(Q,1)}),g.pricingArea.operateInfos=[],(M==null?void 0:M.length)>0&&g.pricingArea.operateInfos.push(...M),q.forEach(D=>{const Q=[],w=D.text;D.passengerId&&Q.push(Number(D.passengerId));const $={passengerIds:Q,text:`${w.startsWith("OI/")?w:`OI/${w}`}`,passengerName:D.passengerName,lineIndex:Math.floor(Math.random()*9500)+500};g.pricingArea.operateInfos.push($)})}},Rn=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea||(i.pricingArea={}),i.pricingArea.fareBoxes=n},En=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea||(i.pricingArea={}),i.pricingArea.fareCaluations=n},Sn=(s,n)=>{const i=t.value.get(s)??{};i.pricingArea||(i.pricingArea={}),i.pricingArea.endorsementInfos=n},$n=s=>{var i;const n=t.value.get(s)??{};if(!n.selectFare)return!1;(((i=n.selectFare)==null?void 0:i.passengerFares)??[]).forEach(c=>{c.isNewFare=!1})},Pn=s=>{const n=t.value.get(s)??{};return n.pricingArea?(["fareBoxes","fareCaluations","endorsementInfos","formOfPayments","IOperateInfo","TourCodes"].forEach(c=>{const g=n.pricingArea[c];Array.isArray(g)&&g.length>0&&g.forEach(T=>{T.isNewFare=!1})}),!0):!1},ts=(s,n,i=!1)=>{var c,g;if(t.value.get(s).type==="2"){const T=(((c=(t.value.get(s)??{}).selectFare)==null?void 0:c.passengerFares)??[]).filter(A=>(i?A.code==="INF":A.code!=="INF")&&A.pasgId.length===0&&((A==null?void 0:A.isNewFare)??!1)).length>0;(t.value.get(s)??{}).selectFare.passengerFares=(((g=(t.value.get(s)??{}).selectFare)==null?void 0:g.passengerFares)??[]).filter(A=>(i?A.code!=="INF":A.code==="INF")||(i?A.code==="INF":A.code!=="INF")&&(T?A.pasgId.length===0:A.pasgId.length>0))}else{const T=((t.value.get(s)??{}).pricingArea[n]??[]).filter(A=>(i?A.passengerType==="INF":A.passengerType!=="INF")&&A.passengerIds.length===0&&((A==null?void 0:A.isNewFare)??!1)).length>0;(t.value.get(s)??{}).pricingArea[n]=((t.value.get(s)??{}).pricingArea[n]??[]).filter(A=>(i?A.passengerType!=="INF":A.passengerType==="INF")||(i?A.passengerType==="INF":A.passengerType!=="INF")&&(T?A.passengerIds.length===0:A.passengerIds.length>0))}},Ln=(s,n,i=!1)=>{const c=((t.value.get(s)??{}).pricingArea[n]??[]).filter(g=>(i?g.infantInd:!g.infantInd)&&g.passengerIds.length===0&&((g==null?void 0:g.isNewFare)??!1)).length>0;(t.value.get(s)??{}).pricingArea[n]=((t.value.get(s)??{}).pricingArea[n]??[]).filter(g=>(i?!g.infantInd:g.infantInd)||(i?g.infantInd:!g.infantInd)&&(c?g.passengerIds.length===0:g.passengerIds.length>0))},Mn=(s,n,i=!1)=>{const c=((t.value.get(s)??{}).pricingArea[n]??[]).filter(g=>(i?L(g.text):!L(g.text))&&g.passengerIds.length===0&&((g==null?void 0:g.isNewFare)??!1)).length>0;(t.value.get(s)??{}).pricingArea[n]=((t.value.get(s)??{}).pricingArea[n]??[]).filter(g=>(i?!L(g.text):L(g.text))||(i?L(g.text):!L(g.text))&&(c?g.passengerIds.length===0:g.passengerIds.length>0))},On=(s,n,i=!1)=>{t.value.get(s).type==="2"||["fareBoxes","fareCaluations"].includes(n)?ts(s,n,i):["endorsementInfos","formOfPayments"].includes(n)?Ln(s,n,i):Mn(s,n,i)},Bn=(s,n)=>{t.value.get(s).pricingArea.fareType=n},Hn=(s,n)=>{t.value.get(s).pricingArea.saveSaleOrChange=n},Un=(s,n)=>{const i=t.value.get(s);i.rebookFare=n},qn=(s,n)=>{const i=t.value.get(s);i&&(i!=null&&i.rebookInfos?t.value.get(s).rebookInfos=n:Reflect.set(i,"rebookInfos",n))},ss=s=>{var D,Q;const n=((D=d.value.get(s))==null?void 0:D.flight)??[],i=((Q=t.value.get(s))==null?void 0:Q.flight)??[],c=i.filter(w=>w.segments[0].segmentType!=="1");if(c.length)return!0;const g=i.filter(w=>w.disabled);if(g.length)return!0;const T=Va(i,c,g),A=w=>{var K,ce,qe;const $=`${(K=w.airlines)==null?void 0:K.airCode}${((ce=w.airlines)==null?void 0:ce.flightNo)??""}`,z=((qe=w.cabins??[])==null?void 0:qe[0]).cabinName.toUpperCase(),V=w.actionCode??"",J=!B(w.departureDate).isValid()||B(w.departureDate).isBefore(new Date("2000-01-01"),"year")?"":w.departureDate;return`${$}-${z}-${V}-${J}`};return((w,$)=>{const z=w.flatMap(ce=>ce.segments[0]),V=$.flatMap(ce=>ce.segments[0]);return Ya(z,V,(ce,qe)=>A(ce)===A(qe)).length>0})(T,n)};return{setPlaceholderFlightsParams:(s,n)=>{if(n){r.value.params.bookAirSegs.forEach(i=>i.tktNum=n.toString());return}r.value=De(s)},pnrNo:l,activeTag:e,placeholderFlightsParams:r,setPlaceholderFlightsQueryForm:s=>{o.value=s},placeholderFlightsQueryForm:o,orderInfo:t,originPnrData:d,currentRebookData:y,getOrderInfo:h,addTagIndex:a,setPnrNo:F,pushOrderInfo:G,deleteOrderInfoByPnr:dt,setActiveTag:yt,setPassenger:oe,deletePassenger:Me,clearAllOrderInfo:me,updatePassenger:ct,setFlight:Oe,setCheckedFlight:ot,setPayMehtod:Ms,setFlights:je,delSeizeSeatInfoFlight:ut,setAddTagIndex:R,setOrderInfoActiveTag:be,updateFlight:fe,setRemark:_e,setChangeManualFare:Vs,setIssueExceptionsFlag:ke,getIssueExceptionsFlag:xe,setDefaultJumpHistory:he,setInitRebook:Ie,clearRebookChacheFlights:gt,getCheckedFlight:js,getChangeFlight:Gs,setSelectFare:Ge,setPushSelectFare:Dt,setRebookPassengers:qs,deleteRebookPassengers:Ys,clearRebookChacheFares:Je,setRebookChacheFares:ht,setFareParam:Os,setFareParamByInter:Hs,setSaveFareParamByInter:Us,clearPnrFares:pt,clearPnrFNFC:ft,updateFlights:at,updateCabinReplacementFlights:rt,setDefaultFareParam:Bs,setNewChange:es,getNewChange:Ws,getNewChangeValue:Qs,setAddTagLength:$e,setNewChangeStep:zs,setBackStep:Js,setRebookIsInternational:lt,setInternationalFlight:cn,getNewChangeStep:Zs,setNewInvoluntaryInfo:Xs,getNewInvoluntaryInfo:tn,setNewInvoluntaryPayment:Ks,getNewInvoluntaryPayment:en,setManualFnFcCaches:I,getManualFnFcCaches:ge,setGroupBaseInfo:Pt,setPassengerType:Lt,getFareType:ne,setFareType:ie,deleteMorePassenger:Se,getMiscItem:Pe,setMiscItem:Le,setFareInfoByManual:sn,setFareInfoByManualCCV:nn,getFareInfoByManual:an,setUpdateFareInfoByManual:rn,setPreviewTicket:on,setCommandsSeatCheckSuccess:un,setCommandsSeatSuccess:dn,setManualFnFcInfCaches:se,getManualFnFcInfCaches:S,setManualFnFcCachesNew:H,getManualFnFcCachesNew:_,setCommandsAvCheckSuccess:gn,setManualRmkCaches:pn,setManualEiCaches:fn,setManualTcCaches:Cn,setManualTCCachesList:vn,clearManualTcCaches:An,delManualTcCachesByIndex:In,clearManualEiCaches:mn,setFareDomFN:P,setFareDomEI:O,setFareDomFC:j,setFareDomFP:ee,setFareDomTC:U,setFareOI:Z,delFareEmpty:On,delManualEiCachesByIndex:Tn,setEIByPnr:kn,setFPByPnr:Nn,setCCVByPnr:Dn,setTCByPnr:Fn,setOIByPnr:wn,setAutoFN:Rn,setAutoFC:En,setAutoEI:Sn,setOldFareTag:Pn,setPendingOldFareTag:$n,setSaveFareType:Bn,setRebookFares:Un,setSaveSaleOrChange:Hn,setFareItemInfo:Ee,getFareUUid:de,setRebookInfos:qn,setOriginPnrDeleteFNFCTag:p,checkExistNotSaveDataOfFlight:ss,isExistNotSavedData:(s,n,i)=>{if(!s)return!1;const c=n,g=ss(i);return c||g},setSortFlights:it,setCurrentRebookInfo:s=>{y.value=s},setUpdatePassengerJudge:(s,n)=>{t.value.get(s).isDelPassenger=n},setPATMManualFPCaches:bn,clearManualFPCaches:_n,setPATMManualEiCaches:yn,pushCustomerCodeHistory:(s,n)=>{t.value.get(s).customerCodeHistory?(t.value.get(s).customerCodeHistory.length<5||t.value.get(s).customerCodeHistory.splice(0,1),t.value.get(s).customerCodeHistory.push(n)):t.value.get(s).customerCodeHistory=[n]},setSelectFlight:(s,n,i)=>{if(!n.length)return;const c=t.value.get(s);c.flightNotNeedSort=!1,c.flight.forEach(g=>{g.segments.forEach(T=>{c.type==="2"&&g.key&&n.includes(g.key)&&(T.selectedFlag=i)})}),c.type==="2"&&Ge(s,{}),t.value.set(s,c)},setGovernment:(s,n)=>{t.value.get(s).government=n},setGroupPassengerType:Mt,setSelectPasgFare:Ft,setHasPnrFN:wt,setHasPnrFC:$t,setHasPnrTC:St,setHasPnrRMK:Et,setDeleteFareRMK:(s,n)=>{t.value.get(s)&&(t.value.get(s).fareDeleteRemarks||(t.value.get(s).fareDeleteRemarks=[]),t.value.get(s).fareDeleteRemarks=(t.value.get(s).fareDeleteRemarks??[]).concat(n))},setRMK:Ae,setCommandsGN:(s,n,i)=>{t.value.get(s).psgType="1",t.value.get(s).groupDto.groupName=n,t.value.get(s).groupDto.totalSeats=i},setNameOfType:jt,setConnectTKNE:ln,setQueryParam:Bt,setDomQueryParam:Ne,setDisCountCode:Ot,setSingleDel:Rt,setRightGoFare:(s,n)=>{const i=t.value.get(s);i.rightGoFare=n},setGroupExpand:(s,n)=>{t.value.get(s).isGroupExpand=n},setSVCByPnr:xn,setManualSVCCaches:hn,setFareRemark:Ce}}),vt=Tr,Du=e=>Fe(`${we}/apiAvSearch/computeInterAirPrice`).post(e).json(),Fu=e=>Fe(`${we}/fare/domestic`).post(e).json(),wu=e=>Fe(`${we}/fare/queryInterReprice`).post(e).json(),Ru=e=>Fe(`${we}/fare/queryDomesticReprice`).post(e).json(),Eu=e=>Fe(`${we}/fare/route/query`).post(e).json(),Su=e=>Fe(`${we}/apiAvSearch/fareRuleBaggage`).post(e).json(),$u=e=>Fe(`${we}/apiAvSearch/domesticFreeBaggage`).post(e).json(),Pu=e=>Fe(`${we}/apiAvSearch/historyAndNewPriceCompute`).post(e).json(),Lu=e=>Fe(`${we}/fare/domestic`).post(e).json(),Mu=e=>Fe(`${we}/apiAvSearch/fareIntoAndEtdz`).post(e).json(),kr=e=>Fe(`${we}/apiAvSearch/queryRuleForInterAirPrice`).post(e).json(),Ou=e=>Fe(`${we}/fare/queryRTKTDetail`,{originalValue:!0,ignoreError:!0}).post(e).json(),Bu=e=>Fe(`${we}/fare/cancelCreditCardAuth`).post(e).json(),Hu=e=>Fe(`${we}/cpay/crsCdsPay`).post(e).json(),Uu=e=>Fe(`${we}/fare/queryTicketsDetail`,{ignoreError:!0}).post(e).json(),xr=[{code:"<",offset:"-1"},{code:">",offset:"+1"},{code:"\\",offset:"+2"},{code:"+",offset:"+2"}],fs=(e,a)=>!e||!a||!ea.test(a)?"":`${e}T${a.slice(0,2)}:${a.slice(2)}`,Nr=e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),Dr=(e,a,r,o)=>{if(Ra.test(e)){const C=B(new Date).format("YYYY").slice(0,2),[x,F,N]=e.match(/(\d{2})([A-Za-z]{3})(\d{2})/).slice(1),E=`${C}${N}`,P=B(`${E}-${Nr(F)}-${x}`,"YYYY-MMM-DD");if(!P.isValid())throw new Error;return P.format("YYYY-MM-DD")}let l=r,t=o;(l??"")===""&&(l=B(new Date).format("YYYY-MM-DD"),t=B(new Date).format("HHmm"));const d=l.substring(0,4);let y,h;const v=e?wa(e,d):`${d}-${l.substring(5)}`;return t!==""&&a!==""?(y=B(`${d}-${l.substring(5)}:${t}`),h=B(`${v}:${a}`)):(y=B(`${d}-${l.substring(5)}`),h=B(`${v}`)),h.isBefore(y)?h.add(1,"year").format("YYYY-MM-DD"):h.format("YYYY-MM-DD")},Fr=e=>({[Y.FLIGHT_STATUS]:{value:"",required:!0},[Y.COMPANY_CODE]:{value:"",required:!0},[Y.FLIGHT_NUMBER]:{value:"",required:e},[Y.CABIN_CODE]:{value:"",required:!0},[Y.DEPARTURE_DATE]:{value:"",required:e},[Y.DEPARTURE_AIRPORT]:{value:"",required:!0},[Y.DATE_TIME_RANGE]:{value:"",required:e},[Y.ARRIVAL_AIRPORT]:{value:"",required:!0},[Y.STOP_QUANTITY]:{value:"",required:!0},[Y.STOP_FLAG]:{value:"",required:!0,unorderedCode:[Y.GLOBAL_INDICATOR]},[Y.AIR_EQUIPMENT_TYPE]:{value:""},[Y.OC_AIRLINE]:{value:""},[Y.OPEN_AND_CLOSE_CABINS]:{value:""}}),wr=()=>({[Y.GLOBAL_INDICATOR]:{value:""}}),Rr=e=>{const a=e[0]!=="O";let r=e.replaceAll(/ */gi,"");const o=wr(),l=Fr(a);try{if(Object.keys(l).forEach(d=>{var v;const y=Ut.get(d),h=l[d];if(y){const C=y.exec(r);if(!C||C.index){if(h!=null&&h.required)throw new Error;return}h.value=C[0],r=r.slice(C[0].length),(v=h.unorderedCode)!=null&&v.length&&h.unorderedCode.forEach(x=>{const F=o[x],N=new RegExp(Ut.get(x),"g"),E=r.match(N);if(E!=null&&E.length){if(E.length>1)throw new Error;F.value=E[0],r=r.replace(N,"")}})}}),r)throw new Error;return{valid:!0,flightInfoForm:{...l,...o}}}catch{return{valid:!1,flightInfoForm:{...l,...o}}}},Er=(e,a,r,o)=>{var F;if(!e&&!a)return{departureDateTime:"",arrivalDateTime:""};const l=a,t=l.slice(0,4),d=Dr(e,t,r,o),y=((F=xr.find(N=>l.includes(N.code)))==null?void 0:F.offset)??0,h=B(d).add(Number(y),"day").format("YYYY-MM-DD"),v=y?l.slice(5):l.slice(4),C=fs(d,t||"0000"),x=fs(h,v||"0000");return{departureDateTime:C,arrivalDateTime:x}},Sr=e=>e.slice(2),$r=e=>{if(!e)return{openCabins:[],closeCabins:[]};let a=e,r=[],o=[];const l=Ut.get(Y.OPEN_CABINS),t=l.exec(a);if(l&&t){const h=t[0];if(r=h.replace("#D","").split("")??[],t.index)return{openCabins:r,closeCabins:[]};a=a.slice(h.length)}return Ut.get(Y.CLOSE_CABINS).exec(a)&&(o=a.replace("#C","").split("")??[]),{openCabins:r,closeCabins:o}},Pr=(e,a,r)=>{const o=e[Y.FLIGHT_STATUS].value==="O",{departureDateTime:l,arrivalDateTime:t}=Er(e[Y.DEPARTURE_DATE].value,e[Y.DATE_TIME_RANGE].value,a,r),{openCabins:d,closeCabins:y}=$r(e[Y.OPEN_AND_CLOSE_CABINS].value),h=Sr(e[Y.OC_AIRLINE].value),v=e[Y.GLOBAL_INDICATOR].value.slice(2);return{openFlag:o,globalIndicator:v,flightStatus:e[Y.FLIGHT_STATUS].value,companyCode:e[Y.COMPANY_CODE].value,flightNumber:e[Y.FLIGHT_NUMBER].value,cabinCode:e[Y.CABIN_CODE].value,departureAirport:e[Y.DEPARTURE_AIRPORT].value,departureDateTime:l,arrivalDateTime:t,arrivalAirport:e[Y.ARRIVAL_AIRPORT].value,stopQuantity:e[Y.STOP_QUANTITY].value,stopFlag:e[Y.STOP_FLAG].value,airEquipmentType:e[Y.AIR_EQUIPMENT_TYPE].value,operatingAirline:h,openCabins:d,closeCabins:y,flightType:""}},qu=(e,a,r)=>{const o=[];return e.some(d=>{const{valid:y,flightInfoForm:h}=Rr(d);return o.push(h),!y})?[]:o.map(d=>[Pr(d,a??"",r??"")])??[]},Lr=e=>{const a=[],r=[];return e.forEach(o=>{Ea.some(l=>l.value.toUpperCase()===o.toUpperCase())?r.push({passengerType:o==="CNN"?"CHD":o,selectedNumber:1}):a.push({passengerType:o,selectedNumber:1})}),{selectedPassengers:r,groupPassengers:a}},Vu=e=>{switch(e){case"RR":case"HK":return"S";case"HL":case"HN":return"L";default:return"U"}},ju=e=>e?` #O${e}`:"",Mr=e=>Fe(`${we}/apiAvSearch/queryRules/translate`).post(e).json(),Gu=e=>Fe(`${we}/apiAvSearch/queryRules`).post(e).json(),Or={class:"item-name"},Br={class:"item-state"},Hr={class:"item-container"},Ur={class:"item"},qr={class:"item-text"},Vr={class:"item-name"},jr={class:"item-state"},Gr={class:"item-container"},Yr={class:"item"},Qr={class:"item-text"},Wr={class:"item-name"},zr={class:"item-state"},Jr={class:"item-container"},Zr={class:"item"},Xr={class:"item-text"},Kr={class:"item-name"},eo={class:"item-state"},to={class:"item-container"},so={class:"item"},no={class:"item-text"},ao=nt({__name:"CollapseInfo",props:{rule:{}},setup(e){const a=vt(),{orderInfo:r,activeTag:o}=ta(a),l=We(()=>a.getFareType(o.value)),t=We(()=>r.value.get(o.value).type==="2"),d=We(()=>r.value.get(o.value).international),y=X("unfold"),h=X("unfold"),v=X(["change","refund"]),C=x=>{(x??[]).includes("change")?h.value="retract":h.value="unfold",(x??[]).includes("refund")?y.value="retract":y.value="unfold"};return kt(()=>{!d.value&&t.value&&l.value==="normalFare"&&(y.value="retract",h.value="retract")}),(x,F)=>{const N=Ta,E=ka;return!d.value&&t.value&&l.value==="normalFare"?(m(),He(E,{key:0,modelValue:v.value,"onUpdate:modelValue":F[0]||(F[0]=P=>v.value=P),onChange:C},{default:re(()=>[pe(N,{name:"change"},{title:re(()=>[u("div",Or,[u("span",null,f(x.$t("app.fareQuery.freightate.revalidationRule")),1),u("span",Br,f(x.$t(`app.fareQuery.freightate.${h.value??"unfold"}`)),1)])]),default:re(()=>[u("div",Hr,[u("div",Ur,[(m(!0),b(le,null,ue(x.rule.ruleInfos.change,(P,O)=>(m(),b("div",{key:O},[u("div",qr,f(P.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),pe(N,{name:"refund"},{title:re(()=>[u("div",Vr,[u("span",null,f(x.$t("app.fareQuery.freightate.refund")),1),u("span",jr,f(x.$t(`app.fareQuery.freightate.${y.value??"unfold"}`)),1)])]),default:re(()=>[u("div",Gr,[u("div",Yr,[(m(!0),b(le,null,ue(x.rule.ruleInfos.refund,(P,O)=>(m(),b("div",{key:O},[u("span",Qr,f(P.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"])):(m(),He(E,{key:1,modelValue:v.value,"onUpdate:modelValue":F[1]||(F[1]=P=>v.value=P),onChange:C},{default:re(()=>[pe(N,{name:"change"},{title:re(()=>[u("div",Wr,[u("span",null,f(x.$t("app.fareQuery.freightate.revalidationRule")),1),u("span",zr,f(x.$t(`app.fareQuery.freightate.${h.value??"unfold"}`)),1)])]),default:re(()=>[u("div",Jr,[u("div",Zr,[(m(!0),b(le,null,ue(x.rule.ruleInfos.change,(P,O)=>(m(),b("div",{key:O},[u("div",Xr,f(P.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),pe(N,{name:"refund"},{title:re(()=>[u("div",Kr,[u("span",null,f(x.$t("app.fareQuery.freightate.refund")),1),u("span",eo,f(x.$t(`app.fareQuery.freightate.${y.value??"unfold"}`)),1)])]),default:re(()=>[u("div",to,[u("div",so,[(m(!0),b(le,null,ue(x.rule.ruleInfos.refund,(P,O)=>(m(),b("div",{key:O},[u("span",no,f(P.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"]))}}});const ro=Nt(ao,[["__scopeId","data-v-68b3b8dc"]]),oo=e=>{const{t:a}=Wt(),r=X([]),o=X(!1),l=X([]),t=X([]),d=We(()=>e.isCnLang),y=We(()=>e.issueDate??""),h=X(),v=X(),C=X({serialNumber:"16"}),x=X(!1),F={amount:"-",currency:"-",changeType:"-",allowRefund:"-",permission:"-"},N=We(()=>e.rulesRes.translatedRuleInfo??""),E=sa({proChange:[],maxStay:"",minStay:""}),P={serialNumber:[{pattern:na,message:a("app.fareQuery.freightate.inputNum")}]},O=G=>{if(!G)return"";if(G.includes(" ")){const[p,oe]=G.split(" "),ge=/^(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?$/,I=oe.match(ge);if(I){const _=I[1].padStart(2,"0"),H=I[2].padStart(2,"0"),S=I[3]?I[3].padStart(2,"0"):"00";return`${p} ${_}:${H}:${S}`}else return G}else return`${G} 00:00:00`},j=G=>{r.value=G.map(p=>({...p,contents:p.contents.map(oe=>({strList:oe.split("<br>")}))}))},ee=()=>{var G;o.value=!1,(G=h.value)==null||G.validate(p=>{if(p)if(C.value.serialNumber){const oe=e.rulesRes.fareRuleInfos.filter(ge=>Number(C.value.serialNumber)===Number(ge.number));j(oe)}else j(e.rulesRes.fareRuleInfos)})},L=()=>{var oe,ge,I,_;o.value=!0,C.value.serialNumber="";const G=We(()=>e.rulesRes.fareRuleInfos.map(H=>({number:H.number,title:H.title}))),p=Math.ceil(((oe=G.value)==null?void 0:oe.length)/2);l.value=(ge=G.value)==null?void 0:ge.slice(0,p),t.value=(_=G.value)==null?void 0:_.slice(p,(I=G.value)==null?void 0:I.length)},U=G=>{C.value.serialNumber=G,ee()},W=G=>{E.maxStay=G.maxStay??"-",E.minStay=G.minStay??"-",(G.change??[F]).forEach(p=>{E.proChange.push({...p,name:a("app.fareQuery.freightate.revalidationRule")})}),(G.refund??[F]).forEach(p=>{E.proChange.push({...p,name:a("app.fareQuery.freightate.refund")})}),(G.noShow??[F]).forEach(p=>{E.proChange.push({...p,name:p.allowRefund?a("app.fareQuery.freightate.missedReFundFlight"):a("app.fareQuery.freightate.missedFlight")})})},Z=()=>e.detailInfo.segInfos.filter(G=>`${G.departureCode}${G.arrivalCode}`==`${e.rulesRes.departureAirport}${e.rulesRes.arrivalAirport}`).map(G=>{var p,oe;return{ref1:((p=G.ruleIds)==null?void 0:p[0])??"",ref2:((oe=G.ruleIds)==null?void 0:oe[1])??"",fareReference:G.fareBasis,departureDate:B(G.departureDateTime).format("YYYY-MM-DD hh:mm:ss"),filingAirline:G.marketingAirline,departureAirport:G.departureCode,arrivalAirport:G.arrivalCode}}),ke=G=>{switch(G){case"1":return"per one way";case"2":return"per round way";case"3":return"per ticket";case"4":return"per coupon";case"5":return"per direction";default:return"-"}},xe=G=>{switch(G){case"H":return"(取高)";case"L":return"(取低)";case"A":return"(叠加)";default:return""}},Ue=async()=>{var S,se,ne,ie,de,Ee,Pe,Le,Me,Se;const G=Date.now(),p=Z();E.proChange=[],E.maxStay="",E.minStay="";const oe=(se=(S=p??[])==null?void 0:S[0].ref1)==null?void 0:se.slice(4,-1).split("::"),ge=(ie=(ne=p??[])==null?void 0:ne[0].ref2)==null?void 0:ie.split("::"),I={};oe.forEach((Oe,je)=>{I[Oe]=ge[je]});const _={originCode:(I==null?void 0:I.Origin)??"",destinationCode:(I==null?void 0:I.Destination)??"",travelDate:((Ee=(de=p??[])==null?void 0:de[0])==null?void 0:Ee.departureDate)??"",ticketingDate:O(y.value),carrier:(I==null?void 0:I.Carrier)??"",fareReference:(I==null?void 0:I.FBC)??"",passType:(I==null?void 0:I.PassengerType)??"",ruleNumber:(I==null?void 0:I.Rule)??"",fareType:(I==null?void 0:I.FareType)??"",ftnt1:(I==null?void 0:I.Footnote1)??"",ftnt2:(I==null?void 0:I.Footnote2)??"",owrt:(I==null?void 0:I.OWRT)??"",ref1:os.encode(((Le=(Pe=p??[])==null?void 0:Pe[0])==null?void 0:Le.ref1)??""),ref2:os.encode(((Se=(Me=p??[])==null?void 0:Me[0])==null?void 0:Se.ref2)??""),routing:(I==null?void 0:I.Routing)??"",ruleTariff:(I==null?void 0:I.RuleTariff)??"",source:(I==null?void 0:I.FareSource)??"",originAddonTrf:(I==null?void 0:I.OriginAddonTariff)??"",originAddonRouting:(I==null?void 0:I.OriginAddonRouting)??"",destinationAddonTrf:(I==null?void 0:I.DestinationAddonTariff)??"",destinationAddonRouting:(I==null?void 0:I.DestinationAddonRouting)??"",fbrBaseFareBasis:(I==null?void 0:I.FbrBaseFareBasis)??"",fbrBaseRule:(I==null?void 0:I.FbrBaseRule)??"",fbrBaseTariff:(I==null?void 0:I.FbrBaseTariff)??"",originAddonFtnt1:(I==null?void 0:I.OriginAddonFootnote1)??"",originAddonFtnt2:(I==null?void 0:I.OriginAddonFootnote2)??"",destinationAddonFtnt1:(I==null?void 0:I.DestinationAddonFootnote1)??"",destinationAddonFtnt2:(I==null?void 0:I.DestinationAddonFootnote2)??"",fbrR3C25TblNo:(I==null?void 0:I.R3c025TblNo)??"",accountCode:(I==null?void 0:I.AccountCode)??""};let H=null;try{x.value=!0;const{data:Oe}=await Mr(_);H=Date.now(),Oe.value&&W(Oe.value)}finally{x.value=!1,ra(G,H,"国际运价计算查询票规-中文化")}};return kt(async()=>{await Ue(),ee()}),aa(()=>{r.value=[]}),{interRuleContent:r,priceRules:E,queryRuleInfo:ee,translateRuleInfo:Ue,QUIT_OR_UPDATE_RULE:P,quitOrUpdateRuleForm:C,quitOrUpdateRuleRef:h,loading:x,scrollbar:v,getHightLow:xe,getChargePer:ke,queryList:L,isList:o,leftColumn:l,rightColumn:t,queryRuleByNumber:U,isChineseShow:d,isCnRulesText:N}},io=oo,qt=e=>(Jt("data-v-b26eb3c5"),e=e(),Zt(),e),lo={key:0,class:"u-content"},co={class:"u-cat"},uo={class:"u-cat-tit"},go=qt(()=>u("span",{class:"w-[111px]"},null,-1)),po={class:"u-cat-cont"},fo=qt(()=>u("span",null,"Cat16",-1)),ho=[fo],yo={class:"u-right"},vo={class:"w-[60px]"},mo={key:0},bo={key:0},_o={key:1},Co={key:0},Ao={key:2},Io={key:0},To={key:3},ko={key:0},xo={key:4},No={key:0},Do={class:"u-cat"},Fo={class:"u-cat-cont u-stay"},wo=qt(()=>u("div",{class:"u-left"},[u("span",null,"Cat06")],-1)),Ro={class:"u-right"},Eo={class:"u-item"},So={class:"u-cat"},$o={class:"u-cat-cont u-stay"},Po=qt(()=>u("div",{class:"u-left"},[u("span",null,"Cat07")],-1)),Lo={class:"u-right"},Mo={class:"u-item"},Oo={class:"w-[100%]"},Bo={key:0},Ho={key:1,class:"loading-text"},Uo={key:0,class:"text-gray-2 text-xs"},qo={key:1,class:"loading-text"},Vo={key:0},jo=["onClick"],Go=["onClick"],Yo={key:1,class:"loading-text"},Qo=nt({__name:"InterRuleInfo",props:{rulesRes:{},detailInfo:{},ruleFullHeight:{type:Boolean},isCnLang:{type:Boolean},openDialog:{type:Boolean},fsn:{type:Boolean},issueDate:{}},setup(e){const a=e,{interRuleContent:r,queryRuleInfo:o,priceRules:l,quitOrUpdateRuleRef:t,quitOrUpdateRuleForm:d,QUIT_OR_UPDATE_RULE:y,loading:h,scrollbar:v,getHightLow:C,getChargePer:x,queryList:F,isList:N,queryRuleByNumber:E,leftColumn:P,rightColumn:O,isCnRulesText:j,isChineseShow:ee}=io(a);return(L,U)=>{const W=Xt,Z=oa,ke=xs,xe=Cs,Ue=xa,G=As;return ms((m(),b("div",{class:ve(["rule-table flex w-full mt-1",L.ruleFullHeight?"h-[668px]":"h-[244px]"])},[k(l).proChange.length>0?(m(),b("div",lo,[pe(W,{class:ve([L.ruleFullHeight?"h-[668px]":"h-[244px]"])},{default:re(()=>[u("div",co,[u("div",uo,[go,u("span",{class:ve([L.fsn?"w-[40px]":"w-[60px]"])},f(L.$t("app.fareQuery.freightate.allowed")),3),u("span",{class:ve([L.fsn?"w-[106px]":"w-[210px]"])},f(L.$t("app.fareQuery.freightate.Fee")),3),u("span",{class:ve([L.fsn?"w-[88px]":"w-[100px]"])},f(L.$t("app.fareQuery.freightate.scope")),3)]),u("div",po,[u("div",{class:ve(["u-left",[L.fsn?"":"ml-[8px]"]])},ho,2),u("div",yo,[(m(!0),b(le,null,ue(k(l).proChange,(p,oe)=>{var ge,I,_,H,S,se,ne,ie,de,Ee,Pe,Le,Me,Se,Oe,je,at,rt,ot,it,lt,ct,dt,ut,gt,Je,pt,ft,ht,yt;return m(),b("div",{key:oe,class:"u-item"},[u("span",vo,f(p.name),1),k(zt)()==="en"?(m(),b("span",{key:0,class:ve([L.fsn?"w-[40px]":"w-[60px]"])},f(p.allowRefund?p.allowRefund==="是"?"Y":"N":(p.permission==="是"?"Y":"N")||"-"),3)):(m(),b("span",{key:1,class:ve([L.fsn?"w-[40px]":"w-[60px]"])},f(p.allowRefund?p.allowRefund:p.permission||"-"),3)),(ge=p==null?void 0:p.discount)!=null&&ge.adt||(I=p==null?void 0:p.discount)!=null&&I.cnn||(_=p==null?void 0:p.discount)!=null&&_.unn||(H=p==null?void 0:p.discount)!=null&&H.inf||(S=p==null?void 0:p.discount)!=null&&S.ins?(m(),b("span",{key:2,class:ve([L.fsn?"w-[106px]":"w-[210px]"])},[(se=p==null?void 0:p.discount)!=null&&se.adt?(m(),b("div",mo,[ye(f(L.$t("app.fareQuery.freightate.ADT"))+" ",1),(m(!0),b(le,null,ue((ie=(ne=p.discount)==null?void 0:ne.adt)==null?void 0:ie.calculatedSpecified,(R,$e)=>{var me,be,fe,_e,Ce,Ae,Ie,he;return m(),b("span",{key:$e},[ye(f(((me=R==null?void 0:R.amountCurrency)==null?void 0:me.length)>0?(fe=R==null?void 0:R.amountCurrency)==null?void 0:fe.substring(0,((be=R==null?void 0:R.amountCurrency)==null?void 0:be.length)-1):R==null?void 0:R.amountCurrency),1),((Ae=(Ce=(_e=p.discount)==null?void 0:_e.adt)==null?void 0:Ce.calculatedSpecified)==null?void 0:Ae.length)>1||(he=(Ie=p.discount)==null?void 0:Ie.adt)!=null&&he.discountPercent?(m(),b("span",bo,"/")):ae("",!0)])}),128)),ye(f((Ee=(de=p.discount)==null?void 0:de.adt)==null?void 0:Ee.discountPercent)+" "+f(p!=null&&p.highLow?k(C)(p==null?void 0:p.highLow):""),1)])):ae("",!0),(Pe=p==null?void 0:p.discount)!=null&&Pe.cnn?(m(),b("div",_o,[ye(f(L.$t("app.fareQuery.freightate.cnn"))+" ",1),(m(!0),b(le,null,ue((Me=(Le=p.discount)==null?void 0:Le.cnn)==null?void 0:Me.calculatedSpecified,(R,$e)=>{var me,be,fe,_e,Ce,Ae,Ie,he;return m(),b("span",{key:$e},[ye(f(((me=R==null?void 0:R.amountCurrency)==null?void 0:me.length)>0?(fe=R==null?void 0:R.amountCurrency)==null?void 0:fe.substring(0,((be=R==null?void 0:R.amountCurrency)==null?void 0:be.length)-1):R==null?void 0:R.amountCurrency),1),((Ae=(Ce=(_e=p.discount)==null?void 0:_e.cnn)==null?void 0:Ce.calculatedSpecified)==null?void 0:Ae.length)>1||(he=(Ie=p.discount)==null?void 0:Ie.cnn)!=null&&he.discountPercent?(m(),b("span",Co,"/")):ae("",!0)])}),128)),ye(f((Oe=(Se=p.discount)==null?void 0:Se.cnn)==null?void 0:Oe.discountPercent)+" "+f(p!=null&&p.highLow?k(C)(p==null?void 0:p.highLow):""),1)])):ae("",!0),(je=p==null?void 0:p.discount)!=null&&je.unn?(m(),b("div",Ao,[ye(f(L.$t("app.fareQuery.freightate.unn"))+" ",1),(m(!0),b(le,null,ue((rt=(at=p.discount)==null?void 0:at.unn)==null?void 0:rt.calculatedSpecified,(R,$e)=>{var me,be,fe,_e,Ce,Ae,Ie,he;return m(),b("span",{key:$e},[ye(f(((me=R==null?void 0:R.amountCurrency)==null?void 0:me.length)>0?(fe=R==null?void 0:R.amountCurrency)==null?void 0:fe.substring(0,((be=R==null?void 0:R.amountCurrency)==null?void 0:be.length)-1):R==null?void 0:R.amountCurrency),1),((Ae=(Ce=(_e=p.discount)==null?void 0:_e.unn)==null?void 0:Ce.calculatedSpecified)==null?void 0:Ae.length)>1||(he=(Ie=p.discount)==null?void 0:Ie.unn)!=null&&he.discountPercent?(m(),b("span",Io,"/")):ae("",!0)])}),128)),ye(" "+f((it=(ot=p.discount)==null?void 0:ot.unn)==null?void 0:it.discountPercent)+" "+f(p!=null&&p.highLow?k(C)(p==null?void 0:p.highLow):""),1)])):ae("",!0),(lt=p==null?void 0:p.discount)!=null&&lt.inf?(m(),b("div",To,[ye(f(L.$t("app.fareQuery.freightate.inf"))+" ",1),(m(!0),b(le,null,ue((dt=(ct=p.discount)==null?void 0:ct.inf)==null?void 0:dt.calculatedSpecified,(R,$e)=>{var me,be,fe,_e,Ce,Ae,Ie,he;return m(),b("span",{key:$e},[ye(f(((me=R==null?void 0:R.amountCurrency)==null?void 0:me.length)>0?(fe=R==null?void 0:R.amountCurrency)==null?void 0:fe.substring(0,((be=R==null?void 0:R.amountCurrency)==null?void 0:be.length)-1):R==null?void 0:R.amountCurrency),1),((Ae=(Ce=(_e=p.discount)==null?void 0:_e.inf)==null?void 0:Ce.calculatedSpecified)==null?void 0:Ae.length)>1||(he=(Ie=p.discount)==null?void 0:Ie.inf)!=null&&he.discountPercent?(m(),b("span",ko,"/")):ae("",!0)])}),128)),ye(" "+f((gt=(ut=p.discount)==null?void 0:ut.inf)==null?void 0:gt.discountPercent)+" "+f(p!=null&&p.highLow?k(C)(p==null?void 0:p.highLow):""),1)])):ae("",!0),(Je=p==null?void 0:p.discount)!=null&&Je.ins?(m(),b("div",xo,[ye(f(L.$t("app.fareQuery.freightate.ins"))+" ",1),(m(!0),b(le,null,ue((ft=(pt=p.discount)==null?void 0:pt.ins)==null?void 0:ft.calculatedSpecified,(R,$e)=>{var me,be,fe,_e,Ce,Ae,Ie,he;return m(),b("span",{key:$e},[ye(f(((me=R==null?void 0:R.amountCurrency)==null?void 0:me.length)>0?(fe=R==null?void 0:R.amountCurrency)==null?void 0:fe.substring(0,((be=R==null?void 0:R.amountCurrency)==null?void 0:be.length)-1):R==null?void 0:R.amountCurrency),1),((Ae=(Ce=(_e=p.discount)==null?void 0:_e.ins)==null?void 0:Ce.calculatedSpecified)==null?void 0:Ae.length)>1||(he=(Ie=p.discount)==null?void 0:Ie.ins)!=null&&he.discountPercent?(m(),b("span",No,"/")):ae("",!0)])}),128)),ye(" "+f((yt=(ht=p.discount)==null?void 0:ht.ins)==null?void 0:yt.discountPercent)+" "+f(p!=null&&p.highLow?k(C)(p==null?void 0:p.highLow):""),1)])):ae("",!0)],2)):(m(),b("span",{key:3,class:ve([L.fsn?"w-[106px]":"w-[210px]"])},"-",2)),u("span",{class:ve([L.fsn?"w-[88px]":"w-[100px]"])},f(k(x)(p!=null&&p.chargePortion?p==null?void 0:p.chargePortion:"")||"-"),3)])}),128))])])]),u("div",Do,[u("div",Fo,[wo,u("div",Ro,[u("div",Eo,[u("span",null,f(L.$t("app.fareQuery.freightate.minimumStay"))+"："+f(k(l).minStay?k(l).minStay:"-"),1)])])])]),u("div",So,[u("div",$o,[Po,u("div",Lo,[u("div",Mo,[u("span",null,f(L.$t("app.fareQuery.freightate.maximumStay"))+"："+f(k(l).maxStay?k(l).maxStay:"-"),1)])])])])]),_:1},8,["class"])])):ae("",!0),u("div",Oo,[pe(Ue,{ref_key:"quitOrUpdateRuleRef",ref:t,inline:"",model:k(d),rules:k(y),onSubmit:U[1]||(U[1]=_s(()=>{},["prevent"]))},{default:re(()=>[bs(L.$slots,"default",{},void 0,!0),k(ee)?ae("",!0):(m(),b(le,{key:0},[pe(ke,{label:L.$t("app.fareQuery.freightate.ruleNumber"),prop:"serialNumber"},{default:re(()=>[pe(Z,{modelValue:k(d).serialNumber,"onUpdate:modelValue":U[0]||(U[0]=p=>k(d).serialNumber=p),placeholder:L.$t("app.fareQuery.freightate.inputNum")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),pe(xe,{type:"primary",size:"small",onClick:k(o)},{default:re(()=>[ye(f(L.$t("app.fareQuery.freightate.search")),1)]),_:1},8,["onClick"]),pe(xe,{type:"primary",link:"",size:"small",class:"ml-1",onClick:k(F)},{default:re(()=>[ye(f(L.$t("app.fareQuery.freightate.list")),1)]),_:1},8,["onClick"])],64))]),_:3},8,["model","rules"]),pe(W,{ref_key:"scrollbar",ref:v,height:L.ruleFullHeight?"610":"192",class:ve(["mt-2.5 text-xs text-gray-2"])},{default:re(()=>[!k(N)&&!k(ee)?(m(!0),b(le,{key:0},ue(k(r),(p,oe)=>(m(),b("div",{key:oe},[u("pre",null,f(`${p.number}.${p.title}`),1),p.contents.length?(m(),b("div",Bo,[(m(!0),b(le,null,ue(p.contents,(ge,I)=>(m(),b("div",{key:I},[(m(!0),b(le,null,ue(ge.strList,_=>(m(),b("div",{key:_},f(_),1))),128))]))),128))])):(m(),b("div",Ho,f(L.$t("app.fareQuery.freightate.noDataAvailable")),1))]))),128)):ae("",!0),k(ee)?(m(),b(le,{key:1},[k(j)?(m(),b("div",Uo,[u("pre",null,f(k(j)),1)])):(m(),b("div",qo,f(L.$t("app.fareQuery.freightate.noDataAvailable")),1))],64)):ae("",!0),k(N)&&!k(ee)?(m(),b(le,{key:2},[k(P).length>0||k(O).length>0?(m(),b("div",Vo,[(m(!0),b(le,null,ue(k(P),(p,oe)=>(m(),b("div",{key:oe},[k(P).length>0?(m(),b("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:ge=>k(E)(`${p==null?void 0:p.number}`)},[u("span",null,f((p==null?void 0:p.number)<10?`0${p==null?void 0:p.number}`:`${p==null?void 0:p.number}`)+" "+f(p==null?void 0:p.title),1)],8,jo)):ae("",!0)]))),128)),(m(!0),b(le,null,ue(k(O),(p,oe)=>(m(),b("div",{key:oe},[k(O).length>0?(m(),b("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:ge=>k(E)(`${p==null?void 0:p.number}`)},[u("span",null,f((p==null?void 0:p.number)<10?`0${p==null?void 0:p.number}`:`${p==null?void 0:p.number}`)+" "+f(p==null?void 0:p.title),1)],8,Go)):ae("",!0)]))),128))])):(m(),b("div",Yo,f(L.$t("app.fareQuery.freightate.noDataAvailable")),1))],64)):ae("",!0)]),_:1},8,["height"])])],2)),[[G,k(h)]])}}});const $s=Nt(Qo,[["__scopeId","data-v-b26eb3c5"]]),Wo={key:0,class:"w-[1012px]"},zo={key:0,class:"title flex gap-1"},Jo={key:0,class:"bg-gray-7 text-gray-3 text-[12px] font-[400] py-[4px] px-[5px] rounded-[2px]"},Zo={key:1},Xo={key:2},Ko={class:"content"},ei={key:1,class:"empty-info"},ti=nt({__name:"TicketRuleDialog",props:{detailInfo:{},isCnLang:{type:Boolean},issueDate:{},fsnTicketNos:{}},emits:["update:modelValue"],setup(e,{emit:a}){const r=a,o=()=>{r("update:modelValue",!1)},l=It([]),t=d=>{const y=l.value.find(h=>h.airportCode===d.toUpperCase());return y?y.airportEnName:d};return kt(async()=>{const d=await xt("searchLocalData");l.value=d?JSON.parse(d==null?void 0:d.localData):[]}),(d,y)=>{const h=Xt,v=Na;return m(),He(v,{title:d.$t("app.fareQuery.freightate.tktRule"),class:"tkt-rules-dialog","align-center":!0,modal:"","close-on-click-modal":!1,width:"1040",onClose:o},{default:re(()=>[pe(h,{ref:"scrollbar",height:"688"},{default:re(()=>{var C,x,F;return[((x=(C=d.detailInfo.ticketRegulation)==null?void 0:C.flightRules[0])==null?void 0:x.rules.length)!==0?(m(),b("div",Wo,[(m(!0),b(le,null,ue(((F=d.detailInfo.ticketRegulation)==null?void 0:F.flightRules)??[],(N,E)=>(m(),b("div",{key:E,class:"rule-item overflow-y-hidden"},[(m(!0),b(le,null,ue(N.rules,(P,O)=>{var j,ee;return m(),b("div",{key:O,class:"min-w-full"},[N.rules.length>1?(m(),b("div",zo,[(j=d.fsnTicketNos)!=null&&j[O]?(m(),b("span",Jo,f(k(tt)((ee=d.fsnTicketNos)==null?void 0:ee[O])),1)):ae("",!0),k(zt)()==="en"?(m(),b("span",Zo,f(`${t(P.departureAirport)}(${P.departureAirport}) - ${t(P.arrivalAirport)}(${P.arrivalAirport})`),1)):(m(),b("span",Xo,f(`${P.departureAirportCityCh}(${P.departureAirport}) - ${P.arrivalAirportCityCh}(${P.arrivalAirport})`),1))])):ae("",!0),u("div",Ko,[pe($s,{"is-cn-lang":d.isCnLang,"rules-res":P,"detail-info":d.detailInfo,"rule-full-height":"","issue-date":d.issueDate},{default:re(()=>[bs(d.$slots,"default")]),_:2},1032,["is-cn-lang","rules-res","detail-info","issue-date"])])])}),128))]))),128))])):(m(),b("div",ei,[u("div",null,f(d.$t("app.fareQuery.freightate.noTktData")),1)]))]}),_:3},512)]),_:3},8,["title"])}}});const si={key:0,class:"close-box"},ni={class:"flex gap-1 items-center"},ai={key:0,class:"title-text"},ri={key:1,class:"title-text"},oi={key:0,class:"title flex gap-1"},ii={key:0,class:"bg-gray-7 text-gray-3 text-[12px] font-[400] py-[4px] px-[5px] rounded-[2px]"},li={key:1},ci={key:2},di={class:"content"},ui=nt({__name:"TicketRule",props:{international:{type:Boolean},ruleInfoData:{},detailInfo:{},needCloseIcon:{type:Boolean},fullHeight:{type:Boolean},fsn:{type:Boolean},issueDate:{},fsnTicketNos:{}},emits:["closePanel"],setup(e,{emit:a}){const r=X(!1),o=X(!1),l=a,t=v=>{l("closePanel",v)},d=()=>{r.value=!0},y=It([]),h=v=>{const C=y.value.find(x=>x.airportCode===v.toUpperCase());return C?C.airportEnName:v};return kt(async()=>{const v=await xt("searchLocalData");y.value=v?JSON.parse(v==null?void 0:v.localData):[]}),(v,C)=>{var E;const x=la,F=Da,N=xs;return m(),b("div",null,[u("div",{class:ve(["rule-info",[v.fullHeight||v.fsn?"":" border-t border-gray-7 border-solid",v.fsn?"":"mt-2.5 mr-2.5"]])},[v.needCloseIcon?(m(),b("div",si,[u("div",ni,[v.detailInfo.componentCode==="tktRule"?(m(),b("span",ai,f(v.$t(`app.fareQuery.freightate.${v.detailInfo.componentCode}`)),1)):ae("",!0),v.detailInfo.componentCode==="ticketRegulation"?(m(),b("span",ri,f(v.$t(`app.fare.singleFare.${v.detailInfo.componentCode}`)),1)):ae("",!0),(v.detailInfo.componentCode==="tktRule"||v.detailInfo.componentCode==="ticketRegulation")&&v.international?(m(),b("span",{key:2,class:"text-brand-2 text-xs font-normal leading-tight cursor-pointer",onClick:d},f(v.$t("app.fareQuery.freightate.fullView")),1)):ae("",!0)]),v.fsn?ae("",!0):(m(),He(x,{key:0,class:"cursor-pointer",size:"16px",onClick:C[0]||(C[0]=P=>t(v.detailInfo))},{default:re(()=>[pe(k(ia))]),_:1}))])):ae("",!0),(m(!0),b(le,null,ue(((E=v.ruleInfoData)==null?void 0:E.flightRules)??[],(P,O)=>(m(),b("div",{key:O,class:"rule-item overflow-y-hidden"},[(m(!0),b(le,null,ue(P.rules,(j,ee)=>{var L,U;return m(),b("div",{key:ee,class:"min-w-full"},[P.rules.length>1?(m(),b("div",oi,[(L=v.fsnTicketNos)!=null&&L[ee]?(m(),b("span",ii,f(k(tt)((U=v.fsnTicketNos)==null?void 0:U[ee])),1)):ae("",!0),k(zt)()==="en"?(m(),b("span",li,f(`${h(j.departureAirport)}(${j.departureAirport}) - ${h(j.arrivalAirport)}(${j.arrivalAirport})`),1)):(m(),b("span",ci,f(`${j.departureAirportCityCh}(${j.departureAirport}) - ${j.arrivalAirportCityCh}(${j.arrivalAirport})`),1))])):ae("",!0),u("div",di,[v.international?(m(),He($s,{key:0,"is-cn-lang":o.value,"onUpdate:isCnLang":C[2]||(C[2]=W=>o.value=W),"rules-res":j,"detail-info":v.detailInfo,"rule-full-height":v.fullHeight,fsn:v.fsn,"issue-date":v.issueDate},{default:re(()=>[pe(N,null,{default:re(()=>[pe(F,{modelValue:o.value,"onUpdate:modelValue":C[1]||(C[1]=W=>o.value=W),"inline-prompt":"","active-text":v.$t("app.fareQuery.freightate.chinese"),"inactive-text":v.$t("app.fareQuery.freightate.english"),class:ve([o.value?"is-cn-switch":"is-en-switch"])},null,8,["modelValue","active-text","inactive-text","class"])]),_:1})]),_:2},1032,["is-cn-lang","rules-res","detail-info","rule-full-height","fsn","issue-date"])):(m(),He(ro,{key:1,rule:j},null,8,["rule"]))])])}),128))]))),128))],2),pe(ti,{modelValue:r.value,"onUpdate:modelValue":C[4]||(C[4]=P=>r.value=P),"is-cn-lang":o.value,"onUpdate:isCnLang":C[5]||(C[5]=P=>o.value=P),"detail-info":v.detailInfo,"issue-date":v.issueDate,"fsn-ticket-nos":v.fsnTicketNos},{default:re(()=>[pe(N,null,{default:re(()=>[pe(F,{modelValue:o.value,"onUpdate:modelValue":C[3]||(C[3]=P=>o.value=P),"inline-prompt":"","active-text":v.$t("app.fareQuery.freightate.chinese"),"inactive-text":v.$t("app.fareQuery.freightate.english"),class:ve([o.value?"is-cn-switch":"is-en-switch"])},null,8,["modelValue","active-text","inactive-text","class"])]),_:1})]),_:1},8,["modelValue","is-cn-lang","detail-info","issue-date","fsn-ticket-nos"])])}}});const gi=Nt(ui,[["__scopeId","data-v-431ff790"]]),pi=/[\u4e00-\u9fa5]+/,Ps=/\(UM\d+\)$/,fi=/[A-Za-z]+$/,Ls=/^\(UM\d+\)$/;let Vt=[];const hi=(e,a)=>!e||e===""||!B((e==null?void 0:e.updateTime)??"").isSame(a,"day"),yi=async()=>{var o;const e=await xt("PASSENGER_SPECIAL_TYPE_DATA"),a=new Date().getTime();if(hi(e,a)){const l={pageNumber:1,pageSize:500,content:{dictCode:"",dictName:"",dictTypeCode:"PASSENGER_TYPE_DETAIL"}},{data:t}=await va(l),d=((o=t==null?void 0:t.value)==null?void 0:o.content)??"";return await ya("PASSENGER_SPECIAL_TYPE_DATA",JSON.stringify(d),a),d}return JSON.parse(e==null?void 0:e.localData)},vi=async(e,a)=>{let r=a??"";if(r!=null&&r.includes("UM")){if(!Ls.test(a))return e;r="UM"}const o=(Vt??[]).find(l=>l.field1===e&&l.field2===r);return(o==null?void 0:o.field3)??e},mi=async(e,a)=>{const r=a??"";if(a.includes("UM")&&Ls.test(a))return a;const o=(Vt??[]).find(l=>l.field1===e&&l.field2===r);return(o==null?void 0:o.field2)??""},bi=e=>{const a=e.match(Ps);return a?a[0]:""},_i=e=>{const a=e.match(Ps);if(a)return a[0];const r=e.match(fi);return r&&Vt.some(l=>l.field2===r[0])?r[0]:""},Ci=async(e,a,r)=>{var d;let o="",l="",t="";if(r){const y=e.lastIndexOf(r)===-1?e.length:e.lastIndexOf(r);l=(d=e==null?void 0:e.substring(0,y))==null?void 0:d.trim(),t=r,o=await vi(a,t)}else l=e,o=a;return{specialPassengerType:o,fullName:l,nameSuffix:t}},Yu=async(e,a)=>{if(!e.trim())return{};const r=e.trim();let o;if(Vt=await yi(),pi.test(r))o=_i(e);else if(o=bi(e),!o){const t=e.split("/")[1],d=t==null?void 0:t.lastIndexOf(" ");d>-1&&(o=await mi(a,t.substring(d+1)))}return await Ci(e,a,o??"")},Qu=e=>{const a=e.endsWith(" VIP");let r=e;for(;r.endsWith(" VIP");)r=r.slice(0,-4);return{fullNameAfterCutVip:r,endsWithVIP:a}},Wu=e=>{let a=e,r=!1;return/^\(UM\d+\) VIP$/.test(e)&&(r=!0,a=e.slice(0,-4)),{nameSuffixRes:a,isVipAndUm:r}},Ai=(e,a)=>{var L;const{t:r}=Wt(),o=X([]),{copy:l,isSupported:t}=ca({legacy:!0}),d=X((L=e.tktInfo)==null?void 0:L.showTktPopover),y=X(""),h=X(),v=X(!1),C=X({}),x=U=>{C.value=U},F=U=>{v.value=U},N=()=>{d.value=!1,o.value=[],v.value=!1,a("close-popover",e.tktIndex)},E=()=>{const U=e.formTicket;if(U.secondFactorValue){if(U.secondFactorType==="PNR"&&!vs.test(U.secondFactorValue))return!1;if(U.secondFactorType==="name"&&!ua.test(U.secondFactorValue))return!1;if(U.secondFactorType==="certificate"&&U.secondFactorCode==="NI"&&!ga.test(U.secondFactorValue))return!1}else return U.secondFactorType==="PNR"||U.secondFactorType==="name",!1;return!0},P=async()=>{var U,W;if(e.isOldFare&&(a("fare-vaild"),!E()))return N(),!1;setTimeout(()=>{var Z,ke,xe;(xe=(ke=(Z=h.value)==null?void 0:Z.popperRef)==null?void 0:ke.popperInstanceRef)==null||xe.forceUpdate()},100),o.value=((U=e.conjunctionTicketNos)==null?void 0:U.length)>0?e.conjunctionTicketNos:[((W=e.tktInfo)==null?void 0:W.etNumber)||O(e.ticketNumber)],y.value=o.value[0]},O=U=>{const W=(U??"").match(Is);return(W==null?void 0:W[1])??""},j=(U,W)=>{if(!o.value.includes(U))for(let Z=0;Z<W;Z++){const ke=`${U.slice(0,U.length-10)}${String(ma(Number(U.slice(-10)),Z)).padStart(10,"0")}`;o.value.push(ke)}y.value=U},ee=U=>{t&&(l(U),ba({message:r("app.batchRefund.copySuccess"),type:"success"}))};return da(()=>{var U;return(U=e.tktInfo)==null?void 0:U.showTktPopover},()=>{var U;d.value=(U=e.tktInfo)==null?void 0:U.showTktPopover},{immediate:!0,deep:!0}),{showTktPopover:d,closePopover:N,tickets:o,openPopover:P,addTicketTab:j,activeName:y,showTktRef:h,copyInfo:ee,isChangedTicket:v,changedSecondFactor:C,updateChangedSecondFactor:x,updateIsChangedTicket:F}},Ii=Ai,Kt=e=>(Jt("data-v-188f0e04"),e=e(),Zt(),e),Ti={class:"flex-grow max-h-[calc(100vh_-_150px)] overflow-y-auto"},ki={key:0,class:"h-full border-collapse w-[100%] flex-col justify-start items-stretch inline-flex overflow-auto pr-0.5"},xi={class:"self-stretch justify-start items-stretch inline-flex"},Ni={class:"w-[50%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},Di={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Fi={class:"text-gray-2 text-xs font-normal leading-tight info-text"},wi={class:"w-[50%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},Ri={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Ei={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Si={class:"self-stretch justify-start items-stretch inline-flex"},$i={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},Pi={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Li={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Mi={class:"w-[28%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},Oi=Kt(()=>u("div",{class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},"OFFICE：",-1)),Bi={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Hi={class:"w-[25%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},Ui={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},qi={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Vi={class:"w-[25%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},ji={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Gi={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Yi={class:"self-stretch justify-start items-stretch inline-flex"},Qi={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},Wi={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},zi={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Ji={class:"w-[28%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},Zi={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Xi={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Ki=pa('<div class="w-[25%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex" data-v-188f0e04><div class="text-gray-2 text-xs font-bold leading-tight whitespace-nowrap" data-v-188f0e04></div><div class="text-gray-2 text-xs font-normal leading-tight info-text" data-v-188f0e04></div></div><div class="w-[25%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex" data-v-188f0e04><div class="text-gray-2 text-xs font-bold leading-tight whitespace-nowrap" data-v-188f0e04></div><div class="text-gray-2 text-xs font-normal leading-tight info-text" data-v-188f0e04></div></div>',2),el={class:"self-stretch justify-start items-stretch inline-flex"},tl={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},sl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},nl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},al={class:"w-[28%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},rl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},ol={class:"text-gray-2 text-xs font-normal leading-tight info-text"},il={class:"w-[50%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},ll={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},cl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},dl={class:"self-stretch justify-start items-stretch inline-flex"},ul={class:"w-full p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},gl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},pl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},fl={class:"w-full justify-start inline-flex"},hl={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},yl={class:"text-gray-2 text-xs font-bold leading-tight break-keep"},vl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},ml={class:"w-[100%] justify-start inline-flex"},bl={class:"w-[37%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},_l={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Cl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Al={class:"w-[33.33%] p-1.5 py-2.5 bg-gray-0 border border-r-0 border-b-0 border-gray-2 justify-start items-center flex"},Il={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Tl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},kl={class:"w-[33.33%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},xl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Nl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Dl={class:"w-[100%] justify-start items-center inline-flex"},Fl={class:"w-[37%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},wl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Rl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},El={class:"w-[33.33%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},Sl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},$l={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Pl={class:"w-[33.33%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},Ll={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Ml={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Ol={class:"self-stretch justify-start items-stretch inline-flex"},Bl={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},Hl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Ul={class:"text-gray-2 text-xs font-normal leading-tight info-text"},ql={class:"w-[78%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},Vl={class:"text-gray-2 text-xs font-bold leading-tight break-keep"},jl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Gl={class:"self-stretch justify-start items-stretch inline-flex"},Yl={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},Ql={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Wl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},zl={class:"w-[78%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},Jl={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Zl={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Xl={class:"self-stretch justify-start items-stretch inline-flex"},Kl={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},ec={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},tc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},sc={class:"w-[28%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},nc={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},ac={class:"text-gray-2 text-xs font-normal leading-tight info-text"},rc={class:"w-[20%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},oc={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},ic={class:"text-gray-2 text-xs font-normal leading-tight"},lc={class:"w-[30%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},cc={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},dc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},uc={class:"self-stretch justify-start items-stretch inline-flex"},gc={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},pc={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},fc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},hc={class:"w-[28%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-r-0 border-gray-2 justify-start items-center flex"},yc={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},vc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},mc={class:"w-[20%] p-1.5 py-2.5 bg-gray-0 border border-gray-2 border-r-0 border-b-0 justify-start items-center flex"},bc={class:"text-gray-2 text-xs font-bold leading-tight whitespace-pre-wrap"},_c={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Cc={class:"w-[30%] p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2 justify-start items-center flex"},Ac={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Ic={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Tc={class:"self-stretch justify-start items-stretch inline-flex"},kc={class:"w-[22%] p-1.5 py-2.5 bg-gray-0 border border-gray-2 border-r-0 justify-start items-center flex"},xc=Kt(()=>u("div",{class:"w-[27px] whitespace-nowrap text-gray-2 text-xs font-bold leading-tight"},"FP：",-1)),Nc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Dc={class:"w-[28%] p-1.5 py-2.5 bg-gray-0 border border-gray-2 border-r-0 justify-start items-center flex"},Fc=Kt(()=>u("div",{class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},"TC：",-1)),wc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Rc={class:"w-[50%] p-1.5 py-2.5 bg-gray-0 border border-gray-2 justify-start items-center flex"},Ec={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Sc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},$c={class:"self-stretch justify-start items-stretch inline-flex"},Pc={class:"w-[100%] p-1.5 py-2.5 bg-gray-0 border border-t-0 border-gray-2 justify-start items-center flex"},Lc={class:"text-gray-2 text-xs font-bold leading-tight whitespace-nowrap"},Mc={class:"text-gray-2 text-xs font-normal leading-tight info-text"},Oc={key:1,class:"mt-[110px] text-center w-full"},Bc=["alt"],Hc={class:"mt-[15px] text-gray-2 leading-6 text-lg font-bold"},Uc=nt({__name:"RtktDetailedInfo",props:{rtktDetailedInfo:{}},setup(e){return(a,r)=>{var o,l,t,d,y,h,v,C,x,F,N,E,P,O,j,ee,L,U,W,Z,ke,xe,Ue,G,p,oe,ge,I,_,H,S,se,ne,ie,de,Ee,Pe,Le,Me,Se,Oe,je,at,rt,ot,it,lt,ct,dt,ut,gt,Je,pt,ft,ht,yt,R,$e,me,be,fe,_e,Ce,Ae,Ie,he,Ge,Dt,Ft,wt,Rt,Et,St,$t,Pt,Lt,Mt,Ot,Bt;return m(),b("div",Ti,[a.rtktDetailedInfo.ticket?(m(),b("div",ki,[u("div",xi,[u("div",Ni,[u("div",Di,f(a.$t("app.queryRtkt.issueAirline")),1),u("div",Fi,f(((l=(o=a.rtktDetailedInfo)==null?void 0:o.ticket)==null?void 0:l.issueAirline)||"-"),1)]),u("div",wi,[u("div",Ri,f(a.$t("app.queryRtkt.issueMethod")),1),u("div",Ei,f(((d=(t=a.rtktDetailedInfo)==null?void 0:t.ticket)==null?void 0:d.issueType)||"-"),1)])]),u("div",Si,[u("div",$i,[u("div",Pi,f(a.$t("app.queryRtkt.asa")),1),u("div",Li,f(((h=(y=a.rtktDetailedInfo)==null?void 0:y.ticket)==null?void 0:h.iataCode)||"-"),1)]),u("div",Mi,[Oi,u("div",Bi,f(((C=(v=a.rtktDetailedInfo)==null?void 0:v.ticket)==null?void 0:C.office)||"-"),1)]),u("div",Hi,[u("div",Ui,f(a.$t("app.queryRtkt.code")),1),u("div",qi,f(((F=(x=a.rtktDetailedInfo)==null?void 0:x.ticket)==null?void 0:F.code)||"-"),1)]),u("div",Vi,[u("div",ji,f(a.$t("app.queryRtkt.ticketState")),1),u("div",Gi,f((E=(N=a.rtktDetailedInfo)==null?void 0:N.ticket)!=null&&E.ticketState?a.$t(`app.queryRtkt.${(O=(P=a.rtktDetailedInfo)==null?void 0:P.ticket)==null?void 0:O.ticketState}`):"-"),1)])]),u("div",Yi,[u("div",Qi,[u("div",Wi,f(a.$t("app.queryRtkt.account")),1),u("div",zi,f(((ee=(j=a.rtktDetailedInfo)==null?void 0:j.ticket)==null?void 0:ee.accountNumber)||"-"),1)]),u("div",Ji,[u("div",Zi,f(a.$t("app.queryRtkt.issueDate")),1),u("div",Xi,f(((U=(L=a.rtktDetailedInfo)==null?void 0:L.ticket)==null?void 0:U.issueDate)||"-"),1)]),Ki]),u("div",el,[u("div",tl,[u("div",sl,f(a.$t("app.queryRtkt.ticketNumber")),1),u("div",nl,f(((Z=(W=a.rtktDetailedInfo)==null?void 0:W.ticket)==null?void 0:Z.ticketNumber)||"-"),1)]),u("div",al,[u("div",rl,f(a.$t("app.queryRtkt.deviceNumber")),1),u("div",ol,f((xe=(ke=a.rtktDetailedInfo)==null?void 0:ke.ticket)!=null&&xe.printNumber?`DEV-${(G=(Ue=a.rtktDetailedInfo)==null?void 0:Ue.ticket)==null?void 0:G.printNumber}`:"-"),1)]),u("div",il,[u("div",ll,f(a.$t("app.queryRtkt.refundDeviceNumber")),1),u("div",cl,f((oe=(p=a.rtktDetailedInfo)==null?void 0:p.ticket)!=null&&oe.refundPrintNumber?`DEV-${(I=(ge=a.rtktDetailedInfo)==null?void 0:ge.ticket)==null?void 0:I.refundPrintNumber}`:"-"),1)])]),u("div",dl,[u("div",ul,[u("div",gl,f(a.$t("app.queryRtkt.eiItem")),1),u("div",pl,f(((H=(_=a.rtktDetailedInfo)==null?void 0:_.ticket)==null?void 0:H.ei)||"-"),1)])]),u("div",fl,[u("div",hl,[u("div",yl,f(a.$t("app.queryRtkt.passengerName")),1),u("div",vl,f(((se=(S=a.rtktDetailedInfo)==null?void 0:S.passenger)==null?void 0:se.passengerName.replace("/","").trim())||"-"),1)]),u("div",{class:ve(["w-[78%] flex-col justify-start inline-flex",(de=(ie=(ne=a.rtktDetailedInfo)==null?void 0:ne.passenger)==null?void 0:ie.segments)!=null&&de.length?"":"border border-l-0 border-b-0 border-gray-2"])},[(m(!0),b(le,null,ue((Pe=(Ee=a.rtktDetailedInfo)==null?void 0:Ee.passenger)==null?void 0:Pe.segments,(Ne,jt)=>(m(),b(le,{key:jt},[u("div",ml,[u("div",bl,[u("div",_l,f(a.$t("app.queryRtkt.originAndDestination")),1),u("div",Cl,f(Ne.departureCityName)+"("+f(Ne.departureCity)+")-"+f(Ne.arrivalCityName)+"("+f(Ne.arrivalCity)+")",1)]),u("div",Al,[u("div",Il,f(a.$t("app.queryRtkt.flightNumber")),1),u("div",Tl,f(Ne.flightNo),1)]),u("div",kl,[u("div",xl,f(a.$t("app.queryRtkt.departureDate")),1),u("div",Nl,f(Ne.departureDateTime),1)])]),u("div",Dl,[u("div",Fl,[u("div",wl,f(a.$t("app.queryRtkt.cabin")),1),u("div",Rl,f(Ne==null?void 0:Ne.cabin),1)]),u("div",El,[u("div",Sl,f(a.$t("app.queryRtkt.fareBasis")),1),u("div",$l,f(Ne==null?void 0:Ne.fareBasis),1)]),u("div",Pl,[u("div",Ll,f(a.$t("app.queryRtkt.baggage")),1),u("div",Ml,f(Ne==null?void 0:Ne.baggage),1)])])],64))),128))],2)]),u("div",Ol,[u("div",Bl,[u("div",Hl,f(a.$t("app.queryRtkt.fareFlag")),1),u("div",Ul,f((Me=(Le=a.rtktDetailedInfo)==null?void 0:Le.price)!=null&&Me.autoFareType?a.$t("app.queryRtkt.auto"):a.$t("app.queryRtkt.manual")),1)]),u("div",ql,[u("div",Vl,f(a.$t("app.queryRtkt.fcItem")),1),u("div",jl,f(((Oe=(Se=a.rtktDetailedInfo)==null?void 0:Se.price)==null?void 0:Oe.fc)||"-"),1)])]),u("div",Gl,[u("div",Yl,[u("div",Ql,f(a.$t("app.queryRtkt.totalTax")),1),u("div",Wl,f(((at=(je=a.rtktDetailedInfo)==null?void 0:je.price)==null?void 0:at.taxAmount)||"-"),1)]),u("div",zl,[u("div",Jl,f(a.$t("app.queryRtkt.taxDetails")),1),u("div",Zl,f(((ot=(rt=a.rtktDetailedInfo)==null?void 0:rt.price)==null?void 0:ot.taxDetail)||"-"),1)])]),u("div",Xl,[u("div",Kl,[u("div",ec,f(a.$t("app.queryRtkt.ticketAmountFR")),1),u("div",tc,f(((lt=(it=a.rtktDetailedInfo)==null?void 0:it.price)==null?void 0:lt.ticketAmountFOrR)||"-"),1)]),u("div",sc,[u("div",nc,f(a.$t("app.queryRtkt.ticketAmountE")),1),u("div",ac,f(((dt=(ct=a.rtktDetailedInfo)==null?void 0:ct.price)==null?void 0:dt.ticketAmount)||"-"),1)]),u("div",rc,[u("div",oc,f(a.$t("app.queryRtkt.commissionFare")),1),u("div",ic,f(((gt=(ut=a.rtktDetailedInfo)==null?void 0:ut.ticket)==null?void 0:gt.internationalIndicator)==="D"?(pt=(Je=a.rtktDetailedInfo)==null?void 0:Je.price)==null?void 0:pt.commissionFare:"-"),1)]),u("div",lc,[u("div",cc,f(a.$t("app.queryRtkt.commissionRate")),1),u("div",dc,f(((ht=(ft=a.rtktDetailedInfo)==null?void 0:ft.ticket)==null?void 0:ht.internationalIndicator)==="I"?(R=(yt=a.rtktDetailedInfo)==null?void 0:yt.price)==null?void 0:R.commissionRate:"-"),1)])]),u("div",uc,[u("div",gc,[u("div",pc,f(a.$t("app.queryRtkt.scny"))+"：",1),u("div",fc,f(((me=($e=a.rtktDetailedInfo)==null?void 0:$e.price)==null?void 0:me.scny)||"-"),1)]),u("div",hc,[u("div",yc,f(a.$t("app.queryRtkt.fareAmount")),1),u("div",vc,f(((fe=(be=a.rtktDetailedInfo)==null?void 0:be.price)==null?void 0:fe.fareAmount)||"-"),1)]),u("div",mc,[u("div",bc,[ye(f(a.$t("app.queryRtkt.issueAirlineCode"))+" ",1),u("span",_c,f(((Ce=(_e=a.rtktDetailedInfo)==null?void 0:_e.ticket)==null?void 0:Ce.issueAirlineCode)||"-"),1)])]),u("div",Cc,[u("div",Ac,f(a.$t("app.queryRtkt.gpSign")),1),u("div",Ic,f(((Ie=(Ae=a.rtktDetailedInfo)==null?void 0:Ae.ticket)==null?void 0:Ie.ticketManagementOrganizationCode)??"-")+" "+f(((Ge=(he=a.rtktDetailedInfo)==null?void 0:he.ticket)==null?void 0:Ge.ticketType)!=="NONE"?"-":"")+" "+f((((Ft=(Dt=a.rtktDetailedInfo)==null?void 0:Dt.ticket)==null?void 0:Ft.ticketType)==="NONE"?"":(Rt=(wt=a.rtktDetailedInfo)==null?void 0:wt.ticket)==null?void 0:Rt.ticketType)??""),1)])]),u("div",Tc,[u("div",kc,[xc,u("div",Nc,f(((St=(Et=a.rtktDetailedInfo)==null?void 0:Et.ticket)==null?void 0:St.fp)||"-"),1)]),u("div",Dc,[Fc,u("div",wc,f(((Pt=($t=a.rtktDetailedInfo)==null?void 0:$t.price)==null?void 0:Pt.tc)||"-"),1)]),u("div",Rc,[u("div",Ec,f(a.$t("app.queryRtkt.creditCard")),1),u("div",Sc,f(((Mt=(Lt=a.rtktDetailedInfo)==null?void 0:Lt.price)==null?void 0:Mt.creditCardDetail)||"-"),1)])]),u("div",$c,[u("div",Pc,[u("div",Lc,f(a.$t("app.queryRtkt.originalTicket")),1),u("div",Mc,f(((Bt=(Ot=a.rtktDetailedInfo)==null?void 0:Ot.ticket)==null?void 0:Bt.originalTicket)||"-"),1)])])])):(m(),b("div",Oc,[u("img",{src:_a,alt:a.$t("app.querySearch.airCityCommon.noData"),class:"inline"},null,8,Bc),u("p",Hc,f(a.$t("app.querySearch.airCityCommon.noData")),1)]))])}}});const qc=Nt(Uc,[["__scopeId","data-v-188f0e04"]]),Vc=u("i",{class:"iconfont icon-windowed mr-[2px]"},null,-1),jc=nt({__name:"RtktWindoing",props:{rtktDetailedInfo:{}},emits:["openRtktDetailWindow"],setup(e,{emit:a}){const r=e,o=a,l=ar(),t=async()=>{o("openRtktDetailWindow");const d=r.rtktDetailedInfo.ticket.ticketNumber.indexOf(r.rtktDetailedInfo.ticket.issueAirlineCode);let y="";d===0&&(y=r.rtktDetailedInfo.ticket.ticketNumber.substring(d+r.rtktDetailedInfo.ticket.issueAirlineCode.length)),y=`${r.rtktDetailedInfo.ticket.issueAirlineCode}-${y}`,await l.delRtktDetailInfoWindowsList(y),l.setRtktDetailInfoWindowsList({...r.rtktDetailedInfo,id:y}),l.closeFastQuery()};return(d,y)=>(m(),b("div",{class:"open-detail-dialog flex items-center text-brand-2 text-[12px] cursor-pointer",onClick:y[0]||(y[0]=h=>t())},[Vc,u("div",null,f(d.$t("app.fastQuery.windowing")),1)]))}}),Gc=(e,a)=>{const{t:r}=Wt(),o=We(()=>e.isInternational),l=X(),t=X(!0),d=X([{code:"D",showText:r("app.fareQuery.freightate.originalTextTicket"),isShow:!0},{code:"H",showText:r("app.fareQuery.freightate.history"),isShow:!0},{code:"F",showText:r("app.fareQuery.freightate.certAndOther"),isShow:!0},{code:"X",showText:r("app.fareQuery.freightate.taxDetail"),isShow:!0},{code:"R",showText:r("app.fareQuery.freightate.rtktDetail"),isShow:!0},{code:"N",showText:r("app.fareQuery.freightate.fsnTkt"),isShow:o.value}]),y=X("D"),h=X(""),v=It([]),C=It({}),x=It(),F=X({}),N=X([]),E=X(!1),P=_=>{const H=(_??"").match(Is);return(H==null?void 0:H[1])??""},O=async()=>{var H,S;const _=(H=(await Fa({detrType:y.value,ticketNo:e.ticketNumber,intl:ha()==="en",secondFactor:e.isChangedTicket?e.changedSecondFactor:e.secondFactor})).data.value)==null?void 0:H.data;switch(y.value){case"F":h.value=is(((S=_==null?void 0:_.credential)==null?void 0:S.certificatesText)??"");break;case"D":v.value=is((_==null?void 0:_.openSourceText)??"").split(`\r
`),a("updateSecondFactor",(_==null?void 0:_.secondFactor)??e.changedSecondFactor);break;case"X":h.value=(_==null?void 0:_.ticketFareInfoText)??"";break;case"H":h.value=(_==null?void 0:_.tktHistoryText)??"";break}},j=async()=>{const _=await xt("searchLocalData"),H=[];(JSON.parse(_.localData??"")??[]).forEach(S=>{const se={code:S.airportCode,cnName:S.cityArray[1],enName:S.cityArray[0]};H.push(se)}),x.value=H},ee=_=>((_??[]).forEach((H,S)=>{var se;(se=x.value)==null||se.forEach(ne=>{ne.code===H.departureCity&&(_[S]=Object.assign(_[S],{departureCityName:ne.cnName})),ne.code===H.arrivalCity&&(_[S]=Object.assign(_[S],{arrivalCityName:ne.cnName}))})}),_),L=()=>{const{passenger:_}=C.value;_&&(_.segments=ee(_==null?void 0:_.segments))},U=_=>((_==null?void 0:_.filter(S=>S.flightNo))??[]).map(S=>{var se,ne,ie;return(se=S.fareBasis)!=null&&se.includes("/")?(ie=S.fareBasis)==null?void 0:ie.slice(0,(ne=S.fareBasis)==null?void 0:ne.indexOf("/")):S.fareBasis}).join("/"),W=_=>_.gpSign?_.gpSign.trim():_.negotiatedFareCode,Z=_=>{const H=_.replace(/\s+/g,"");return H&&H!=="null"?_.replace(/\s+/g,"T"):""},ke=_=>{const H=_==null?void 0:_.filter(S=>S.flightNo);N.value=(H??[]).map(S=>S.ticketNo)},xe=_=>((_==null?void 0:_.filter(S=>S.flightNo))??[]).map(S=>{var se,ne,ie,de;return[{openFlag:((se=S.flightNo)==null?void 0:se.includes("OPEN"))||((ne=S.flightNo)==null?void 0:ne.length)<=2,companyCode:((ie=S.flightNo)==null?void 0:ie.slice(0,2))??"",flightNumber:((de=S.flightNo)==null?void 0:de.slice(2))??"",cabinCode:S.cabin??"",departureDateTime:Z(S.departureDateTime??""),departureAirport:S.departureCity??"",arrivalDateTime:Z(S.arrivalDateTime??""),arrivalAirport:S.arrivalCity??"",flightType:"",stopQuantity:"",stopFlag:""}]}),Ue=_=>{var ie,de,Ee,Pe,Le,Me;ke(_.passenger.conjunctionSegments);const H=[_.passenger.passengerType??""],{selectedPassengers:S,groupPassengers:se}=Lr(H);return l.value=`${((ie=_.ticket)==null?void 0:ie.issueDate)??""}${((de=_.ticket)==null?void 0:de.issueTime)??""}`,{creditCard:W(_.price)==="GP",fareType:"",farebasic:U(_.passenger.conjunctionSegments),currencyCode:((Ee=_.price.fareAmount)==null?void 0:Ee.slice(0,3))??"",selectedPassengers:S,company:_.ticket.issueAirline,farePricingSale:!0,negotiatedFareCode:W(_.price),queryExclusiveNegotiatedFare:_.price.queryExclusiveNegotiatedFare??!1,allb:"0",pricingSource:"Both",reprice:"0",voyages:xe(_.passenger.conjunctionSegments),brand:"",baggage:"",altTicketingDateTime:`${((Pe=_.ticket)==null?void 0:Pe.issueDate.slice(0,-3))??""}${((Le=_.ticket)==null?void 0:Le.issueTime)??""}`,airportCode:"",groupPassengerType:se.filter(Se=>Se).map(Se=>Se.passengerType).join(",")??"",priceVerification:{ticketAmount:_.price.fsnAmount??"",taxAmount:_.price.totalTaxAmount??"",totalAmount:((Me=_.price.fareAmount)==null?void 0:Me.slice(3))??""}}},G=async()=>{var H,S;const _=((H=(await cs(e.ticketNumber)).data.value)==null?void 0:H.data)??{};return t.value=((S=_.price)==null?void 0:S.autoFareType)??!0,JSON.stringify(_)!=="{}"?Ue(_):{}},p=async()=>{await G().then(async _=>{var H,S;return JSON.stringify(_)!=="{}"&&t.value?((S=(H=await kr(_))==null?void 0:H.data)==null?void 0:S.value)??{}:{}}).then(_=>{var H,S;if(_){let se=[];const ne=(H=_.internationalPriceComputeResponse)==null?void 0:H.fareItems;Object.keys(ne).forEach(ie=>{if(ne[ie].length){const de=ne[ie].map(Ee=>({...Ee,passengerType:ie}));se=se.concat(de)}}),F.value.fareData=(S=se.map(ie=>({...ie,componentCode:"tktRule"}))??[])==null?void 0:S[0],F.value.fareData.ticketRegulation=_.rulesResponse,F.value.rulesResponse=_.rulesResponse}}).catch(_=>{throw _})},oe=async _=>{var H;try{if(y.value=_,E.value=!0,y.value==="R"){C.value=((H=(await cs(e.ticketNumber)).data.value)==null?void 0:H.data)??{},await L(),await fa();const S=document.querySelector(`.${e.parentClassName}`),se=document.querySelector(`.${e.parentClassName} .bkc-el-popper__arrow`);if(S&&se&&["D","R"].includes(y.value)){const ne=S.offsetTop,ie=S.offsetHeight+ne-window.innerHeight;if(ne>ie&&ie>0){let de=ne-ie;de=de-2>0?de-2:2,S.style.top=`${de}px`,se.style.top=`${se.offsetTop+(ne-de)}px`}}}else y.value==="N"?await p():await O()}catch{F.value={},C.value={},h.value="",v.value=[]}finally{E.value=!1}},ge=_=>{const H=_.match(Qt);return tt((H==null?void 0:H[1])??"")??""},I=_=>{const H=_.split("/").length;a("updateIsChangedTicket",!0),a("clickTicketNo",P(_),H)};return kt(async()=>{oe("D"),await j()}),{ticketModelList:d,ticketModel:y,detrResultText:h,rtktDetailedInfo:C,detrResultList:v,loading:E,chooseTktModel:oe,getExchTicket:ge,clickTicketNo:I,tktRulesData:F,fsnIssueDate:l,fsnTicketNos:N,isAutoFare:t}},Yc=Gc,Qc={class:"w-[100%] h-[24px] justify-between items-center inline-flex"},Wc={class:"justify-start items-start flex border-collapse"},zc=["data-gid","onClick"],Jc={key:0},Zc={key:1,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5"},Xc={class:"text-[14px] font-normal leading-relaxed"},Kc={key:1},ed={key:1,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5"},td={class:"text-[14px] font-normal leading-relaxed"},sd={key:2,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5 max-h-[calc(100vh_-_150px)]"},nd={key:0,class:"text-[14px] self-stretch grow shrink basis-0 text-gray-1 font-normal leading-relaxed"},ad={key:0},rd=["onClick"],od={class:"inline-block"},id={key:1},ld={key:1,class:"text-xs font-normal leading-relaxed text-[14px]"},cd={key:3,class:"w-[100%] min-h-[420px] mt-2.5 p-2.5 bg-gray-7 rounded flex-col justify-start items-start gap-2.5 inline-flex overflow-auto pr-0.5"},dd={key:0,class:"self-stretch grow shrink basis-0 text-gray-1 text-[14px] font-normal leading-relaxed"},ud={key:1,class:"text-[14px] font-normal leading-relaxed"},gd=nt({__name:"TicketPopItem",props:{ticketNumber:{},secondFactor:{},changedSecondFactor:{},isCdsTicket:{type:Boolean},parentClassName:{},isInternational:{type:Boolean},isChangedTicket:{type:Boolean}},emits:["clickTicketNo","openRtktDetailWindow","updateSecondFactor","updateIsChangedTicket"],setup(e,{emit:a}){const r=e,o=a,{ticketModelList:l,ticketModel:t,detrResultText:d,rtktDetailedInfo:y,detrResultList:h,loading:v,chooseTktModel:C,getExchTicket:x,clickTicketNo:F,tktRulesData:N,fsnIssueDate:E,fsnTicketNos:P,isAutoFare:O}=Yc(r,o);return(j,ee)=>{const L=Xt,U=As;return ms((m(),b("div",null,[u("div",Qc,[u("div",Wc,[(m(!0),b(le,null,ue(k(l),(W,Z)=>(m(),b("div",{key:Z},[W.isShow?(m(),b("div",{key:0,"data-gid":"091M010"+(Z+2),class:ve(["self-stretch px-3 py-0.5 border justify-center items-center gap-2.5 flex cursor-pointer first:rounded-l-sm last:rounded-r-sm",k(t)===W.code?"bg-brand-7 border-brand-2":"bg-gray-0 border-gray-6"]),onClick:ke=>k(C)(W.code)},[u("div",{class:ve(["text-center text-xs font-normal",k(t)===W.code?"leading-tight text-brand-2":"fleading-tight text-gray-2"])},f(W.showText),3)],10,zc)):ae("",!0)]))),128))]),k(t)==="R"&&k(y).ticket?(m(),He(jc,{key:0,"rtkt-detailed-info":k(y),onOpenRtktDetailWindow:ee[0]||(ee[0]=W=>j.$emit("openRtktDetailWindow"))},null,8,["rtkt-detailed-info"])):ae("",!0)]),u("div",null,[u("div",null,[k(t)==="R"?(m(),b("div",Jc,[k(y).ticket?(m(),He(qc,{key:0,class:"mt-2.5","rtkt-detailed-info":k(y)},null,8,["rtkt-detailed-info"])):(m(),b("div",Zc,[u("div",Xc,f(j.$t("app.queryRtkt.noData")),1)]))])):k(t)==="N"?(m(),b("div",Kc,[JSON.stringify(k(N))!=="{}"&&k(O)?(m(),He(L,{key:0,ref:"scrollbar","max-height":"400"},{default:re(()=>[pe(gi,{international:"","rule-info-data":k(N).rulesResponse,"detail-info":k(N).fareData,fsn:"","need-close-icon":"","issue-date":k(E),"fsn-ticket-nos":k(P)},null,8,["rule-info-data","detail-info","issue-date","fsn-ticket-nos"])]),_:1},512)):(m(),b("div",ed,[u("div",td,f(j.$t("app.queryRtkt.noFareTip")),1)]))])):k(t)==="D"?(m(),b("div",sd,[k(h).length>0?(m(),b("div",nd,[(m(!0),b(le,null,ue(k(h),(W,Z)=>(m(),b(le,{key:Z},[k(Qt).test(W)?(m(),b("span",ad,[ye("EXCH:  "),u("span",{class:"text-brand-2 font-bold cursor-pointer mr-5 underline",onClick:ke=>k(F)(k(x)(W))},f(k(x)(W)),9,rd),u("pre",od,f(W.replace(k(Qt),"")),1)])):(m(),b("pre",id,f(W),1))],64))),128))])):(m(),b("div",ld,f(j.$t("app.queryRtkt.noData")),1))])):k(t)!=="D"&&k(t)!=="R"&&k(t)!=="N"?(m(),b("div",cd,[k(d)?(m(),b("div",dd,[u("pre",null,f(k(d)),1)])):(m(),b("div",ud,f(j.$t("app.queryRtkt.noData")),1))])):ae("",!0)])])])),[[U,k(v)]])}}}),pd=e=>(Jt("data-v-28bdd9b9"),e=e(),Zt(),e),fd={key:0},hd={class:"font-bold text-base"},yd={key:1,class:"text-brand-2 text-base font-bold leading-normal cursor-pointer"},vd={key:2,class:"text-brand-2 text-xs font-bold leading-tight cursor-pointer w-[125px]"},md={class:"ticket-no cursor-pointer mr-5 text-sm"},bd=pd(()=>u("i",{class:"iconfont icon-close"},null,-1)),_d=[bd],Cd=nt({__name:"TicketInfoPopover",props:{tktInfo:{},isInternational:{type:Boolean},tktIndex:{},ticketNumber:{},secondFactor:{},outClass:{default:""},refundClassType:{},level:{default:1},queryType:{},conjunctionTicketNos:{},showUnderline:{type:Boolean,default:!0},ticketNumberColorClass:{},prefix:{},title:{type:Boolean},isCdsTicket:{type:Boolean},formTicket:{},isOldFare:{type:Boolean,default:!1}},emits:["close-popover"],setup(e,{emit:a}){const r=e,o=a,{showTktPopover:l,closePopover:t,tickets:d,openPopover:y,addTicketTab:h,activeName:v,showTktRef:C,copyInfo:x,isChangedTicket:F,changedSecondFactor:N,updateChangedSecondFactor:E,updateIsChangedTicket:P}=Ii(r,o);return(O,j)=>{const ee=Cs,L=Sa,U=$a,W=Pa;return m(),He(W,{ref_key:"showTktRef",ref:C,visible:k(l),"onUpdate:visible":j[3]||(j[3]=Z=>ls(l)?l.value=Z:null),placement:"right",teleported:!0,"popper-class":`tkt-crs-popper ${O.outClass}tkt-crs-popper${O.tktIndex} max-h-[calc(100vh_-_20px)]`,"popper-options":{modifiers:[{name:"preventOverflow",options:{padding:10,rootBoundary:"viewport"}}]},width:792,trigger:"click",onBeforeLeave:k(t),onBeforeEnter:k(y)},{reference:re(()=>{var Z;return[(Z=O.tktInfo)!=null&&Z.etNumber?(m(),b("span",fd,[pe(ee,{link:"",type:"primary"},{default:re(()=>[u("span",hd,f(k(tt)(O.tktInfo.etNumber)),1)]),_:1}),u("em",{class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:j[0]||(j[0]=_s(ke=>{var xe;return k(x)(((xe=O.tktInfo)==null?void 0:xe.etNumber)??"")},["stop"]))})])):O.refundClassType==="0"?(m(),b("span",yd,f(k(tt)(O.ticketNumber??"")),1)):O.refundClassType==="1"?(m(),b("span",vd,f(k(tt)(O.ticketNumber??"")),1)):(m(),b("span",{key:3,class:ve(["text-brand-2 text-xs font-normal leading-tight cursor-pointer",[O.showUnderline?"underline":""]])},[O.prefix?(m(),b("span",{key:0,class:ve(["prefix",O.ticketNumberColorClass])},f(O.prefix),3)):ae("",!0),ye(" "+f(O.title&&O.ticketNumber?O.$t("app.fareQuery.freightate.details"):k(tt)(O.ticketNumber??"")),1)],2))]}),default:re(()=>[pe(U,{modelValue:k(v),"onUpdate:modelValue":j[1]||(j[1]=Z=>ls(v)?v.value=Z:null),type:"border-card",class:"demo-tabs"},{default:re(()=>[(m(!0),b(le,null,ue(k(d),Z=>(m(),He(L,{key:Z,name:Z},{label:re(()=>[u("span",md,f(k(tt)(Z)),1)]),default:re(()=>[k(v)===Z?(m(),He(gd,{key:0,"is-international":O.isInternational,"ticket-number":Z,"second-factor":O.secondFactor,"changed-second-factor":k(N),"is-cds-ticket":O.isCdsTicket??!1,"parent-class-name":`${O.outClass}tkt-crs-popper${O.tktIndex}`,"is-changed-ticket":k(F),onClickTicketNo:k(h),onOpenRtktDetailWindow:k(t),onUpdateSecondFactor:k(E),onUpdateIsChangedTicket:k(P)},null,8,["is-international","ticket-number","second-factor","changed-second-factor","is-cds-ticket","parent-class-name","is-changed-ticket","onClickTicketNo","onOpenRtktDetailWindow","onUpdateSecondFactor","onUpdateIsChangedTicket"])):ae("",!0)]),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]),u("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-6 z-10",onClick:j[2]||(j[2]=(...Z)=>k(t)&&k(t)(...Z))},_d)]),_:1},8,["visible","popper-class","onBeforeLeave","onBeforeEnter"])}}});const zu=Nt(Cd,[["__scopeId","data-v-28bdd9b9"]]);export{er as $,Va as A,Ve as B,Gt as C,pr as D,du as E,tr as F,cu as G,fu as H,ws as I,hu as J,bu as K,_u as L,br as M,yu as N,lr as O,sr as P,vu as Q,qc as R,gu as S,gi as T,uu as U,ps as V,pu as W,lu as X,gs as Y,mu as Z,jc as _,Ba as a,Bu as a0,Nu as a1,Qu as a2,Wu as a3,Ru as a4,wu as a5,Uu as a6,Pu as a7,Ou as a8,$u as a9,Mu as aa,Fu as ab,Lu as ac,Hu as ad,Au as ae,Ir as af,xu as ag,Iu as ah,Ka as ai,Ut as aj,Y as ak,yi as al,Ha as b,ou as c,Ke as d,Du as e,qu as f,ju as g,Lr as h,Vu as i,Su as j,Eu as k,Yu as l,vt as m,zu as n,ru as o,ze as p,Gu as q,ja as r,Ns as s,Cu as t,ar as u,Rr as v,Tu as w,et as x,ku as y,iu as z};

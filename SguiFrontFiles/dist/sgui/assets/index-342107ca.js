import{f as s,g as i,j as l,k as r,w as p,v as g,M as m,fT as f,l as c,T as y,_ as v}from"./index-5ab303b6.js";const _=s({name:"ElCollapseTransition"}),h=s({..._,setup(e){const d=i("collapse-transition"),o=t=>{t.style.maxHeight="",t.style.overflow=t.dataset.oldOverflow,t.style.paddingTop=t.dataset.oldPaddingTop,t.style.paddingBottom=t.dataset.oldPaddingBottom},n={beforeEnter(t){t.dataset||(t.dataset={}),t.dataset.oldPaddingTop=t.style.paddingTop,t.dataset.oldPaddingBottom=t.style.paddingBottom,t.style.maxHeight=0,t.style.paddingTop=0,t.style.paddingBottom=0},enter(t){t.dataset.oldOverflow=t.style.overflow,t.scrollHeight!==0?t.style.maxHeight=`${t.scrollHeight}px`:t.style.maxHeight=0,t.style.paddingTop=t.dataset.oldPaddingTop,t.style.paddingBottom=t.dataset.oldPaddingBottom,t.style.overflow="hidden"},afterEnter(t){t.style.maxHeight="",t.style.overflow=t.dataset.oldOverflow},enterCancelled(t){o(t)},beforeLeave(t){t.dataset||(t.dataset={}),t.dataset.oldPaddingTop=t.style.paddingTop,t.dataset.oldPaddingBottom=t.style.paddingBottom,t.dataset.oldOverflow=t.style.overflow,t.style.maxHeight=`${t.scrollHeight}px`,t.style.overflow="hidden"},leave(t){t.scrollHeight!==0&&(t.style.maxHeight=0,t.style.paddingTop=0,t.style.paddingBottom=0)},afterLeave(t){o(t)},leaveCancelled(t){o(t)}};return(t,T)=>(l(),r(y,m({name:c(d).b()},f(n)),{default:p(()=>[g(t.$slots,"default")]),_:3},16,["name"]))}});var a=v(h,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse-transition/src/collapse-transition.vue"]]);a.install=e=>{e.component(a.name,a)};const u=a;export{u as _};

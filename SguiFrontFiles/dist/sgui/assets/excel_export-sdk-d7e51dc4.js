import{a as f}from"./FileSaver.min-9981bf4e.js";import{E as u}from"./exceljs.min-ea0def99.js";import{c as i}from"./cloneDeep-315a2c4c.js";import{di as p}from"./index-5ab303b6.js";import{k as m}from"./index-bc4d5ef3.js";function x(e,n){return p(n,function(c){return e[c]})}function E(e){return e==null?[]:x(e,m(e))}function k(e,n,c){const a=i(e);a.forEach(o=>{Object.keys(n).forEach(t=>{Object.prototype.hasOwnProperty.call(n,t)&&(o[n[t]]=o[t]),delete o[t]})});const s=new u.Workbook,r=s.addWorksheet(c);r.addRow(E(n)),a.forEach(o=>{r.addRow(Object.values(o))});const l={size:12};r==null||r.eachRow({includeEmpty:!0},function(o){o.eachCell({includeEmpty:!0},function(t){t.style={font:l}})}),s.xlsx.writeBuffer().then(o=>{const t=new Blob([o],{type:"application/octet-stream"});f.saveAs(t,`${c}.xlsx`)})}export{k as e};

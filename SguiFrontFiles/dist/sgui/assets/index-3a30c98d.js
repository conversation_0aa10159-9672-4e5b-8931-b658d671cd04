import{B as m,db as y,f as u,g as h,h as v,j as a,m as i,k as r,w as b,aN as E,l as o,E as C,z as t,p as c,v as d,_,A as g}from"./index-5ab303b6.js";const B=m({type:{type:String,values:["primary","success","warning","info","danger","default"],default:"default"},underline:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},href:{type:String,default:""},icon:{type:y}}),w={click:l=>l instanceof MouseEvent},$=["href"],L=u({name:"ElLink"}),N=u({...L,props:B,emits:w,setup(l,{emit:p}){const s=l,n=h("link"),f=v(()=>[n.b(),n.m(s.type),n.is("disabled",s.disabled),n.is("underline",s.underline&&!s.disabled)]);function k(e){s.disabled||p("click",e)}return(e,S)=>(a(),i("a",{class:c(o(f)),href:e.disabled||!e.href?void 0:e.href,onClick:k},[e.icon?(a(),r(o(C),{key:0},{default:b(()=>[(a(),r(E(e.icon)))]),_:1})):t("v-if",!0),e.$slots.default?(a(),i("span",{key:1,class:c(o(n).e("inner"))},[d(e.$slots,"default")],2)):t("v-if",!0),e.$slots.icon?d(e.$slots,"icon",{key:2}):t("v-if",!0)],10,$))}});var P=_(N,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/link/src/link.vue"]]);const I=g(P);export{I as E};

import{B as Y,_ as j,f as R,F as Z,g as I,d as ee,r as c,h as w,dv as te,u as A,fO as D,j as k,k as W,w as q,W as ae,G as X,p as H,l as m,n as $,X as le,T as se,dx as oe,m as J,x as K,a8 as re,C as ne,dw as _,U,a2 as V,dL as ie,ap as F,L as ce,aE as ue,o as ve,da as fe,v as me,aN as de,z as pe,d2 as he,A as be}from"./index-5ab303b6.js";const g=4,ye={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},ge=({move:d,size:u,bar:r})=>({[r.size]:u,transform:`translate${r.axis}(${d}%)`}),Q=Symbol("scrollbarContextKey"),we=Y({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean}),Se="Thumb",ze=R({__name:"thumb",props:we,setup(d){const u=d,r=Z(Q),l=I("scrollbar");r||ee(Se,"can not inject scrollbar context");const i=c(),v=c(),o=c({}),f=c(!1);let a=!1,h=!1,b=oe?document.onselectstart:null;const t=w(()=>ye[u.vertical?"vertical":"horizontal"]),S=w(()=>ge({size:u.size,move:u.move,bar:t.value})),z=w(()=>i.value[t.value.offset]**2/r.wrapElement[t.value.scrollSize]/u.ratio/v.value[t.value.offset]),T=s=>{var e;if(s.stopPropagation(),s.ctrlKey||[1,2].includes(s.button))return;(e=window.getSelection())==null||e.removeAllRanges(),O(s);const n=s.currentTarget;n&&(o.value[t.value.axis]=n[t.value.offset]-(s[t.value.client]-n.getBoundingClientRect()[t.value.direction]))},P=s=>{if(!v.value||!i.value||!r.wrapElement)return;const e=Math.abs(s.target.getBoundingClientRect()[t.value.direction]-s[t.value.client]),n=v.value[t.value.offset]/2,p=(e-n)*100*z.value/i.value[t.value.offset];r.wrapElement[t.value.scroll]=p*r.wrapElement[t.value.scrollSize]/100},O=s=>{s.stopImmediatePropagation(),a=!0,document.addEventListener("mousemove",C),document.addEventListener("mouseup",y),b=document.onselectstart,document.onselectstart=()=>!1},C=s=>{if(!i.value||!v.value||a===!1)return;const e=o.value[t.value.axis];if(!e)return;const n=(i.value.getBoundingClientRect()[t.value.direction]-s[t.value.client])*-1,p=v.value[t.value.offset]-e,E=(n-p)*100*z.value/i.value[t.value.offset];r.wrapElement[t.value.scroll]=E*r.wrapElement[t.value.scrollSize]/100},y=()=>{a=!1,o.value[t.value.axis]=0,document.removeEventListener("mousemove",C),document.removeEventListener("mouseup",y),L(),h&&(f.value=!1)},x=()=>{h=!1,f.value=!!u.size},M=()=>{h=!0,f.value=a};te(()=>{L(),document.removeEventListener("mouseup",y)});const L=()=>{document.onselectstart!==b&&(document.onselectstart=b)};return A(D(r,"scrollbarElement"),"mousemove",x),A(D(r,"scrollbarElement"),"mouseleave",M),(s,e)=>(k(),W(se,{name:m(l).b("fade"),persisted:""},{default:q(()=>[ae(X("div",{ref_key:"instance",ref:i,class:H([m(l).e("bar"),m(l).is(m(t).key)]),onMousedown:P},[X("div",{ref_key:"thumb",ref:v,class:H(m(l).e("thumb")),style:$(m(S)),onMousedown:T},null,38)],34),[[le,s.always||f.value]])]),_:1},8,["name"]))}});var G=j(ze,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/thumb.vue"]]);const Ee=Y({always:{type:Boolean,default:!0},width:String,height:String,ratioX:{type:Number,default:1},ratioY:{type:Number,default:1}}),_e=R({__name:"bar",props:Ee,setup(d,{expose:u}){const r=d,l=c(0),i=c(0);return u({handleScroll:o=>{if(o){const f=o.offsetHeight-g,a=o.offsetWidth-g;i.value=o.scrollTop*100/f*r.ratioY,l.value=o.scrollLeft*100/a*r.ratioX}}}),(o,f)=>(k(),J(re,null,[K(G,{move:l.value,ratio:o.ratioX,size:o.width,always:o.always},null,8,["move","ratio","size","always"]),K(G,{move:i.value,ratio:o.ratioY,size:o.height,vertical:"",always:o.always},null,8,["move","ratio","size","always"])],64))}});var ke=j(_e,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/bar.vue"]]);const He=Y({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:{type:Boolean,default:!1},wrapStyle:{type:ne([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},id:String,role:String,ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical"]}}),Te={scroll:({scrollTop:d,scrollLeft:u})=>[d,u].every(_)},Ce="ElScrollbar",Le=R({name:Ce}),Ne=R({...Le,props:He,emits:Te,setup(d,{expose:u,emit:r}){const l=d,i=I("scrollbar");let v,o;const f=c(),a=c(),h=c(),b=c("0"),t=c("0"),S=c(),z=c(1),T=c(1),P=w(()=>{const e={};return l.height&&(e.height=U(l.height)),l.maxHeight&&(e.maxHeight=U(l.maxHeight)),[l.wrapStyle,e]}),O=w(()=>[l.wrapClass,i.e("wrap"),{[i.em("wrap","hidden-default")]:!l.native}]),C=w(()=>[i.e("view"),l.viewClass]),y=()=>{var e;a.value&&((e=S.value)==null||e.handleScroll(a.value),r("scroll",{scrollTop:a.value.scrollTop,scrollLeft:a.value.scrollLeft}))};function x(e,n){he(e)?a.value.scrollTo(e):_(e)&&_(n)&&a.value.scrollTo(e,n)}const M=e=>{_(e)&&(a.value.scrollTop=e)},L=e=>{_(e)&&(a.value.scrollLeft=e)},s=()=>{if(!a.value)return;const e=a.value.offsetHeight-g,n=a.value.offsetWidth-g,p=e**2/a.value.scrollHeight,E=n**2/a.value.scrollWidth,N=Math.max(p,l.minSize),B=Math.max(E,l.minSize);z.value=p/(e-p)/(N/(e-N)),T.value=E/(n-E)/(B/(n-B)),t.value=N+g<e?`${N}px`:"",b.value=B+g<n?`${B}px`:""};return V(()=>l.noresize,e=>{e?(v==null||v(),o==null||o()):({stop:v}=ie(h,s),o=A("resize",s))},{immediate:!0}),V(()=>[l.maxHeight,l.height],()=>{l.native||F(()=>{var e;s(),a.value&&((e=S.value)==null||e.handleScroll(a.value))})}),ce(Q,ue({scrollbarElement:f,wrapElement:a})),ve(()=>{l.native||F(()=>{s()})}),fe(()=>s()),u({wrapRef:a,update:s,scrollTo:x,setScrollTop:M,setScrollLeft:L,handleScroll:y}),(e,n)=>(k(),J("div",{ref_key:"scrollbarRef",ref:f,class:H(m(i).b())},[X("div",{ref_key:"wrapRef",ref:a,class:H(m(O)),style:$(m(P)),onScroll:y},[(k(),W(de(e.tag),{id:e.id,ref_key:"resizeRef",ref:h,class:H(m(C)),style:$(e.viewStyle),role:e.role,"aria-label":e.ariaLabel,"aria-orientation":e.ariaOrientation},{default:q(()=>[me(e.$slots,"default")]),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],38),e.native?pe("v-if",!0):(k(),W(ke,{key:0,ref_key:"barRef",ref:S,height:t.value,width:b.value,always:e.always,"ratio-x":T.value,"ratio-y":z.value},null,8,["height","width","always","ratio-x","ratio-y"]))],2))}});var Be=j(Ne,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/scrollbar/src/scrollbar.vue"]]);const Pe=be(Be);export{ye as B,Pe as E};

import{B as Z,db as ae,f as N,D as te,F as q,g9 as ne,h as I,gF as re,j as S,m as _,G as R,v as D,p as v,l as e,H as ie,x as M,w as k,k as K,aN as de,E as ue,z as j,n as Q,_ as W,gG as ce,C as G,dM as x,g2 as fe,d6 as me,gA as pe,fX as Y,r as E,gH as ge,U as ve,gI as ye,a2 as H,ap as Ce,o as be,gJ as he,gb as J,dx as ke,N as De,S as X,g as Ee,L as Ie,W as Ae,P as Se,Q as Te,M as Be,cY as Fe,X as we,T as $e,Y as Pe,gK as Le,A as Re}from"./index-5ab303b6.js";import{c as Me}from"./refs-24680d12.js";import{i as Ne}from"./isUndefined-aa0326a0.js";const ee=Symbol("dialogInjectionKey"),oe=Z({center:Boolean,alignCenter:Boolean,closeIcon:{type:ae},customClass:{type:String,default:""},draggable:Boolean,fullscreen:Boolean,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Oe={close:()=>!0},ze=["aria-level"],Ue=["aria-label"],Ve=["id"],_e=N({name:"ElDialogContent"}),Ke=N({..._e,props:oe,emits:Oe,setup(o){const t=o,{t:u}=te(),{Close:F}=ce,{dialogRef:n,headerRef:c,bodyId:A,ns:a,style:y}=q(ee),{focusTrapRef:r}=q(ne),f=I(()=>[a.b(),a.is("fullscreen",t.fullscreen),a.is("draggable",t.draggable),a.is("align-center",t.alignCenter),{[a.m("center")]:t.center},t.customClass]),m=Me(r,n),C=I(()=>t.draggable);return re(n,c,C),(s,d)=>(S(),_("div",{ref:e(m),class:v(e(f)),style:Q(e(y)),tabindex:"-1"},[R("header",{ref_key:"headerRef",ref:c,class:v(e(a).e("header"))},[D(s.$slots,"header",{},()=>[R("span",{role:"heading","aria-level":s.ariaLevel,class:v(e(a).e("title"))},ie(s.title),11,ze)]),s.showClose?(S(),_("button",{key:0,"aria-label":e(u)("el.dialog.close"),class:v(e(a).e("headerbtn")),type:"button",onClick:d[0]||(d[0]=w=>s.$emit("close"))},[M(e(ue),{class:v(e(a).e("close"))},{default:k(()=>[(S(),K(de(s.closeIcon||e(F))))]),_:1},8,["class"])],10,Ue)):j("v-if",!0)],2),R("div",{id:e(A),class:v(e(a).e("body"))},[D(s.$slots,"default")],10,Ve),s.$slots.footer?(S(),_("footer",{key:0,class:v(e(a).e("footer"))},[D(s.$slots,"footer")],2)):j("v-if",!0)],6))}});var je=W(Ke,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog-content.vue"]]);const qe=Z({...oe,appendToBody:Boolean,appendTo:{type:G(String),default:"body"},beforeClose:{type:G(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1},headerAriaLevel:{type:String,default:"2"}}),Ge={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[x]:o=>fe(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},Ye=(o,t)=>{var u;const n=me().emit,{nextZIndex:c}=pe();let A="";const a=Y(),y=Y(),r=E(!1),f=E(!1),m=E(!1),C=E((u=o.zIndex)!=null?u:c());let s,d;const w=ge("namespace",he),O=I(()=>{const i={},h=`--${w.value}-dialog`;return o.fullscreen||(o.top&&(i[`${h}-margin-top`]=o.top),o.width&&(i[`${h}-width`]=ve(o.width))),i}),z=I(()=>o.alignCenter?{display:"flex"}:{});function $(){n("opened")}function U(){n("closed"),n(x,!1),o.destroyOnClose&&(m.value=!1)}function V(){n("close")}function P(){d==null||d(),s==null||s(),o.openDelay&&o.openDelay>0?{stop:s}=J(()=>L(),o.openDelay):L()}function T(){s==null||s(),d==null||d(),o.closeDelay&&o.closeDelay>0?{stop:d}=J(()=>l(),o.closeDelay):l()}function B(){function i(h){h||(f.value=!0,r.value=!1)}o.beforeClose?o.beforeClose(i):T()}function p(){o.closeOnClickModal&&B()}function L(){ke&&(r.value=!0)}function l(){r.value=!1}function g(){n("openAutoFocus")}function b(){n("closeAutoFocus")}function le(i){var h;((h=i.detail)==null?void 0:h.focusReason)==="pointer"&&i.preventDefault()}o.lockScroll&&ye(r);function se(){o.closeOnPressEscape&&B()}return H(()=>o.modelValue,i=>{i?(f.value=!1,P(),m.value=!0,C.value=Ne(o.zIndex)?c():C.value++,Ce(()=>{n("open"),t.value&&(t.value.scrollTop=0)})):r.value&&T()}),H(()=>o.fullscreen,i=>{t.value&&(i?(A=t.value.style.transform,t.value.style.transform=""):t.value.style.transform=A)}),be(()=>{o.modelValue&&(r.value=!0,m.value=!0,P())}),{afterEnter:$,afterLeave:U,beforeLeave:V,handleClose:B,onModalClick:p,close:T,doClose:l,onOpenAutoFocus:g,onCloseAutoFocus:b,onCloseRequested:se,onFocusoutPrevented:le,titleId:a,bodyId:y,closed:f,style:O,overlayDialogStyle:z,rendered:m,visible:r,zIndex:C}},He=["aria-label","aria-labelledby","aria-describedby"],Je=N({name:"ElDialog",inheritAttrs:!1}),Xe=N({...Je,props:qe,emits:Ge,setup(o,{expose:t}){const u=o,F=De();X({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},I(()=>!!F.title)),X({scope:"el-dialog",from:"custom-class",replacement:"class",version:"2.3.0",ref:"https://element-plus.org/en-US/component/dialog.html#attributes",type:"Attribute"},I(()=>!!u.customClass));const n=Ee("dialog"),c=E(),A=E(),a=E(),{visible:y,titleId:r,bodyId:f,style:m,overlayDialogStyle:C,rendered:s,zIndex:d,afterEnter:w,afterLeave:O,beforeLeave:z,handleClose:$,onModalClick:U,onOpenAutoFocus:V,onCloseAutoFocus:P,onCloseRequested:T,onFocusoutPrevented:B}=Ye(u,c);Ie(ee,{dialogRef:c,headerRef:A,bodyId:f,ns:n,rendered:s,style:m});const p=Le(U),L=I(()=>u.draggable&&!u.fullscreen);return t({visible:y,dialogContentRef:a}),(l,g)=>(S(),K(Pe,{to:l.appendTo,disabled:l.appendTo!=="body"?!1:!l.appendToBody},[M($e,{name:"dialog-fade",onAfterEnter:e(w),onAfterLeave:e(O),onBeforeLeave:e(z),persisted:""},{default:k(()=>[Ae(M(e(Se),{"custom-mask-event":"",mask:l.modal,"overlay-class":l.modalClass,"z-index":e(d)},{default:k(()=>[R("div",{role:"dialog","aria-modal":"true","aria-label":l.title||void 0,"aria-labelledby":l.title?void 0:e(r),"aria-describedby":e(f),class:v(`${e(n).namespace.value}-overlay-dialog`),style:Q(e(C)),onClick:g[0]||(g[0]=(...b)=>e(p).onClick&&e(p).onClick(...b)),onMousedown:g[1]||(g[1]=(...b)=>e(p).onMousedown&&e(p).onMousedown(...b)),onMouseup:g[2]||(g[2]=(...b)=>e(p).onMouseup&&e(p).onMouseup(...b))},[M(e(Te),{loop:"",trapped:e(y),"focus-start-el":"container",onFocusAfterTrapped:e(V),onFocusAfterReleased:e(P),onFocusoutPrevented:e(B),onReleaseRequested:e(T)},{default:k(()=>[e(s)?(S(),K(je,Be({key:0,ref_key:"dialogContentRef",ref:a},l.$attrs,{"custom-class":l.customClass,center:l.center,"align-center":l.alignCenter,"close-icon":l.closeIcon,draggable:e(L),fullscreen:l.fullscreen,"show-close":l.showClose,title:l.title,"aria-level":l.headerAriaLevel,onClose:e($)}),Fe({header:k(()=>[l.$slots.title?D(l.$slots,"title",{key:1}):D(l.$slots,"header",{key:0,close:e($),titleId:e(r),titleClass:e(n).e("title")})]),default:k(()=>[D(l.$slots,"default")]),_:2},[l.$slots.footer?{name:"footer",fn:k(()=>[D(l.$slots,"footer")])}:void 0]),1040,["custom-class","center","align-center","close-icon","draggable","fullscreen","show-close","title","aria-level","onClose"])):j("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,He)]),_:3},8,["mask","overlay-class","z-index"]),[[we,e(y)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["to","disabled"]))}});var Ze=W(Xe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog.vue"]]);const eo=Re(Ze);export{eo as E,Ge as a,qe as d,Ye as u};

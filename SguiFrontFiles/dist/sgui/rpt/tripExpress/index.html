<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta content="text/html; charset=UTF-8" http-equiv="content-type">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <!-- 初始设置一个默认的favicon -->  
  <link id="favicon" rel="icon" href="/sgui/favicon-earth.ico">  
  <title id="sgui-title">Loading...</title>
  <script>  
    // 当文档加载完成后执行  
    document.addEventListener('DOMContentLoaded', function() {  
        // 如果intl,使用海外版本icon，否则使用默认的favicon  
        const sguiLoginRouter =  localStorage.getItem('sguiLoginRouter');
        // 域名/路由/sguiLoginRouter
        const isIntl = window.location.hostname.includes('intl') || window.location.pathname?.includes('intl') || sguiLoginRouter?.includes('intl');
        const faviconHref = isIntl ? '/favicon-intel.ico' : '/favicon-earth.ico';  
        const sguiTitle = isIntl ? 'Selling GUI' : '共翔天易'
        // 获取<link rel="icon">元素并设置其href属性  
        const faviconLink = document.getElementById('favicon');  
        faviconLink.href = faviconHref;  
        const sguiTitleDom = document.getElementById('sgui-title');  
        sguiTitleDom.textContent  = sguiTitle;  
      });  
  </script>  
  <script type="module" crossorigin src="/sgui/assets/index-5ab303b6.js"></script>
  <link rel="stylesheet" href="/sgui/assets/index-743f0613.css">
</head>

<body>
  <noscript>
    <strong>We're sorry but Selling GUI doesn't work properly without JavaScript enabled. Please enable it to
      continue.</strong>
  </noscript>
  <div id="root-app"></div>
  
</body>

</html>

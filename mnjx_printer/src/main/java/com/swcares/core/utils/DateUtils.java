package com.swcares.core.utils;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.lang.Integer.parseInt;

/**
 * <AUTHOR>
 */
public class DateUtils extends DateUtil {

    /**
     * 月份英文缩写
     */
    public static final String[] MONTH_EN = {"JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"};
    /**
     * 星期英文缩写
     */
    private static final String[] WEEK_EN = {"SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"};
    /**
     * 星期中文
     */
    private static final String[] WEEK_CH = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
    /**
     * 日期的Com格式
     * eg: 02JUL21 或者 05DEC
     */
    private static final Pattern PATTERN_COM = Pattern.compile("^(\\d{1,2})([A-Za-z]{3})(\\d{2})?$");

    /**
     * 航信日期的简写格式：+表示明天 -表示昨天 .表示今天
     */
    private static final Pattern PATTERN_SIM_COM = Pattern.compile("([+-.])");

    /**
     * 日期的正则格式
     * 格式1：yyyy-MM-dd
     * 格式2：yyyy/MM/dd
     * 格式3：yyyyMMdd
     */
    private static final Pattern PATTERN_YMD = Pattern.compile("^(\\d{4})[-/]?(\\d{2})[-/]?(\\d{1,2})$");
    /**
     * 日期的正则格式
     * 格式1：yyyy-MM
     * 格式2：yyyy/MM
     * 格式3：yyyyMM
     */
    private static final Pattern PATTERN_YM = Pattern.compile("^(\\d{4})[-/]?(\\d{2})$");

    /**
     * 小时分钟的格式 Hm: 1104
     */
    private static final Pattern PATTERN_HM = Pattern.compile("\\d{4}");

    private static final Pattern PATTERN_ZH = Pattern.compile("^((\\d+)小时)?((\\d+)分)?((\\d+)秒)?((\\d+)毫秒)?$");

    /**
     * 获取英文的星期几
     *
     * @param index 索引值，从0开始，最大值6
     * @return 获取英文的星期几
     */
    public static String getWeekEn(int index) {
        return WEEK_EN[index];
    }

    /**
     * 根据年 月获取当月天数
     *
     * @param date 2022-02-18
     * @return 转换后的结果：28天
     */
    public static Integer getDays(String date) {
        //根据时间获取年
        Date ymd2Date = ymd2Date(date);
        int year = DateUtil.year(ymd2Date);
        // 它是从0开始计数的，所以要+1
        int month = DateUtil.month(ymd2Date) + 1;
        return DateUtil.lengthOfMonth(month, DateUtil.isLeapYear(year));
    }

    /**
     * 【DA/ACRT 指令模板中在用】
     * 当前时间获取小时分钟字符串
     * 2022-05-12 16:58:39
     *
     * @return hhmm 转换后的结果：【1658】
     */
    public static String nowHm2ComHm() {
        Date date = ymdhms2Date(now());
        return StrUtil.format("{}{}", date2hh(date), date2mi(date));
    }

    /**
     * 【ACRT指令模板中在用】
     * 当前日期转航信日期
     * 2022-05-12
     *
     * @return 12MAY22
     */
    public static String nowYmdToCom() {
        return ymd2Com(today());
    }

    /**
     * 将日期格式【yyyy-MM-dd HH:mm:ss】转换成【yyyy-MM-dd】格式的字符串
     *
     * @param date 日期.格式：【2022-05-17 14:07:03】
     * @return 【yyyy-MM-dd】格式的字符串 转换后的结果：【2022-05-17】
     */
    public static String date2ymd(Date date) {
        return DateUtil.formatDate(date);
    }

    /**
     * 将日期格式【yyyy-MM-dd HH:mm:ss】转换成【hh】格式的字符串  （小时）
     *
     * @param date 日期.格式：【2022-05-17 14:07:03】
     * @return HH格式的字符串 转换后的结果：【14】
     */
    public static String date2hh(Date date) {
        return StrUtil.fillBefore(StrUtil.toString(DateUtil.hour(date, true)), '0', 2);
    }

    /**
     * 将日期格式【yyyy-MM-dd HH:mm:ss】转换成【mi】 格式的字符串  （分钟）
     *
     * @param date 日期.格式：【2022-05-17 14:07:03】
     * @return mi格式的字符串 转换后：【07】
     */
    public static String date2mi(Date date) {
        return StrUtil.fillBefore(StrUtil.toString(DateUtil.minute(date)), '0', 2);
    }

    /**
     * 【da指令模板在使用】
     * 将日期格式【yyyy-MM-dd HH:mm:ss】转换成【hhmm】 格式的字符串  （小时分钟）
     *
     * @param date 日期.格式：【2022-05-17 14:07:03】 默认type=0
     * @return HHmm格式的字符串 转换后的格式：【1407】
     */
    public static String date2hm(Date date) {
        return date2hm(date, 0);
    }

    /**
     * 将日期格式【yyyy-MM-dd HH:mm:ss】转换成【hh:mm】或者【hhmm】 格式的字符串  （小时：分钟）
     *
     * @param date 日期.格式：【2022-05-17 14:07:03】
     * @param type 规定日期的转换规则 参数输入0或者1 默认是0
     * @return type=0 转换后的格式 【1407】
     * type=1 转换后的格式 【14:07】
     */
    public static String date2hm(Date date, int type) {
        String hm = StrUtil.EMPTY;
        switch (type) {
            case 0:
                hm = StrUtil.format("{}{}", date2hh(date), date2mi(date));
                break;
            case 1:
                hm = StrUtil.format("{}:{}", date2hh(date), date2mi(date));
                break;
            default:
                break;
        }
        return hm;
    }

    /**
     * 【da指令模板在使用】
     * 标准日期转航信日期
     *
     * @param date 2022-05-19 10:57:04
     * @param len  要截取的长度
     * @return 如果 len=5 返回 17MAY 如果len=7 返回 17MAY22（与这个方法ymd2Com() 的结果相同,可以任选一个使用）
     */
    public static String date2PreCom(Date date, int len) {
        String com = ymd2Com(date2ymd(date));
        return StrUtil.subPre(com, len);
    }

    /**
     * 将【yyyy-MM-dd hh:mm:ss】字符串格式的日期字符串转换为【yyyy-MM-dd】字符串
     * eg: yyyy-MM-dd hh:mm:ss--->【yyyy-MM-dd】
     *
     * @param ymdhms 【yyyy-MM-dd hh:mm:ss】格式的字符串 2022-05-18 00:00:00
     * @return 日期 【yyyy-MM-dd】 2022-05-18
     */
    public static String ymdhms2YmdStr(String ymdhms) {
        return DateUtil.formatDate(ymd2Date(ymdhms));
    }

    /**
     * 将yyyy-MM-dd 格式的日期字符串转换为yyyy-MM-dd hh:mm:ss
     * eg: yyyy-MM-dd---->yyyy-MM-dd hh:mm:ss
     *
     * @param ymd 【yyyy-MM-dd】格式的字符串 2022-05-18
     * @return 日期 【yyyy-MM-dd hh:mm:ss】 2022-05-18 00:00:00
     */
    public static String ymd2YmdhmsStr(String ymd) {
        return DateUtil.formatDateTime(ymd2Date(ymd));
    }

    /**
     * 将【yyyy-MM-dd】格式的日期字符串转换为日期类型
     *
     * @param ymd 【yyyy-MM-dd】格式的字符串
     * @return 日期 【2022-05-17】
     */
    public static Date ymd2Date(String ymd) {
        return DateUtil.parse(ymd);
    }

    /**
     * 将【yyyy-MM-dd HH:mm:ss】格式的日期字符串转换为日期类型
     *
     * @param ymdhms 【yyyy-MM-dd HH:mm:ss】格式的字符串  (大写的H是24小时制，小写的h是12小时制)
     * @return 日期 【2022-05-17 14:07:03】
     */
    public static Date ymdhms2Date(String ymdhms) {
        return DateUtil.parse(ymdhms, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * [ACRT指令模板在用]
     * 字符串的时间转换成小时分钟字符串
     *
     * @param dateStr【2022-05-17 14:07:03】
     * @return hhmm 【1439】
     */
    public static String ymdhms2hm(String dateStr) {
        Date date = ymdhms2Date(dateStr);
        return StrUtil.format("{}{}", date2hh(date), date2mi(date));
    }


    /**
     * 将【yyyy-MM-dd 或者yyyy/MM/dd或者 yyyyMMdd】格式的日期转换为航信服务器认可的日期
     *
     * @param date YMD格式的日期 例如：yyyy-MM-dd yyyy/MM/dd yyyyMMdd
     * @return 转换后的日期 如：17JUL09
     */
    public static String ymd2Com(String date) {
        boolean match = ReUtil.isMatch(PATTERN_YMD, date);
        if (match) {
            List<String> groups = ReUtil.getAllGroups(PATTERN_YMD, date);
            String year = groups.get(1).substring(2);
            int month = Integer.parseInt(groups.get(2));
            String day = StrUtil.fillBefore(groups.get(3), '0', 2);
            return StrUtil.format("{}{}{}", day, MONTH_EN[month - 1], year);
        } else {
            throw new DateException("不支持的日期格式");
        }
    }

    /**
     * 将【yyyy-MM 或者yyyy/MM或者 yyyyMM】格式的日期转换为航信服务器认可的日期
     *
     * @param date YMD格式的日期 例如：yyyy-MM yyyy/MM yyyyMM
     * @return 转换后的日期 如：JUL22
     */
    public static String ym2Com(String date) {
        boolean match = ReUtil.isMatch(PATTERN_YM, date);
        if (match) {
            List<String> groups = ReUtil.getAllGroups(PATTERN_YM, date);
            String year = groups.get(1).substring(2);
            int month = Integer.parseInt(groups.get(2));
            return StrUtil.format("{}{}", MONTH_EN[month - 1], year);
        } else {
            throw new DateException("不支持的日期格式");
        }
    }

    /**
     * DETR_TN 模板在用
     * 标准日期转航信日期
     *
     * @param date 2022-05-19
     * @param len  要截取的长度
     * @return 如果 len=5 返回 17MAY 如果len=7 返回 17MAY22（与这个方法ymd2Com() 的结果相同,可以任选一个使用）
     */
    public static String ymd2PreCom(String date, int len) {
        String com = ymd2Com(date);
        return StrUtil.subPre(com, len);
    }

    /**
     * 获得某日期是星期几的英文表示
     *
     * @param ymdDate 参数 2022-05-17
     * @return 转换后的结果 TUE
     */
    public static String ymd2WeekEn(String ymdDate) {
        Calendar c = Calendar.getInstance();
        c.setTime(ymd2Date(ymdDate));
        return WEEK_EN[c.get(Calendar.DAY_OF_WEEK) - 1];
    }

    /**
     * 获得某日期是星期几的英文表示
     *
     * @param ymdDate 参数 2022-05-17
     * @return 转换后的结果 TUE
     */
    public static String ymd2WeekCh(String ymdDate) {
        Calendar c = Calendar.getInstance();
        c.setTime(ymd2Date(ymdDate));
        return WEEK_CH[c.get(Calendar.DAY_OF_WEEK) - 1];
    }

    /**
     * 获得日期的星期数字表示，7,1,2,3,4,5,6
     *
     * @param ymdDate 日期 2022-05-17
     * @return 星期的数字表示 转换后的结果：2
     */
    public static int ymd2WeekNum(String ymdDate) {
        int n = DateUtil.dayOfWeek(ymd2Date(ymdDate)) - 1;
        n = n < 1 ? 7 : n;
        return n;
    }

    /**
     * 时间格式转换（时分秒转换成航信字符串格式的时分）
     *
     * @param hmis 12:10:00
     * @return 转换后 【1210】
     */
    public static String hmis2ComHm(String hmis) {
        Date dateHmis = parseTimeToday(hmis);
        return StrUtil.format("{}{}", date2hh(dateHmis), date2mi(dateHmis));
    }

    /**
     * 小时分钟字符串转换为小时分钟的时间格式
     *
     * @param hm 1210
     * @return 转换后的结果：12:10:00
     */
    public static String comHm2hmis(String hm) {
        return comHm2hmis(hm, 0);
    }

    /**
     * 具体时间按转为 具体当天某时
     *
     * @param hmis 12:10:10
     * @return 2023-06-19 12:10:10
     */
    public static String hmis2Ymdhmis(String hmis) {
        return StrUtil.format("{} {}", DateUtil.today(), hmis);
    }

    /**
     * 具体时间按转为 具体当天某时 并转换为日期格式
     *
     * @param hmis 12:10:10
     * @return 2023-06-19 12:10:10
     */
    public static Date hmis2Date(String hmis) {
        String ymdhmis = StrUtil.format("{} {}", DateUtil.today(), hmis);
        return ymdhms2Date(ymdhmis);
    }

    /**
     * 小时分钟字符串转换为小时分钟的时间格式
     *
     * @param hm   1210 （时间规则24:00:00 默认是00:00:00,hm传入的参数不能大于23:59）
     * @param type 规定日期的转换规则 参数输入0或者1 默认是0
     * @return type=0时,返回的格式是：12:10:00
     * type=1时,返回的格式是：12:10
     */
    public static String comHm2hmis(String hm, int type) {
        if (!ReUtil.isMatch(PATTERN_HM, hm)) {
            throw new DateException(StrUtil.format("{}日期格式不对", hm));
        }
        String hh = StrUtil.subPre(hm, 2);
        String mi = StrUtil.subSuf(hm, 2);
        String hmis = StrUtil.EMPTY;
        switch (type) {
            case 0:
                hmis = StrUtil.format("{}:{}:00", hh, mi);
                break;
            case 1:
                hmis = StrUtil.format("{}:{}", hh, mi);
                break;
            default:
                break;
        }
//        比较结果，如果date1 < date2，返回数小于0，date1==date2返回0，date1 > date2 大于0
        int compare = DateUtil.compare(DateUtil.parseTimeToday("23:59:59"), DateUtil.parseTimeToday(hmis));
        if (compare < 0) {
            throw new DateException("TIME");
        }
        return hmis;
    }

    /**
     * 航信日期转标准日期【18MAY/18MAY22 转换后的格式 2022-05-18】
     * 【. 表示今天：转换后 2022-05-18
     * + 表示明天：转换后 2022-05-19
     * - 表示昨天：转换后 2022-05-17
     * 】
     *
     * @param dateCom 航信日期 23MAY/23MAY22/./+/-  (不输入年份时，默认补充为当年)
     * @return 标准日期 转换后的结果：【2022-05-23】
     */
    public static String com2ymd(String dateCom) {
        return com2ymd(dateCom, 0);
    }

    /**
     * @param dateCom 航信日期 【23MAY/23MAY22/./+/-】
     * @param type    规定日期的转换规则 参数输入0或者1
     *                next=0，dateCom为23MAY 不跟年份 默认补齐为当年
     *                next=1，dateCom为23MAY 不跟年份 先补齐为当前年，然后与当前日期比较，如果补齐后的日期大于等于当前日期，则使用当前年。如果补齐后的日期小于当前日期，则使用当前年的下一年进行补齐
     *                例如：next=1 dateCom=20MAY 今天是2022-05-23 转换后的结果为 2023-05-20
     *                next=0 dateCom=20MAY 今天是2022-05-23 转换后的结果为 2022-05-20
     * @return 标准日期 转换后的结果：【2022-05-23】
     */
    public static String com2ymd(String dateCom, int type) {
        if (!ReUtil.isMatch(PATTERN_COM, dateCom) && !ReUtil.isMatch(PATTERN_SIM_COM, dateCom)) {
            throw new DateException("DATE");
        }
        String ymd = StrUtil.EMPTY;
        if (ReUtil.isMatch(PATTERN_SIM_COM, dateCom)) {
            switch (dateCom) {
                case StrUtil.DOT:
                    //. 今天
                    ymd = DateUtil.today();
                    break;
                case StrUtil.DASHED:
                    //- 昨天
                    ymd = DateUtil.yesterday().toDateStr();
                    break;
                case "+":
                    //+ 明天
                    ymd = DateUtil.tomorrow().toDateStr();
                    break;
                default:
                    break;
            }
        } else if (ReUtil.isMatch(PATTERN_COM, dateCom)) {
            List<String> args = ReUtil.getAllGroups(PATTERN_COM, dateCom, Boolean.FALSE);
            int day = parseInt(args.get(0));
            String monthEn = args.get(1);
            String years = args.get(2);
            int thisYear = DateUtil.thisYear();
            if (StrUtil.isEmpty(years)) {
                switch (type) {
                    case 0:
                        ymd = getYmd(day, monthEn, thisYear);
                        break;
                    case 1:
                        //先补齐为当前年，然后与当前日期比较，如果补齐后的日期大于等于当前日期，则使用当前年。如果补齐后的日期小于当前日期，则使用当前年的下一年进行补齐
                        ymd = getYmd(day, monthEn, thisYear);
                        //日期比较 endDate.compareTo(startDate)结果>0 说明前者晚于后者 前者时间靠后
                        if (DateUtil.today().compareTo(ymd) > 0) {
                            thisYear = thisYear + 1;
                            ymd = getYmd(day, monthEn, thisYear);
                        }
                        break;
                    case 2:
                        //先补齐为当前年，然后与当前日期的前一天比较，如果补齐后的日期大于等于当前日期的前一天，则使用当前年。如果补齐后的日期小于当前日期的前一天，则使用当前年的下一年进行补齐
                        ymd = getYmd(day, monthEn, thisYear);
                        if (offsetDay(DateUtil.date(), -1).toString().compareTo(ymd2YmdhmsStr(ymd)) > 0) {
                            thisYear = thisYear + 1;
                            ymd = getYmd(day, monthEn, thisYear);
                        }
                    default:
                        break;
                }
            } else {
                String yearPre = StrUtil.subPre(Integer.toString(thisYear), 2);
                int year = parseInt(StrUtil.format("{}{}", yearPre, args.get(2)));
                ymd = getYmd(day, monthEn, year);
            }
        }
        return ymd;
    }

    /**
     * @param day     20
     * @param monthEn MAY
     * @param year    2022或者2023
     * @return 转换后的日期：2022-05-20 或者 2023-05-20
     */
    private static String getYmd(int day, String monthEn, int year) {
        String ymd;//英文月份转为数字月份
        int monthNum = Arrays.asList(MONTH_EN).indexOf(monthEn.toUpperCase()) + 1;
        if (monthNum <= 0) {
            throw new DateException("月份输入错误");
        }
        //根据月份年份判断天数，如果输入的天数大于当前月的总天数，则说明日期错误 如 2022-02-29（2022年2月只有28天）
        int montDays = DateUtil.lengthOfMonth(monthNum, DateUtil.isLeapYear(year));
        if (day > montDays || day <= 0) {
            throw new DateException("天数与该月天数不匹配");
        }

        String monthStr = StrUtil.toString(Arrays.asList(MONTH_EN).indexOf(monthEn.toUpperCase()) + 1);
        monthStr = StrUtil.fillBefore(monthStr, '0', 2);
        String dayStr = StrUtil.fillBefore(StrUtil.toString(day), '0', 2);
        ymd = StrUtil.format("{}-{}-{}", year, monthStr, dayStr);
        return ymd;
    }

    /**
     * 获取2个日期时间得所有日期数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 前闭，后闭得日期区间数据
     */
    public static List<String> rangeToList(Date startDate, Date endDate) {
        return DateUtils.rangeToList(startDate, endDate, DateField.DAY_OF_MONTH).stream().map(DateUtils::date2ymd).collect(Collectors.toList());
    }

    /**
     * Title: calculateDate
     * Description: 获取计算跨天后的日期
     *
     * @param flightDate flightDate 2022-10-01
     * @param time       time       0830
     * @param dateChange dateChange +1
     * @return {@link String}
     * <AUTHOR>
     * @date 2022/7/27 15:41
     */
    public static String calculateDate(String flightDate, String time, String dateChange) {
        String resDate = flightDate;
        if (StrUtil.isNotEmpty(dateChange)) {
            DateTime dateTime = DateUtil.parseDate(flightDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(dateTime);
            int dayChange = 0;
            if (dateChange.startsWith(StrUtil.DASHED)) {
                dayChange = -Integer.parseInt(dateChange.substring(1));
            } else if (dateChange.startsWith("+")) {
                dayChange = Integer.parseInt(dateChange.substring(1));
            }
            calendar.add(Calendar.DAY_OF_MONTH, dayChange);
            resDate = DateUtils.date2ymd(calendar.getTime());
        }
        resDate = StrUtil.format("{} {}:{}:00", resDate, time.substring(0, 2), time.substring(2));
        return resDate;
    }


    /**
     * Title: setRetrieveOffTime
     * Description: 设置起飞时间，如果指令无起飞时间且为当天，设置默认的现在时间
     *
     * @param date    date
     * @param offTime offTime
     * @return 设置起飞时间，如果指令无起飞时间且为当天，设置默认的现在时间
     * <AUTHOR>
     * @date 2022/5/6 14:05
     */
    public static String getRetrieveOffTime(String date, String offTime) {
        if (StrUtil.isEmpty(offTime)) {
            String now = DateUtil.now();
            String nowDate = now.substring(0, 10);
            if (nowDate.equals(date)) {
                offTime = now.substring(now.length() - 8, now.length() - 3).replace(StrUtil.COLON, StrUtil.EMPTY);
            }
        }
        return offTime;
    }

    /**
     * 带有中文格式时间格式转换
     *
     * @param dateStr dateStr 5小时59分 默认格式
     * @return 转换之后的格式：默认为：5:59
     */
    public static String normalize(String dateStr) {
        return normalize(dateStr, 0);
    }

    /**
     * 带有中文格式时间格式转换
     *
     * @param dateStr 5小时59分
     * @param type    规定日期的转换规则 参数输入0或者1 默认是0
     *                type =0 时，转为 5:59
     *                type=1 时，转为 5:59:00
     * @return 转换之后的格式：默认为：5:59
     */
    public static String normalize(String dateStr, int type) {
        if (!ReUtil.isMatch(PATTERN_ZH, dateStr)) {
            throw new DateException("DATE");
        }
        List<String> allGroups = ReUtil.getAllGroups(PATTERN_ZH, dateStr);
        String hour = StrUtil.isEmpty(allGroups.get(2)) ? "00" : allGroups.get(2);
        String minute = StrUtil.isEmpty(allGroups.get(4)) ? "00" : allGroups.get(4);
        String seconds = StrUtil.isEmpty(allGroups.get(6)) ? "00" : allGroups.get(6);
        String millisecond = StrUtil.isEmpty(allGroups.get(8)) ? "00" : allGroups.get(8);
        String hm = StrUtil.EMPTY;
        switch (type) {
            case 0:
                hm = StrUtil.format("{}:{}", hour, minute);
                break;
            case 1:
                hm = StrUtil.format("{}:{}:{}", hour, minute, seconds);
                break;
        }
        return hm;
    }

    public static void main(String[] args) {
        String dateStr = "5小时59分50秒1毫秒";
        String normalize = normalize(dateStr, 1);
        System.out.println(normalize);
    }
}

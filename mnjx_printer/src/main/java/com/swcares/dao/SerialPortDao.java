package com.swcares.dao;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.swcares.core.print.PrinterConfiguration;
import gnu.io.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.Enumeration;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class SerialPortDao {

    @Resource
    private PrinterConfiguration printerConfiguration;

    /**
     * 串口打印数据
     *
     * @param tData 要打印的数据
     */
    public void transmitData(String tData) {
        log.info("要打印的数据：{}", tData);
        try {
            // 1、构建打印数据
            byte[] data = ArrayUtil.addAll(new byte[]{0x2}, tData.getBytes(CharsetUtil.GBK), new byte[]{0x3});
            // 2、打印数据
            this.printDataByComPort(tData, data);
        } catch (UnsupportedEncodingException e) {
            log.error("打印发生错误，错误原因：{}", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取可用串口
     * 一般的客户机上的串口数量都是有限的，除特别需要外，一般都是一个，
     * 所以我们这里获取可以用的串口
     *
     * @return 打印串口
     */
    private void printDataByComPort(String tData, byte[] data) {
        // CommPortIdentifier主要用于对串口进行管理和设置，是对串口进行访问控制的核心类
        // 获得当前所有可用端口实例
        Enumeration portIdentifiers = CommPortIdentifier.getPortIdentifiers();

        while (portIdentifiers.hasMoreElements()) {
            log.info("---have element");
            Object element = portIdentifiers.nextElement();
            if (element instanceof CommPortIdentifier) {
                // 主要用于对串口进行管理和设置，是对串口进行访问控制的核心类
                CommPortIdentifier commPortIdentifier = (CommPortIdentifier) element;
                if (!commPortIdentifier.isCurrentlyOwned()) {
                    log.info("---current COM port:{}", commPortIdentifier.getName());
                    if ("1".equals(printerConfiguration.getPrinterConnectionModel())) {
                        if ("Hailian".equals(printerConfiguration.getCategory())) {
                            if ((tData.startsWith("\u0002CP") && commPortIdentifier.getName().equals(printerConfiguration.getCpCom())) || (tData.startsWith("\u0002BTP") && commPortIdentifier.getName().equals(printerConfiguration.getBtpCom()))) {
                                SerialPort serialPort = this.openComPort(commPortIdentifier);
                                if (ObjectUtil.isNotEmpty(serialPort)) {
                                    log.info("---print by appoint COM:{}", commPortIdentifier.getName());
                                    log.info("---print {}", tData.startsWith("\u0002CP") ? "boarding" : "luggage");
                                    this.submitData(serialPort, data);
                                    break;
                                }
                            }
                        } else {
                            if ((tData.startsWith("CP") && commPortIdentifier.getName().equals(printerConfiguration.getCpCom())) || (tData.startsWith("BTP") && commPortIdentifier.getName().equals(printerConfiguration.getBtpCom()))) {
                                SerialPort serialPort = this.openComPort(commPortIdentifier);
                                if (ObjectUtil.isNotEmpty(serialPort)) {
                                    log.info("---print by appoint COM:{}", commPortIdentifier.getName());
                                    log.info("---print {}", tData.startsWith("CP") ? "boarding" : "luggage");
                                    this.submitData(serialPort, data);
                                    break;
                                }
                            }
                        }
                    } else {
                        log.info("---print by auto COM:{}", commPortIdentifier.getName());
                        SerialPort serialPort = this.openComPort(commPortIdentifier);
                        if (ObjectUtil.isNotEmpty(serialPort)) {
                            this.submitData(serialPort, data);
                        }
                    }
                }

            }
        }
    }

    /**
     * 链接到具体的端口对象
     *
     * @param commPortIdentifier 端口建立链接的对象
     * @return 端口对象
     */
    private SerialPort openComPort(CommPortIdentifier commPortIdentifier) {
        log.info("commPortIdentifier----", commPortIdentifier);
        SerialPort serialPort = null;
        try {
            // 打开端口，并给端口名字和一个timeout（打开操作的超时时间）
            // 通讯端口 CommDriver 可负载设备（the loadable device）驱动程序接口的一部分
            /* open方法打开通讯端口，获得一个CommPort对象。它使程序独占端口。
             * 如果端口正被其他应用程序占用，将使用 CommPortOwnershipListener事件机制，传递一个PORT_OWNERSHIP_REQUESTED事件。
             * 每个端口都关联一个 InputStream 和一个OutputStream。
             * 如果端口是用open方法打开的，那么任何的getInputStream都将返回相同的数据流对象，除非有close 被调用。
             * 有两个参数，第一个为应用程序名；第二个参数是在端口打开时阻塞等待的毫秒数。
             */
            CommPort commPort = commPortIdentifier.open(commPortIdentifier.getName(), 2000);
            // 判断是不是串口
            if (commPort instanceof SerialPort) {
                serialPort = (SerialPort) commPort;
                // 设置一下串口的波特率等参数
                // 数据位：8
                // 停止位：1
                // 校验位：None
                serialPort.setSerialPortParams(9600, SerialPort.DATABITS_8, SerialPort.STOPBITS_1, SerialPort.PARITY_NONE);
            }
        } catch (PortInUseException | UnsupportedCommOperationException e) {
            log.error("获取串口发生了异常：{}", e.getMessage());
            e.printStackTrace();
        }
        return serialPort;
    }

    /**
     * 打印数据
     *
     * @param serialPort 打印使用的串口对象
     * @param sData      要打印的字节数组（协议数据）
     */
    private void submitData(SerialPort serialPort, byte[] sData) {
        OutputStream out = null;
        try {
            out = serialPort.getOutputStream();
            out.write(sData);
        } catch (IOException e) {
            log.error("打印发生错误，错误原因：{}", e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (ObjectUtil.isNotNull(out)) {
                    out.close();
                }
            } catch (IOException e) {
                log.error("打印发生错误，错误原因：{}", e.getMessage());
                e.printStackTrace();
            } finally {
                serialPort.close();
            }
        }
    }
}

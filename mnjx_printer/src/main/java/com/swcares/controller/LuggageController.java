package com.swcares.controller;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.service.ILuggageService;
import com.swcares.vo.LuggageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 行李牌
 *
 * <AUTHOR>
 */
@Api(tags = "行李牌打印")
@Slf4j
@RestController
@RequestMapping("/luggage")
public class LuggageController {
    @Resource
    private ILuggageService iLuggageService;

    @ApiOperation("行李牌打印")
    @PostMapping("/print")
    public void print(@RequestBody LuggageVo luggageVo) throws UnifiedResultException {
        iLuggageService.print(luggageVo);
    }
}

package com.swcares.service.impl.dongxing;

import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.swcares.core.protocol.atp.AtpDataHeader;
import com.swcares.core.protocol.atp.AtpDataPayload;
import com.swcares.core.protocol.atp.AtpDataPayloadItem;
import com.swcares.core.utils.DateUtils;
import com.swcares.service.IBoardingService;
import com.swcares.service.impl.BasePrintServiceImpl;
import com.swcares.vo.BoardingVo;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 霍尼韦尔的登机牌打印具体业务逻辑
 *
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(prefix = "printer", name = "category", havingValue = "DongXing")
public class DongXingBoardingServiceImpl extends BasePrintServiceImpl<BoardingVo> implements IBoardingService {

    @Override
    protected String generatePrintData(BoardingVo vo) {
        // 数据流的头部
        String atpDataHeader = AtpDataHeader.toStr();
        // 数据流的主体
        String atpDataPayload = this.getAtpDataPayload(vo);
        // 构建打印数据
        return StrFormatter.format("{}#{}", atpDataHeader, atpDataPayload);
    }

    /**
     * 获得负载的数据
     *
     * @param vo 登机牌数据
     * @return 登机牌对应需要打印的字符串
     */
    private String getAtpDataPayload(BoardingVo vo) {
        List<AtpDataPayloadItem> atpDataPayloadItems = new ArrayList<>();

        // 旅客姓名数据
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("05").content(PinyinUtil.getPinyin(vo.getNameZh(), StrUtil.EMPTY).toUpperCase()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("07").content(vo.getNameZh()).build());

        // 航班数据
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("08").content(vo.getFlightNo().substring(0, 2)).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("10").content(vo.getFlightNo().substring(2)).build());

        // LOGO
//        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("11").content("@12").build());

        // 日期
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("17").content(vo.getFlightDate().substring(5)).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("18").content(vo.getFlightDate().substring(0, 5)).build());

        // 登机时间
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("19").content(Optional.ofNullable(vo.getBoardingTime()).orElse(StrUtil.EMPTY)).build());

        // 座位号
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("20").content(vo.getSeatNo()).build());

        // 登机口
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("21").content(vo.getGate()).build());

        // 值机序号
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("22").content(vo.getBoardingNo()).build());

        // 票号
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("28").content(vo.getTicketPre()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("29").content(vo.getTicketNo()).build());

        // 出发机场
//        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("30").content(PinyinUtil.getPinyin(vo.getFromZh(), StrUtil.EMPTY).toUpperCase()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("31").content(vo.getFrom()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("62").content(vo.getFromZh()).build());

        // 到达机场
//        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("35").content(PinyinUtil.getPinyin(vo.getToZh(), StrUtil.EMPTY).toUpperCase()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("36").content(vo.getTo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("63").content(vo.getToZh()).build());

        // 舱位
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("33").content(vo.getCabin()).build());

        // 厂商设定的额外占座数据，以后需要再进行补充
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("39").content("XXX").build());

        // 条形码
        String barcode = vo.getBarcode();
        String barcodeDate = barcode.substring(barcode.length() - 7);
        String ymd = DateUtils.com2ymd(barcodeDate).replace("-", "");
        barcode = StrUtil.format("{}{}", barcode.substring(0, 9), ymd);
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("61").content(barcode).build());

        return AtpDataPayload.builder().atpDataPayloadItems(atpDataPayloadItems).build().toStr();
    }
}

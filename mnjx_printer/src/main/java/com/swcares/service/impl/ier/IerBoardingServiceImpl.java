package com.swcares.service.impl.ier;

import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.swcares.core.protocol.atp.AtpDataHeader;
import com.swcares.core.protocol.atp.AtpDataPayload;
import com.swcares.core.protocol.atp.AtpDataPayloadItem;
import com.swcares.core.utils.DateUtils;
import com.swcares.service.IBoardingService;
import com.swcares.service.impl.BasePrintServiceImpl;
import com.swcares.vo.BoardingVo;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 霍尼韦尔的登机牌打印具体业务逻辑
 *
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(prefix = "printer", name = "category", havingValue = "IER")
public class IerBoardingServiceImpl extends BasePrintServiceImpl<BoardingVo> implements IBoardingService {

    @Override
    protected String generatePrintData(BoardingVo vo) {
        // 数据流的头部
        String atpDataHeader = AtpDataHeader.toStr();
        // 数据流的主体
        String atpDataPayload = this.getAtpDataPayload(vo);
        // 构建打印数据
        return StrFormatter.format("{}#{}", atpDataHeader, atpDataPayload);
    }

    /**
     * 获得负载的数据
     *
     * @param vo 登机牌数据
     * @return 登机牌对应需要打印的字符串
     */
    private String getAtpDataPayload(BoardingVo vo) {
        List<AtpDataPayloadItem> atpDataPayloadItems = new ArrayList<>();
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("02").content(vo.getNameZh()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("03").content(PinyinUtil.getPinyin(vo.getNameZh(), StrUtil.EMPTY).toUpperCase()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("04").content(vo.getFlightNo().substring(0, 2)).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("05").content(vo.getFlightNo().substring(2)).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("06").content(vo.getFrom()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("07").content(vo.getFromZh()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("08").content(vo.getTo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("09").content(vo.getToZh()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("10").content(vo.getFlightDate()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("11").content(vo.getBoardingNo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("12").content(vo.getCabin()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("13").content(vo.getFrequentNo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("14").content(vo.getGate()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("15").content(Optional.ofNullable(vo.getBoardingTime()).orElse(StrUtil.EMPTY)).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("16").content(vo.getSeatNo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("17").content(vo.getTicketNo()).build());
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("18").content(vo.getTicketPre()).build());
        // 条形码
        String barcode = vo.getBarcode();
        String barcodeDate = barcode.substring(barcode.length() - 7);
        String ymd = DateUtils.com2ymd(barcodeDate).replace("-", "");
        barcode = StrUtil.format("{}{}", barcode.substring(0, 9), ymd);
        atpDataPayloadItems.add(AtpDataPayloadItem.builder().itemNo("19").content(barcode).build());
        return AtpDataPayload.builder().atpDataPayloadItems(atpDataPayloadItems).build().toStr();
    }
}

package com.swcares.service.impl.dongxing;

import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.swcares.core.protocol.btp.BtpDataHeader;
import com.swcares.core.protocol.btp.BtpDataPayload;
import com.swcares.core.protocol.btp.BtpDataPayloadItem;
import com.swcares.service.ILuggageService;
import com.swcares.service.impl.BasePrintServiceImpl;
import com.swcares.vo.LuggageSegVo;
import com.swcares.vo.LuggageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@ConditionalOnProperty(prefix = "printer", name = "category", havingValue = "DongXing")
@Slf4j
public class DongXingIerLuggageServiceImpl extends BasePrintServiceImpl<LuggageVo> implements ILuggageService {

    @Override
    protected String generatePrintData(LuggageVo vo) {
        // 数据流的头部
        String btpDataHeader = BtpDataHeader.toStr();
        // 数据流的主体
        String btpDataPayload = this.getBtpDataPayload(vo);
        // 构建打印数据
        return StrFormatter.format("{}#{}", btpDataHeader, btpDataPayload);
    }

    private String getBtpDataPayload(LuggageVo vo) {
        List<BtpDataPayloadItem> btpDataPayloadItems = new ArrayList<>();
        // 中文拼音项
        constructLuggageData(btpDataPayloadItems,
                "01", PinyinUtil.getPinyin(vo.getName(), StrUtil.EMPTY).toUpperCase(),
                "02", vo.getBagCnt(),
                "03", vo.getBagWeight(),
                "06", vo.getBagNoAirlineCode(),
                "08", vo.getBagAgent()
        );
        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo("09").content(vo.getBagNoAirlineSerial()).build());
        // 登机序号
        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo("0A").content(vo.getSeqNo()).build());
        // 条码
//        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo("99").content(vo.getBarCode()).build());
        // 获得所有的航班
        List<LuggageSegVo> luggageSegVos = vo.getLuggageSegVos();
        // 获取最小序号的航班信息，最大序号的航班信息就是目的地
        LuggageSegVo luggageSegVoFrom = luggageSegVos.stream().min(Comparator.comparingInt(o -> Integer.parseInt(o.getSerialNo()))).get();
        if (ObjectUtil.isNotNull(luggageSegVoFrom)) {
            btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo("80").content(luggageSegVoFrom.getFlightNo().substring(0, 2)).build());
            btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo("81").content(luggageSegVoFrom.getFlightNo().substring(2)).build());
            btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo("82").content(luggageSegVoFrom.getFlightDate()).build());
            btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo("85").content(luggageSegVoFrom.getOrg()).build());
        }
        // 获取最大序号的航班信息，最大序号的航班信息就是目的地
        LuggageSegVo luggageSegVoTo = luggageSegVos.stream().max(Comparator.comparingInt(o -> Integer.parseInt(o.getSerialNo()))).get();
        String fltDate = luggageSegVoTo.getFlightDate();
        if (StrUtil.isNotBlank(fltDate)) {
            if (fltDate.length() >= 5) {
                fltDate = fltDate.substring(0, 5);
            }
        }
        log.info("IRE行李中航班日期是：" + fltDate);
        // 目的地航班
        if (ObjectUtil.isNotNull(luggageSegVoTo)) {
            constructLuggageData(btpDataPayloadItems,
                    "10", luggageSegVoTo.getFlightNo().substring(0, 2),
                    "11", luggageSegVoTo.getFlightNo().substring(2),
                    "12", fltDate,
                    "15", luggageSegVoTo.getDst(),
                    "16", PinyinUtil.getPinyin(luggageSegVoTo.getDstZh(), StrUtil.EMPTY).toUpperCase()
            );
        }
        log.info("--------------------------第一次打印start---------------------");
        log.info(btpDataPayloadItems.toString());
        log.info("--------------------------第一次打印end-----------------------");
        // 确定中转地 （1是单程，没得中转；2是有一个中转；3是有两个中转）
        int i = Integer.parseInt(luggageSegVoTo.getSerialNo()) - Integer.parseInt(luggageSegVoFrom.getSerialNo());
        switch (i) {
            case 1:
                constructLuggageData(btpDataPayloadItems,
                        "20", luggageSegVoFrom.getFlightNo().substring(0, 2),
                        "21", luggageSegVoFrom.getFlightNo().substring(2),
                        "22", luggageSegVoFrom.getFlightDate(),
                        "25", luggageSegVoFrom.getDst(),
                        "26", PinyinUtil.getPinyin(luggageSegVoFrom.getDstZh(), StrUtil.EMPTY).toUpperCase()
                );
                break;
            case 2:
                constructLuggageData(btpDataPayloadItems,
                        "30", luggageSegVoFrom.getFlightNo().substring(0, 2),
                        "31", luggageSegVoFrom.getFlightNo().substring(2),
                        "32", luggageSegVoFrom.getFlightDate(),
                        "35", luggageSegVoFrom.getDst(),
                        "36", PinyinUtil.getPinyin(luggageSegVoFrom.getDst(), StrUtil.EMPTY).toUpperCase()
                );
                // 排序，预防顺序引起的错误
                luggageSegVos.sort(Comparator.comparingInt(o -> Integer.parseInt(o.getSerialNo())));
                LuggageSegVo luggageSegVoMid = luggageSegVos.get(Integer.parseInt(luggageSegVoFrom.getSerialNo()));
                constructLuggageData(btpDataPayloadItems,
                        "20", luggageSegVoMid.getFlightNo().substring(0, 2),
                        "21", luggageSegVoMid.getFlightNo().substring(2),
                        "22", luggageSegVoMid.getFlightDate(),
                        "25", luggageSegVoMid.getDst(),
                        "26", PinyinUtil.getPinyin(luggageSegVoMid.getDstZh(), StrUtil.EMPTY).toUpperCase()
                );
                break;
            default:
                break;
        }
        log.info("--------------------------第二次打印start---------------------");
        log.info(btpDataPayloadItems.toString());
        log.info("--------------------------第二次打印end-----------------------");
        return BtpDataPayload.builder().btpDataPayloadItems(btpDataPayloadItems).build().toStr();
    }

    private void constructLuggageData(List<BtpDataPayloadItem> btpDataPayloadItems,
                                      String k1, String airline,
                                      String k2, String flightNo,
                                      String k3, String flightDate,
                                      String k4, String dst,
                                      String k5, String pinyin) {
        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo(k1).content(airline).build());
        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo(k2).content(flightNo).build());
        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo(k3).content(flightDate).build());
        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo(k4).content(dst).build());
        btpDataPayloadItems.add(BtpDataPayloadItem.builder().itemNo(k5).content(pinyin).build());
    }
}

#服务器部署资料
##说明
    - 长春的是使用ToDest远程连接到跳板机，跳板机再链接服务器
    - 使用xshell连接服务器的地址，服务器地址：
    - Mysql 数据库，采用Dockerr容器化部署
    
##服务器地址列表
    ```
    IP **************
    用户名 root
    密码是 Kaiya@2Xu#$
    ```

###mysql数据库服务器：
####地址
    参照mysql部署脚本
####ssh
    参照mysql部署脚本
####mysql用户
参照mysql部署脚本

##操作步骤
    在进行这个操作前，先要将所有部署脚本的
        数据库地址、数据库名称、数据库账户名、数据库密码
    修改正确（就是修改为你要部署的环境地址）
###1、备份老的数据库
   
###2、查看目前的容器情况
    
###3、删除mnjx数据内部的表对象
   
###4、拷贝数据库初始化文件（使用我们提供的文件）
   
###5、拷贝部署脚本
    1、将当前文件夹下的除部署说明.md外的所有文件拷贝到/deploy文件夹下
    2、到/deploy下执行chmod -R 744 /deploy
    3、执行脚本（这个根据自己情况来）
### 6、关于Docker-compose 
- 以前使用sudo curl -L "https://get.daocloud.io/docker/compose/releases/download/1.27.4/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose拉下docker-compose
- 目前`https://get.daocloud.io`这个地址网址打不开，建议使用离线安装方式，离线版本的Docker-compose gitlab上，可以直接拉取下来上传到服务器
- 可以直接使用当前文件夹下的`Docker-compose`文件
### 7、本次部署的分支即镜像

- 分支：v4.2.0-2023-06-06

### 8、Mysql 部署
mysql部署获取通用镜像版本

http://**************/sts/#/dashboard
http://**************:8380/mnjx_job/
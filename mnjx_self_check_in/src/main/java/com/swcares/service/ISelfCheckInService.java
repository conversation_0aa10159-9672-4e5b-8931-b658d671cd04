package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.dto.selfCheckIn.CheckInDto;
import com.swcares.obj.dto.selfCheckIn.DetailTripDto;
import com.swcares.obj.dto.selfCheckIn.SeatMapDto;
import com.swcares.obj.dto.selfCheckIn.TripDto;
import com.swcares.obj.vo.PassengerTripVo;
import com.swcares.obj.vo.selfCheckIn.BoardingVo;
import com.swcares.obj.vo.selfCheckIn.SeatMapVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 10:16
 */
public interface ISelfCheckInService {

    /**
     * 获取旅客行程列表
     * @param TripDto tripDto
     * @return
     * @throws UnifiedResultException
     */
    List<PassengerTripVo> getPassengerTripList(TripDto tripDto) throws UnifiedResultException;

    /**
     * 查询旅客行程详情
     * @param detailTripDto
     * @return
     */
    PassengerTripVo getDetailPassengerTrip(DetailTripDto detailTripDto) throws UnifiedResultException;

    /**
     * 值机
     * @param checkInDto
     * @return
     * @throws UnifiedResultException
     */
    List<BoardingVo> checkIn(CheckInDto checkInDto) throws UnifiedResultException;

    /**
     * 查询座位图
     * @param seatMapDto
     * @return
     */
    List<SeatMapVo> retrieveSeatMap(SeatMapDto seatMapDto) throws UnifiedResultException;
}

package com.swcares.obj.dto.selfCheckIn;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/18 9:19
 */
@Data
@Api("查询详情参数")
public class TripDto {

    @ApiModelProperty(value = "身份证号")
    @NotNull(message = "身份证号不能为空")
    private String idCard;

    @ApiModelProperty(value = "姓名")
    private String inputName;
}

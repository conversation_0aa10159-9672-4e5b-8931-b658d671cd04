package com.swcares.core.unified;


import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
/**
 * 统一结果处理器
 * 捕捉所有controller的返回结果，并且全部封装成统一的格式，
 *
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class UnifiedResultHandler implements ResponseBodyAdvice<Object> {
    /**
     * 确定对哪些进行拦截处理
     *
     * @param returnType    controller返回的类型
     * @param converterType 目前不知道这个参数是什么意思
     * @return 确定对哪些类型是否需要进行转换进行判断
     */
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // controller返回类型的字节码
        Class<?> parameterType = returnType.getParameterType();
        // ResponseEntity 一般是文件下载的时候使用，这个地方返回的可能是流数据
        return UnifiedResult.class != parameterType && ResponseEntity.class != parameterType;
    }

    /**
     * 返回结果封装
     *
     * @param body                  要封装的原始对象
     * @param returnType            要封装的对象类型
     * @param selectedContentType   目前不知道什么意思
     * @param selectedConverterType 目前不知道什么意思
     * @param request               请求
     * @param response              返回
     * @return 封装结果
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        response.getHeaders().set("Set-Cookie","name=value;HttpOnly");
        // 如果方法没得返回，即是void类型
        final Class<?> type = returnType.getParameterType();
        UnifiedResult<Object> ok = UnifiedResult.ok();
        if (void.class == type) {
            return ok;
        }

        // 如果在controller端自己手动封装了，那就直接返回了
        if (UnifiedResult.class == type) {
            return body;
        }

        // 默认情况下都是要封装的
        ok.setData(body);
        return ok;
    }
}

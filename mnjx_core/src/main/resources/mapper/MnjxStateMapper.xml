<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxStateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxState">
        <id column="state_id" property="stateId" />
        <result column="country_id" property="countryId" />
        <result column="state_code" property="stateCode" />
        <result column="state_ename" property="stateEname" />
        <result column="state_cname" property="stateCname" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        state_id, country_id, state_code, state_ename, state_cname, status
    </sql>

</mapper>

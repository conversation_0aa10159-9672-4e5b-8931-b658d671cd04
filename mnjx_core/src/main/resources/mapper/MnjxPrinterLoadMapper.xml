<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPrinterLoadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPrinterLoad">
        <result column="printer_load_id" property="printerLoadId" />
        <result column="loader_si_id" property="loaderSiId" />
        <result column="load_date" property="loadDate" />
        <result column="printer_id" property="printerId" />
        <result column="ticket_start" property="ticketStart" />
        <result column="ticket_end" property="ticketEnd" />
        <result column="unloader_si_id" property="unloaderSiId" />
        <result column="unloader_date" property="unloaderDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        printer_load_id, loader_si_id, load_date, printer_id, ticket_start, ticket_end, unloader_si_id, unloader_date
    </sql>

</mapper>

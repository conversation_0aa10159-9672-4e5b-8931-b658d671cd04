<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxAirportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxAirport">
        <id column="airport_id" property="airportId"/>
        <result column="city_id" property="cityId"/>
        <result column="airport_code" property="airportCode"/>
        <result column="airport_cname" property="airportCname"/>
        <result column="airport_ename" property="airportEname"/>
        <result column="latitude" property="latitude"/>
        <result column="longitude" property="longitude"/>
        <result column="airport_status" property="airportStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        airport_id, city_id, airport_code, airport_cname, airport_ename, latitude, longitude, airport_status
    </sql>

</mapper>

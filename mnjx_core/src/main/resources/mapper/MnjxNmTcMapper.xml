<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxNmTcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxNmTc">
        <id column="nm_tc_id" property="nmTcId" />
        <result column="pnr_nm_id" property="pnrNmId" />
        <result column="pnr_index" property="pnrIndex" />
        <result column="tc_info" property="tcInfo" />
        <result column="input_value" property="inputValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        nm_tc_id, pnr_nm_id, pnr_index, tc_info, input_value
    </sql>

</mapper>

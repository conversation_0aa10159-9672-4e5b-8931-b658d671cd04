<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxFlightMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxFlight">
        <id column="flight_id" property="flightId"/>
        <result column="airline_id" property="airlineId"/>
        <result column="flight_no" property="flightNo"/>
        <result column="flight_status" property="flightStatus"/>
        <result column="is_e" property="isE"/>
        <result column="is_a" property="isA"/>
        <result column="is_x" property="isX"/>
        <result column="is_w" property="isW"/>
        <result column="allow_asr" property="allowAsr"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        flight_id, airline_id, flight_no, flight_status, is_e, is_a, is_x, is_w, allow_asr
    </sql>
    
</mapper>

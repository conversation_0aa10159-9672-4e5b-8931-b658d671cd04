<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrTcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrTc">
        <id column="pnr_tc_id" property="pnrTcId" />
        <result column="pnr_id" property="pnrId" />
        <result column="pnr_index" property="pnrIndex" />
        <result column="tc_info" property="tcInfo" />
        <result column="input_value" property="inputValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        pnr_tc_id, pnr_id, pnr_index, tc_info, input_value
    </sql>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxStandardPatMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxStandardPat">
        <id column="standard_pat_id" property="standardPatId" />
        <result column="org_city_id" property="orgCityId" />
        <result column="dst_city_id" property="dstCityId" />
        <result column="distance_pirce" property="distancePrice" />
        <result column="distance" property="distance" />
        <result column="currency_code" property="currencyCode" />
        <result column="normal_pat" property="normalPat" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        standard_pat_id, org_city_id, dst_city_id, distance_pirce, distance, currency_code,normal_pat
    </sql>

</mapper>

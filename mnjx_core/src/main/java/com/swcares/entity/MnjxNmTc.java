package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_nm_tc")
@ApiModel(value="MnjxNmTc对象", description="")
public class MnjxNmTc extends Model<MnjxNmTc> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "NMTC ID")
    @TableId(value = "nm_tc_id", type = IdType.ASSIGN_ID)
    private String nmTcId;

    @TableField("pnr_nm_id")
    private String pnrNmId;

    @TableField("pnr_index")
    private Integer pnrIndex;

    @TableField("tc_info")
    private String tcInfo;

    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.nmTcId;
    }

}

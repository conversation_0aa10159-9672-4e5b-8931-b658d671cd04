package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_ticket_price")
@ApiModel(value="MnjxTicketPrice对象", description="")
public class MnjxTicketPrice extends Model<MnjxTicketPrice> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ticket_price_id", type = IdType.ASSIGN_ID)
    private String ticketPriceId;

    @TableField("ticket_no")
    private String ticketNo;

    @TableField("pay_type")
    private String payType;

    @TableField("fc_info")
    private String fcInfo;

    @TableField("fn_info")
    private String fnInfo;

    @TableField("seg_info")
    private String segInfo;

    @Override
    protected Serializable pkVal() {
        return this.ticketPriceId;
    }

}

package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_weight")
public class MnjxWeight extends Model<MnjxWeight> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "weight_id", type = IdType.ASSIGN_ID)
    private String weightId;

    @ApiModelProperty(value = "标准重量表号")
    @TableField("weight_no")
    private String weightNo;

    @ApiModelProperty(value = "航空公司二字码")
    @TableField("airline_code")
    private String airlineCode;

    @ApiModelProperty(value = "单位重量")
    @TableField("unit_weight")
    private String unitWeight;

    @ApiModelProperty(value = "是否要求识别旅客性别：GN，要求识别旅客性别，并输入其交运的行李件数和重量")
    @TableField("is_gender")
    private String isGender;

    @ApiModelProperty(value = "成人男性标准重量：M80（单位公斤")
    @TableField("male_standard")
    private String maleStandard;

    @ApiModelProperty(value = "成人女性标准重量：F70（单位公斤）")
    @TableField("female_standard")
    private String femaleStandard;

    @ApiModelProperty(value = "成人标准重量：P75（单位公斤）")
    @TableField("adult_standard")
    private String adultStandard;

    @ApiModelProperty(value = "小孩标准重量：C38（单位公斤）")
    @TableField("child_standard")
    private String childStandard;

    @ApiModelProperty(value = "婴儿标准重量：I10（单位公斤）")
    @TableField("baby_standard")
    private String babyStandard;

    @ApiModelProperty(value = "是否必须输入标准重量：BMAN，必须输入行李重量，默认为空")
    @TableField("is_standard_weight")
    private String isStandardWeight;

    @ApiModelProperty(value = "是否可选择输入手提行李：H0,要求输入手提行李，默认为空")
    @TableField("is_bag")
    private String isBag;


    @Override
    protected Serializable pkVal() {
        return this.weightId;
    }

}

package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_seat_model")
public class MnjxSeatModel extends Model<MnjxSeatModel> {

    @ApiModelProperty(value = "ID")
    @TableId(value = "seat_model_id", type = IdType.ASSIGN_ID)
    private String seatModelId;

    @ApiModelProperty(value = "CND ID")
    @TableField("cnd_id")
    private String cndId;

    @ApiModelProperty(value = "舱等")
    @TableField("cabin_class")
    private String cabinClass;

    @ApiModelProperty(value = "座位号")
    @TableField("seat_no")
    private String seatNo;

    @ApiModelProperty(value = "座位状态")
    @TableField("seat_status")
    private String seatStatus;

    @ApiModelProperty(value = "座位排号")
    @TableField("seat_row")
    private String seatRow;

    @ApiModelProperty(value = "座位列号")
    @TableField("seat_column")
    private String seatColumn;

    @ApiModelProperty(value = "横坐标")
    @TableField("seat_x")
    private Integer seatX;

    @ApiModelProperty(value = "纵坐标")
    @TableField("seat_y")
    private Integer seatY;

    @ApiModelProperty(value = "坐标上填充的字符")
    @TableField("coordinate_string")
    private String coordinateString;

    @ApiModelProperty(value = "是否靠窗，0不是，1是")
    @TableField("is_window")
    private String isWindow;

    @ApiModelProperty(value = "是否靠过道，0不是，1是")
    @TableField("is_aisle")
    private String isAisle;

    @ApiModelProperty(value = "是否靠前，0不是，1是")
    @TableField("is_forward")
    private String isForward;

    @ApiModelProperty(value = "是否靠后，0不是，1是")
    @TableField("is_after")
    private String isAfter;

    @ApiModelProperty(value = "是否靠右，0不是，1是")
    @TableField("is_right")
    private String isRight;

    @ApiModelProperty(value = "是否靠左，0不是，1是")
    @TableField("is_left")
    private String isLeft;

    @Override
    protected Serializable pkVal() {
        return this.seatModelId;
    }
}

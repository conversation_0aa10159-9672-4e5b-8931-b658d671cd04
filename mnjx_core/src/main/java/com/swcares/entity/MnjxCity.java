package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 * 城市表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_city")
public class MnjxCity extends Model<MnjxCity> {

    @ApiModelProperty(value = "城市ID")
    @TableId(value = "city_id", type = IdType.ASSIGN_ID)
    private String cityId;

    @ApiModelProperty(value = "国家id", example = "1493113961565282306")
    @TableField("country_id")
    private String countryId;

    @ApiModelProperty(value = "州id", example = "1493113961565282301")
    @TableField("state_id")
    private String stateId;

    @ApiModelProperty(value = "城市中文名", example = "重庆")
    @TableField("city_cname")
    @NotBlank(message = "城市中文名不能为空")
    private String cityCname;

    @ApiModelProperty(value = "城市英文名", example = "CHONGQING")
    @TableField("city_ename")
    private String cityEname;

    @ApiModelProperty(value = "城市代码", example = "CKG")
    @NotBlank(message = "城市代码不能为空")
    @Size(max = 3, message = "城市代码长度不合法，最大长度为3位")
    @TableField("city_code")
    private String cityCode;

    @ApiModelProperty(value = "城市缩写", example = "00")
    @Size(max = 2, message = "城市缩写长度不合法，最大长度为2位")
    @TableField("city_abbr")
    private String cityAbbr;

    @ApiModelProperty(value = "0 国际 1国内", example = "0")
    @TableField("city_type")
    private String cityType;

    @TableField("city_status")
    @ApiModelProperty(value = "状态 1，启用 0，停用 ", example = "1")
    private String cityStatus;

    @Override
    protected Serializable pkVal() {
        return this.cityId;
    }

}

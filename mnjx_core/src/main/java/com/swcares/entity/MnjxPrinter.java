package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_printer")
public class MnjxPrinter extends Model<MnjxPrinter> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "printer_id", type = IdType.ASSIGN_ID)
    private String printerId;

    @ApiModelProperty(value = "打票机所属OFFICE ID")
    @NotBlank(message = "打票机所属officeId不能为空")
    @TableField("office_id")
    private String officeId;

    @ApiModelProperty(value = "打票机编号")
    @NotBlank(message = "打票机编号不能为空")
    @TableField("printer_no")
    private String printerNo;

    @ApiModelProperty(value = "打票机PID")
    @NotBlank(message = "打票机PID不能为空")
    @TableField("printer_pid")
    private String printerPid;

    @ApiModelProperty(value = "建控了当前打票机的工作号ID")
    @TableField("si_id")
    private String siId;

    @ApiModelProperty(value = "打票机出票状态:打票机的出票状态有两种UP和DOWN UP时才可以打票")
    @NotBlank(message = "打票机出票状态不能为空")
    @TableField("printer_status")
    private String printerStatus;

    @ApiModelProperty(value = "属性")
    @TableField("print_attribute")
    private String printAttribute;

    @ApiModelProperty(value = "输入状态: 打票机是否允许将需要打印的客票送入打印队列 ACTIVE:工作状态 , INACTIVE:非工作状态")
    @TableField("input_status")
    private String inputStatus;

    @ApiModelProperty(value = "输出状态: ACTIVE:工作状态。  INACTIVE:非工作状态。")
    @TableField("output_status")
    private String outputStatus;

    @ApiModelProperty(value = "DEMAND")
    @TableField("printer_mode")
    private String printerMode;

    @ApiModelProperty(value = "1：航空公司国际客票。2：BSP国际客票。3：航空公司国内客票。4：BSP国内客票。")
    @NotBlank(message = "打票机类型")
    @TableField("printer_type")
    private String printerType;

    @ApiModelProperty(value = "打印方式")
    @TableField("print_way")
    private String printWay;

    @ApiModelProperty(value = "允许接收货币类型：CNY2：CNY表示货币；2表示货币所要求保留的小数点位数")
    @TableField("currency_type")
    private String currencyType;

    @ApiModelProperty(value = "票证归属")
    @TableField("airline")
    private String airline;

    @ApiModelProperty(value = "本机已上票起始票号，10位")
    @TableField("ticket_start")
    private BigInteger ticketStart;

    @ApiModelProperty(value = "本机已上票终止票号，10位，一个打票机一次只能上一个BSP票号段，号数不能超过500张")
    @TableField("ticket_end")
    private BigInteger ticketEnd;

    @ApiModelProperty(value = "上一张票号")
    @TableField("last_ticket")
    private BigInteger lastTicket;

    @Override
    protected Serializable pkVal() {
        return this.printerId;
    }

}

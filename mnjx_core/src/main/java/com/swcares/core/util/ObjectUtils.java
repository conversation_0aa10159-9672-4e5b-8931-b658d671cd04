package com.swcares.core.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ObjectUtils extends ObjectUtil {
    /**
     * 使用这个方法有个要求就是字段名要一直
     *
     * @param vos         导入的数据对象
     * @param targetClazz 目标对象
     * @param <P>         vo类型
     * @param <T>         Entity类型
     * @return 使用这个方法有个要求就是字段名要一直
     */
    public static <P, T> List<T> vo2Entity(List<P> vos, Class<T> targetClazz) {
        return vos.stream().map(vo -> {
            T instance = null;
            try {
                instance = targetClazz.newInstance();
                BeanUtil.copyProperties(vo, instance);
            } catch (InstantiationException | IllegalAccessException e) {
                e.printStackTrace();
            }
            return instance;
        }).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    public static <P, T> T vo2Entity(P vo, Class<T> clazz) throws IllegalAccessException, InstantiationException {
        T instance = clazz.newInstance();
        BeanUtil.copyProperties(vo, instance);
        return instance;
    }


    /**
     * 将空字符转成“”
     *
     * @param <P>   泛型
     * @param obj   原有对象
     * @param value 修改的值
     * @return 将空字符转成“”
     * @throws IllegalAccessException 转换异常
     */
    public static <P> P vo2EntityToValue(P obj, String value) throws IllegalAccessException {
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        if (ArrayUtil.isNotEmpty(declaredFields)) {
            for (Field f : declaredFields) {
                f.setAccessible(true);
                if (f.getType().getTypeName().equals(String.class.getTypeName())
                        && !Modifier.isTransient(f.getModifiers())
                        && f.get(obj) == null) {
                    value = StrUtil.isEmpty(value) ? StrUtil.EMPTY : value;
                    f.set(obj, value);
                }
            }
        }
        return obj;
    }


}

package com.swcares.util;

import java.awt.Font;
import java.util.Enumeration;

import javax.swing.UIManager;
import javax.swing.plaf.FontUIResource;
/**
 * description：SwappUtil <br>
 * <AUTHOR> <br>
 * date 2023/07/06 <br>
 * @version v1.0 <br>
 */
public class SwappUtil {

	/**
	 * @title：InitGlobalFont <br>
	 * @description：设置字体<br>
	 * @param font <br>
	 * <AUTHOR> <br>
	 * @date 2023/06/28 <br>
	 */
	public static void initGlobalFont(Font font) {
		FontUIResource fontRes = new FontUIResource(font);
		for (Enumeration<Object> keys = UIManager.getDefaults().keys(); keys.hasMoreElements();) {
			Object key = keys.nextElement();
			Object value = UIManager.get(key);
			if (value instanceof FontUIResource) {
				UIManager.put(key, fontRes);
			}
		}
	}
	
}

package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.obj.vo.LuggageCheckVo;
import com.swcares.service.ILuggageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "开包行李管理接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RequestMapping(value = "/luggage")
@RestController
public class LuggageController {

    @Resource
    private ILuggageService luggageService;

    @ApiOperation("根据扫描枪录入行李信息查询行李")
    @GetMapping("/checkLuggage/{luggageInfo}")
    public UnifiedResult checkLuggage(@PathVariable String luggageInfo) {
        try {
            LuggageCheckVo checkLuggage = luggageService.checkLuggage(luggageInfo);
            return UnifiedResult.ok("成功", checkLuggage);
        } catch (UnifiedResultException e) {
            return UnifiedResult.fail(200, e.getMessage());
        }
    }

    @ApiOperation("添加行李信息")
    @PostMapping("/addLuggage")
    public UnifiedResult addLuggage(@RequestBody LuggageCheckVo luggageCheckVo) throws UnifiedResultException {
        luggageService.addLuggage(luggageCheckVo);
        return UnifiedResult.ok(Constant.CREATE_SUCCESS);
    }

    @ApiOperation("修改行李备注")
    @PutMapping("/updateRemark")
    public UnifiedResult updateRemark(@RequestBody LuggageCheckVo luggageCheckVo) {
        try {
            luggageService.updateRemark(luggageCheckVo);
        } catch (UnifiedResultException e) {
            return UnifiedResult.fail(200, e.getMessage());
        }
        return UnifiedResult.ok(Constant.UPDATE_SUCCESS);
    }

    @ApiOperation("修改行李状态")
    @PutMapping("/updateStatus")
    public UnifiedResult updateStatus(@RequestBody List<LuggageCheckVo> luggageCheckVos) {
        try {
            luggageService.updateStatus(luggageCheckVos);
        } catch (UnifiedResultException e) {
            return UnifiedResult.fail(200, e.getMessage());
        }
        return UnifiedResult.ok(Constant.UPDATE_SUCCESS);
    }

    @ApiOperation("分页查询开包行李信息")
    @PostMapping("/queryPage/{current}/{limit}")
    public IPage<LuggageCheckVo> queryPage(@PathVariable long current,
                                           @PathVariable long limit,
                                           @RequestBody LuggageCheckVo luggageCheckVo) {
        return luggageService.queryPage(new Page<>(current, limit), luggageCheckVo);
    }

    @ApiOperation("根据状态查询开包行李信息")
    @GetMapping("/queryAbnormal")
    public List<LuggageCheckVo> queryAbnormal(@RequestParam String status) {
        return luggageService.queryAbnormal(status);
    }
}

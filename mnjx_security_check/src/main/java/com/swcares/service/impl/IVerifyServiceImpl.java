package com.swcares.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.swcares.entity.MnjxVerifyInfo;
import com.swcares.obj.vo.VerifyInfoVo;
import com.swcares.service.IMnjxVerifyInfoService;
import com.swcares.service.IVerifyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class IVerifyServiceImpl implements IVerifyService {

    @Resource
    IMnjxVerifyInfoService verifyInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addVerifyInfo(VerifyInfoVo verifyInfoVo) {
        MnjxVerifyInfo save = new MnjxVerifyInfo();
        BeanUtil.copyProperties(verifyInfoVo, save);
        save.setVerifyTime(DateUtil.now());
        verifyInfoService.save(save);
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.LuggageMapper">
    <select id="queryLuggage" resultType="com.swcares.obj.vo.LuggageCheckVo">
        select ml.luggage_no,
               ifnull(mps.carrier_flight, mps.flight_no) as flight_no,
               mps.flight_date,
               mpn.name                                  as nm_name,
               ml.luggage_id,
               mps.pnr_seg_no,
               ml.bag_seg_no,
               mpc.sell_cabin
        from mnjx_pnr_nm mpn
                 left join mnjx_pnr mp on
            mpn.pnr_id = mp.pnr_id
                 left join mnjx_pnr_seg mps on
            mps.pnr_id = mp.pnr_id
                 left join mnjx_luggage ml on
            ml.pnr_nm_id = mpn.pnr_nm_id
                 left join mnjx_psg_cki mpc on
            mpc.pnr_nm_id = mpn.pnr_nm_id and mps.pnr_seg_no = mpc.pnr_seg_no
        where ml.luggage_no = #{luggageNo}
          and mps.org = #{org}
          and mps.dst = #{dst}
          and ml.luggage_type = 1
          and ml.is_del is null
          and mps.flight_date = #{today}
    </select>

    <select id="queryPage" resultType="com.swcares.obj.vo.LuggageCheckVo">
        select
        mui.unpack_id, mui.luggage_id, mui.luggage_no, mui.nm_name, mui.flight_no, mui.flight_date, mui.status,
        mui.remark, mui.sell_cabin
        from
        mnjx_unpack_info mui
        where
        1=1
        <if test="luggageNo !=null and luggageNo != ''">
            and mui.luggage_no like concat('%',#{luggageNo},'%')
        </if>
        <if test="nmName !=null and nmName != ''">
            and mui.nm_name like concat('%',#{nmName},'%')
        </if>
        <if test="status !=null and status != ''">
            and mui.status = #{status}
        </if>
        <if test="flightDate !=null and flightDate != ''">
            and mui.flight_date = #{flightDate}
        </if>
    </select>

    <select id="queryAbnormal" resultType="com.swcares.obj.vo.LuggageCheckVo">
        select mui.unpack_id,
               mui.luggage_id,
               mui.luggage_no,
               mui.nm_name,
               mui.flight_no,
               mui.flight_date,
               mui.status,
               mui.remark,
               mui.sell_cabin
        from mnjx_unpack_info mui
        where mui.status = #{status}
          and mui.flight_date = #{today}
    </select>
</mapper>
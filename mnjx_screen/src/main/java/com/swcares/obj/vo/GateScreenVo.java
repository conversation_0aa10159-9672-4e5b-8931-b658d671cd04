package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2022-12-12 10:42:43
 */
@Api(tags = "登机屏显返回对象")
@Data
public class GateScreenVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班状态中文显示")
    private String flightStatusCname;
    @ApiModelProperty(value = "航班状态英文显示")
    private String flightStatusEname;
    @ApiModelProperty(value = "经停/目的地")
    private List<String> citys;
    @ApiModelProperty(value = "计划起飞时间")
    private String estimateOff;
    @ApiModelProperty(value = "实际起飞时间")
    private String actualOff;
    @ApiModelProperty(value = "天气对象数据")
    private WeatherVo weatherVo;
}

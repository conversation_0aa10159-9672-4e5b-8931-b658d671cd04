package com.swcares.mapper;

import com.swcares.obj.dto.GateScreenDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GateScreenMapper {


    /**
     * retrieveFltNoAndSection
     *
     * @param gate    gate
     * @param org     org
     * @param fltDate fltDate
     * @return retrieveFltNoAndSection
     */
    List<GateScreenDto> retrieveFltNoAndSection(@Param("gate") String gate, @Param("org") String org, @Param("fltDate") String fltDate);
}

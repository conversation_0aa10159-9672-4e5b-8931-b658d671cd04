package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.vo.GateScreenVo;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2022-12-12 14:21:56
 */
public interface IGateScreenService {

    /**
     * 登机口屏显航班查询
     *
     * @param gate 登机口
     * @return 航班列表
     */
    List<GateScreenVo> retrieveFlightByGate(String gate) throws UnifiedResultException;
}

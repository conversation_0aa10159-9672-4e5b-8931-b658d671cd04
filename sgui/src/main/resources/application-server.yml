#=============================端口===========================#
server:
  port: 8350
  tomcat:
    max-connections: 200
    max-threads: 300
    uri-encoding: utf-8
    max-swallow-size: 5MB
  servlet:
    context-path:
#======================spring配置===============================#
spring:
  # 项目名称
  application:
    name: sgui-bkc
  # 个人理解为禁用了开发属性(默认为true，生产我们设置为false)
  devtools:
    add-properties: false
  jackson:
    default-property-inclusion: always
#======================配置允许跨域源=============================#
sgui:
  allow:
    origin: ${SGUI_ALLOW_ORIGIN}
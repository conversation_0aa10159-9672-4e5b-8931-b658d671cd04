package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:56
 */
@Data
@ApiModel(value = "FaresRuleVo", description = "FaresRuleVo")
public class FaresRuleVo implements Serializable {

    @ApiModelProperty("规则列表")
    private List<Rules> rules = new ArrayList<>();

    @Data
    @ApiModel("运价规则")
    public static class Rules implements Serializable {

        @ApiModelProperty("")
        private String catNo;

        @ApiModelProperty("")
        private String catDetailCN;

        @ApiModelProperty("")
        private String catDetailsText;
    }
}

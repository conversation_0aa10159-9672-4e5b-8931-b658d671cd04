package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:30
 */
@Data
@ApiModel(value = "CheckVersionVo", description = "CheckVersionVo")
public class CheckVersionVo implements Serializable {

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "module")
    private String module;

    @ApiModelProperty(value = "version")
    private String version;

    @ApiModelProperty(value = "versionAttr")
    private String versionAttr;

    @ApiModelProperty(value = "userInfo")
    private UserInfo userInfo;

    /**
     * UserInfo
     */
    @Data
    public static class UserInfo implements Serializable {
        @ApiModelProperty(value = "office")
        private String office;

        @ApiModelProperty(value = "airline")
        private String airline;

        @ApiModelProperty(value = "airport")
        private String airport;
    }

    @ApiModelProperty(value = "versionDesc")
    private String versionDesc;

    @ApiModelProperty(value = "upgradeStrategy")
    private Integer upgradeStrategy;

    @ApiModelProperty(value = "upgradeStrategyEnRemind")
    private String upgradeStrategyEnRemind;

    @ApiModelProperty(value = "upgradeStrategyCnRemind")
    private String upgradeStrategyCnRemind;

    @ApiModelProperty(value = "targetVersion")
    private String targetVersion;

    @ApiModelProperty(value = "onlineUpgradeFlag")
    private Boolean onlineUpgradeFlag;

    @ApiModelProperty(value = "downloadUrl")
    private String downloadUrl;

    @ApiModelProperty(value = "daysRemaining")
    private Integer daysRemaining;
}

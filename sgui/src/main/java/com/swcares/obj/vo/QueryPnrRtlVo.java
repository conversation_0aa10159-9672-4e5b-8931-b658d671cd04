package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 查询航司PNR响应VO
 *
 * <AUTHOR>
 * @date 2025/5/15 14:30
 */
@Data
@ApiModel(value = "查询航司PNR响应VO")
public class QueryPnrRtlVo {

    @ApiModelProperty(value = "RTL行内容")
    private List<LineContent> rtlLineContent;

    @Data
    @ApiModel(value = "行内容")
    public static class LineContent {
        @ApiModelProperty(value = "索引")
        private String index;

        @ApiModelProperty(value = "内容")
        private String content;

        @ApiModelProperty(value = "是否可删除")
        private Boolean canDelete;

        @ApiModelProperty(value = "代码")
        private String code;

        @ApiModelProperty(value = "是否无效航段")
        private Boolean invalidSeg;
    }
}

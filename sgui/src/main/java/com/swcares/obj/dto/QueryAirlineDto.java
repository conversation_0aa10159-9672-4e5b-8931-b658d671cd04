package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/11 00:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryAirlineDto", description = "航空公司查询传输对象")
public class QueryAirlineDto implements Serializable {

    @ApiModelProperty(value = "航空公司名称")
    private String airlineName;

    @ApiModelProperty(value = "航空公司代码")
    private String airlineCode;
}

package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/10 20:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "CurrencyOfLocationDto", description = "位置货币查询传输对象")
public class CurrencyOfLocationDto implements Serializable {

    @ApiModelProperty(value = "查询参数")
    private String queryParam;

    @ApiModelProperty(value = "查询类型")
    private String queryType;
}

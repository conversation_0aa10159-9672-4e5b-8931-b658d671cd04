package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryRulesDto", description = "查询规则传输对象")
public class QueryRulesDto implements Serializable {

    @ApiModelProperty(value = "票证类型")
    private String ticketType;

    @ApiModelProperty(value = "旅客信息列表")
    private List<Passenger> passengers;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "Passenger", description = "旅客信息")
    public static class Passenger implements Serializable {
        
        @ApiModelProperty(value = "货币")
        private String currency;
        
        @ApiModelProperty(value = "航段信息列表")
        private List<SegInfo> segInfos;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "SegInfo", description = "航段信息")
    public static class SegInfo implements Serializable {
        
        @ApiModelProperty(value = "票面价")
        private String ticketAmount;
        
        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;
        
        @ApiModelProperty(value = "航空公司代码")
        private String companyCode;
        
        @ApiModelProperty(value = "出发机场代码")
        private String departureCode;
        
        @ApiModelProperty(value = "到达机场代码")
        private String arrivalCode;
        
        @ApiModelProperty(value = "出发日期时间")
        private String departureDate;
        
        @ApiModelProperty(value = "航班号")
        private String flightNumber;
        
        @ApiModelProperty(value = "规则ID列表")
        private List<String> ruleIds;
        
        @ApiModelProperty(value = "规则信息列表")
        private List<RuleInfo> ruleInfoList;
        
        @ApiModelProperty(value = "关联航段编号")
        private String marriedSegmentNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "RuleInfo", description = "规则信息")
    public static class RuleInfo implements Serializable {
        
        @ApiModelProperty(value = "规则ID")
        private String ruleId;
        
        @ApiModelProperty(value = "位置")
        private String location;
    }
}

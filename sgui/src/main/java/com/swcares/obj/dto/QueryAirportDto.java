package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/11 02:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryAirportDto", description = "机场查询传输对象")
public class QueryAirportDto implements Serializable {

    @ApiModelProperty(value = "城市代码")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "机场代码")
    private String airportCode;

    @ApiModelProperty(value = "机场名称")
    private String airportName;
}

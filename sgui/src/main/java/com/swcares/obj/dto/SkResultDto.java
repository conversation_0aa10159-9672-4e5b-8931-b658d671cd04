package com.swcares.obj.dto;

import com.swcares.obj.vo.SkVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
@Data
@ApiModel(value = "SkResultDto", description = "SK查询结果传输对象")
public class SkResultDto implements Serializable {

    @ApiModelProperty(value = "航司")
    private String airCode;

    @ApiModelProperty(value = "联盟")
    private String alliance;

    @ApiModelProperty(value = "到达航站或城市")
    private String arriveCity;

    @ApiModelProperty(value = "出发航站或城市")
    private String departureCity;

    @ApiModelProperty(value = "出发日期")
    private String departureDate;

    @ApiModelProperty(value = "出发时间")
    private String departureTime;

    @ApiModelProperty(value = "仅直飞")
    private String onlyDirect;

    @ApiModelProperty(value = "中转点")
    private String transitCity;

    @ApiModelProperty(value = "非共享")
    private Boolean unsharedFlight;

    @ApiModelProperty(value = "查询结果")
    private SkVo skVo;
}

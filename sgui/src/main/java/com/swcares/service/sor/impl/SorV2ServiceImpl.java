package com.swcares.service.sor.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.entity.MnjxAirport;
import com.swcares.entity.SguiData;
import com.swcares.obj.dto.ComputeInterAirPriceDto;
import com.swcares.obj.dto.QueryDomesticFreeBaggageDto;
import com.swcares.obj.dto.QueryFareDomesticDto;
import com.swcares.obj.dto.QueryRulesDto;
import com.swcares.obj.vo.QueryDomesticFreeBaggageVo;
import com.swcares.obj.vo.QueryFareDomesticVo;
import com.swcares.obj.vo.QueryRulesVo;
import com.swcares.service.ISguiCommonService;
import com.swcares.service.ISguiDataService;
import com.swcares.service.sor.IFareDomesticService;
import com.swcares.service.sor.ISorV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Slf4j
@Service
public class SorV2ServiceImpl implements ISorV2Service {

    @Resource
    private ISguiDataService iSguiDataService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IFareDomesticService iFareDomesticService;

    @Override
    public String computeInterAirPrice(ComputeInterAirPriceDto dto) throws SguiResultException {
        return "调用SAT接口，解析返回结果异常";
    }

    @Override
    public QueryFareDomesticVo queryFareDomestic(QueryFareDomesticDto dto) throws SguiResultException {
        return iFareDomesticService.queryFareDomestic(dto);
    }

    @Override
    public QueryDomesticFreeBaggageVo queryDomesticFreeBaggage(QueryDomesticFreeBaggageDto dto) throws SguiResultException {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "domestic_free_baggage")
                .one();
        if (data != null) {
            QueryDomesticFreeBaggageVo vo = JSONUtil.parseObj(data.getValue()).toBean(QueryDomesticFreeBaggageVo.class);
            for (QueryDomesticFreeBaggageVo.FreeBaggageSegInfo segInfo : vo.getFreeBaggageSegInfo()) {
                segInfo.setOcAirline(dto.getSegmentInfos().get(0).getOcAirline());
                segInfo.setDepartureDateTime(dto.getSegmentInfos().get(0).getDepartureDate() + "T" + dto.getSegmentInfos().get(0).getDepartureTime());
            }
            return vo;
        }
        return null;
    }

    @Override
    public QueryRulesVo queryRules(QueryRulesDto dto) throws SguiResultException {
        // 从模拟数据中获取
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "ticket_rules")
                .one();
        if (data == null) {
            return new QueryRulesVo();
        }

        QueryRulesVo vo = JSONUtil.parseObj(data.getValue()).toBean(QueryRulesVo.class);

        // 如果没有旅客信息或航段信息，直接返回
        if (CollUtil.isEmpty(dto.getPassengers()) ||
                CollUtil.isEmpty(dto.getPassengers().get(0).getSegInfos())) {
            return vo;
        }

        // 获取出发机场和到达机场代码
        String departureCode = dto.getPassengers().get(0).getSegInfos().get(0).getDepartureCode();
        String arrivalCode = dto.getPassengers().get(0).getSegInfos().get(0).getArrivalCode();

        // 如果没有规则信息，直接返回
        if (CollUtil.isEmpty(vo.getFlightRules()) ||
                CollUtil.isEmpty(vo.getFlightRules().get(0).getRules())) {
            return vo;
        }

        // 获取机场信息
        MnjxAirport departureAirport = iSguiCommonService.getAirportByCode(departureCode);
        MnjxAirport arrivalAirport = iSguiCommonService.getAirportByCode(arrivalCode);

        // 更新机场信息
        QueryRulesVo.Rule rule = vo.getFlightRules().get(0).getRules().get(0);
        rule.setDepartureAirport(departureCode);
        rule.setArrivalAirport(arrivalCode);

        // 设置机场中文名
        if (departureAirport != null) {
            rule.setDepartureAirportCityCh(departureAirport.getAirportCname());
        }
        if (arrivalAirport != null) {
            rule.setArrivalAirportCityCh(arrivalAirport.getAirportCname());
        }

        return vo;
    }
}

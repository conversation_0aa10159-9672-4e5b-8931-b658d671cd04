package com.swcares.service.bkc.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.cache.FlightCacheConstants;
import com.swcares.core.cache.FlightInfoCachePreloader;
import com.swcares.obj.dto.FlightInfoDto;
import com.swcares.service.bkc.IFlightInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 航班信息服务实现类
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
@Slf4j
@Service
public class FlightInfoServiceImpl implements IFlightInfoService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private FlightInfoCachePreloader flightInfoCachePreloader;

    @Override
    public List<FlightInfoDto> getFlightInfoByDate(String date) {
        String dateKey = FlightCacheConstants.FLIGHT_DATE_PREFIX + date;
        String flightIdsJson = stringRedisTemplate.opsForValue().get(dateKey);

        if (StrUtil.isBlank(flightIdsJson)) {
            log.warn("日期 {} 的航班信息缓存未找到", date);
            // 同步刷新
            this.refreshFlightInfoByDate(date);
            // 重新获取
            flightIdsJson = stringRedisTemplate.opsForValue().get(dateKey);
            if (StrUtil.isBlank(flightIdsJson)) {
                return new ArrayList<>();
            }
        }

        if (FlightCacheConstants.EMPTY_VALUE.equals(flightIdsJson)) {
            return new ArrayList<>();
        }

        List<String> flightIds = JSONUtil.toList(JSONUtil.parseArray(flightIdsJson), String.class);
        List<FlightInfoDto> result = new ArrayList<>();

        for (String flightId : flightIds) {
            FlightInfoDto flightInfo = this.getFlightInfoById(flightId);
            if (flightInfo != null) {
                result.add(flightInfo);
            }
        }

        return result;
    }

    @Override
    public FlightInfoDto getFlightInfoById(String flightId) {
        String flightKey = FlightCacheConstants.FLIGHT_DETAIL_PREFIX + flightId;
        String flightJson = stringRedisTemplate.opsForValue().get(flightKey);

        if (StrUtil.isBlank(flightJson)) {
            log.warn("航班ID {} 的详细信息缓存未找到", flightId);
            // 同步刷新
            this.refreshFlightInfoById(flightId);
            // 重新获取
            flightJson = stringRedisTemplate.opsForValue().get(flightKey);
            if (StrUtil.isBlank(flightJson)) {
                return null;
            }
        }

        if (FlightCacheConstants.EMPTY_VALUE.equals(flightJson)) {
            return null;
        }

        return JSONUtil.toBean(flightJson, FlightInfoDto.class);
    }

    @Override
    public List<FlightInfoDto> getFlightInfoByFlightNoAndDate(String flightNo, String date) {
        List<FlightInfoDto> allFlights = this.getFlightInfoByDate(date);
        return allFlights.stream()
                .filter(flight -> flight.getFlightNo().equals(flightNo))
                .collect(Collectors.toList());
    }

    @Override
    public void refreshFlightInfoByDate(String date) {
        // 同步刷新
        flightInfoCachePreloader.loadFlightInfoByDate(date);
    }

    @Override
    public void refreshFlightInfoById(String flightId) {
        // 同步刷新
        flightInfoCachePreloader.loadFlightInfoById(flightId);
    }
}

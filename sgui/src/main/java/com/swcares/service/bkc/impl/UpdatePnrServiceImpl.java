package com.swcares.service.bkc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.UpdatePnrDto;
import com.swcares.obj.vo.UpdatePnrVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IUpdatePnrService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 更新PNR服务实现
 *
 * <AUTHOR>
 * @date 2025/5/26 16:00
 */
@Slf4j
@Service
public class UpdatePnrServiceImpl implements IUpdatePnrService {

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;

    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;

    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;

    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;

    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;

    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;

    @Resource
    private IMnjxNmCtService iMnjxNmCtService;

    @Resource
    private IMnjxNmEiService iMnjxNmEiService;

    @Resource
    private IMnjxNmFcService iMnjxNmFcService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxNmFpService iMnjxNmFpService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdatePnrVo updatePnr(UpdatePnrDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || StrUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (ObjectUtil.isEmpty(pnr)) {
            throw new SguiResultException("未找到对应的PNR信息");
        }

        // 初始化各项操作列表
        List<Object> addList = new ArrayList<>();
        List<Object> updateList = new ArrayList<>();
        List<Object> deleteList = new ArrayList<>();

        // 处理各项变更
        this.processPassengers(dto.getPassengers(), pnr, addList, updateList, deleteList);
        this.processContacts(dto.getContacts(), pnr, addList, updateList, deleteList);
        this.processRemarks(dto.getRemarks(), pnr, addList, updateList, deleteList);
        this.processSpecialServices(dto.getSpecialServices(), pnr, addList, updateList, deleteList);
        this.processIssueLimit(dto.getIssueLimit(), pnr, addList, updateList, deleteList);
        this.processManualFare(dto.getManualFare(), pnr, addList, updateList, deleteList);
        this.processSegments(dto.getSegments(), pnr, addList, updateList, deleteList);
        this.processAutoFares(dto.getAutoFares(), pnr, addList, updateList, deleteList);

        // 处理婴儿SSR INFT修改
        this.processInfantSsrUpdate(pnr);

        // 重新排序所有项的pnr_index
        this.reorderAllPnrIndexes(pnr);

        // 批量执行数据库操作
        this.batchExecuteOperations(addList, updateList, deleteList);

        // 生成封口记录
        this.generateSealingRecord(pnr, dto.getEnvelopType());

        // 构建返回结果
        UpdatePnrVo vo = new UpdatePnrVo();
        vo.setError(null);
        UpdatePnrVo.Dishonest dishonest = new UpdatePnrVo.Dishonest();
        dishonest.setDishonestCheck(false);
        dishonest.setDishonestPassengers("");
        vo.setDishonest(dishonest);

        return vo;
    }

    /**
     * 处理旅客信息变更
     */
    private void processPassengers(UpdatePnrDto.Passengers passengers, MnjxPnr pnr,
                                   List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (passengers == null) {
            return;
        }

        // 处理新增旅客
        if (CollUtil.isNotEmpty(passengers.getAddPassengers())) {
            this.processAddPassengers(passengers.getAddPassengers(), pnr, addList);
        }

        // 处理编辑旅客
        if (CollUtil.isNotEmpty(passengers.getEditPassengers())) {
            this.processEditPassengers(passengers.getEditPassengers(), pnr, updateList);
        }
    }

    /**
     * 处理新增旅客
     */
    private void processAddPassengers(List<UpdatePnrDto.AddPassenger> addPassengers, MnjxPnr pnr, List<Object> addList) {
        // 获取当前最大旅客序号
        MnjxPnrNm maxPnrNm = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByDesc(MnjxPnrNm::getPsgIndex)
                .last("LIMIT 1")
                .one();
        Integer maxPsgIndex = maxPnrNm != null ? maxPnrNm.getPsgIndex() : 0;

        int nextPsgIndex = maxPsgIndex == null ? 1 : maxPsgIndex + 1;

        for (UpdatePnrDto.AddPassenger passenger : addPassengers) {
            // 创建旅客信息
            MnjxPnrNm pnrNm = new MnjxPnrNm();
            pnrNm.setPnrNmId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrNm.setPnrId(pnr.getPnrId());
            pnrNm.setPnrIndex(0); // 后续重新排序
            pnrNm.setPsgIndex(nextPsgIndex++);
            pnrNm.setName(passenger.getFullName());

            // 设置旅客类型
            if ("CHD".equals(passenger.getPassengerType())) {
                pnrNm.setPsgType("1"); // 儿童
            } else if ("INF".equals(passenger.getPassengerType())) {
                pnrNm.setPsgType("4"); // 婴儿
            } else {
                pnrNm.setPsgType("0"); // 成人
            }

            // 构建input_value
            String inputValue = StrUtil.format("{}({})", passenger.getFullName(), passenger.getPassengerType());
            pnrNm.setInputValue(inputValue);

            addList.add(pnrNm);

            // 处理证件信息
            if (StrUtil.isNotEmpty(passenger.getCertificateType()) && StrUtil.isNotEmpty(passenger.getCertificateNo())) {
                this.processPassengerDocument(passenger, pnrNm, addList);
            }
        }
    }

    /**
     * 处理旅客证件信息
     */
    private void processPassengerDocument(UpdatePnrDto.AddPassenger passenger, MnjxPnrNm pnrNm, List<Object> addList) {
        if ("NI".equals(passenger.getCertificateType())) {
            // 生成FOID SSR
            MnjxNmSsr nmSsr = new MnjxNmSsr();
            nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
            nmSsr.setPnrNmId(pnrNm.getPnrNmId());
            nmSsr.setPnrIndex(0); // 后续重新排序
            nmSsr.setSsrType("FOID");
            nmSsr.setActionCode("HK");

            String ssrInfo = StrUtil.format("SSR FOID HK1 NI{}/P{}",
                    passenger.getCertificateNo(), pnrNm.getPsgIndex());
            nmSsr.setSsrInfo(ssrInfo);
            nmSsr.setInputValue(ssrInfo);

            addList.add(nmSsr);
        }
    }

    /**
     * 处理编辑旅客
     */
    private void processEditPassengers(List<UpdatePnrDto.EditPassenger> editPassengers, MnjxPnr pnr, List<Object> updateList) {
        for (UpdatePnrDto.EditPassenger passenger : editPassengers) {
            // 根据passengerId查找旅客
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, passenger.getPassengerId())
                    .one();

            if (pnrNm != null) {
                // 更新旅客信息
                if (StrUtil.isNotEmpty(passenger.getFullName())) {
                    pnrNm.setName(passenger.getFullName());
                }

                updateList.add(pnrNm);

                // 处理婴儿信息
                if (passenger.getInfantDetail() != null) {
                    this.processInfantDetail(passenger.getInfantDetail(), pnrNm, updateList);
                }
            }
        }
    }

    /**
     * 处理婴儿详情
     */
    private void processInfantDetail(UpdatePnrDto.InfantDetail infantDetail, MnjxPnrNm pnrNm, List<Object> updateList) {
        // 查找现有婴儿记录
        MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, pnrNm.getPnrNmId())
                .one();

        if (nmXn != null) {
            // 更新婴儿信息
            if (StrUtil.isNotEmpty(infantDetail.getBirthday())) {
                nmXn.setXnBirthday(DateUtils.ymd2Com(infantDetail.getBirthday()));
            }
            if (StrUtil.isNotEmpty(infantDetail.getChineseName())) {
                nmXn.setXnCname(infantDetail.getChineseName());
            }
            // 注意：MnjxNmXn实体中没有xnName字段，只有xnCname字段

            updateList.add(nmXn);
        }
    }

    /**
     * 处理联系方式变更
     */
    private void processContacts(List<UpdatePnrDto.Contact> contacts, MnjxPnr pnr,
                                 List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        if (CollUtil.isEmpty(contacts)) {
            return;
        }

        for (UpdatePnrDto.Contact contact : contacts) {
            switch (contact.getOperateType()) {
                case "A":
                    this.addContact(contact, pnr, addList);
                    break;
                case "M":
                    this.modifyContact(contact, pnr, updateList, deleteList);
                    break;
                case "D":
                    this.deleteContact(contact, pnr, deleteList);
                    break;
            }
        }
    }

    /**
     * 新增联系方式
     */
    private void addContact(UpdatePnrDto.Contact contact, MnjxPnr pnr, List<Object> addList) {
        if ("CT".equals(contact.getType())) {
            MnjxPnrCt pnrCt = new MnjxPnrCt();
            pnrCt.setPnrCtId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrCt.setPnrId(pnr.getPnrId());
            pnrCt.setPnrIndex(0); // 后续重新排序
            pnrCt.setCtText("T-" + contact.getText());

            // 构建输入值
            String inputValue = StrUtil.format("{}", contact.getText());
            if (StrUtil.isNotEmpty(contact.getAirline())) {
                inputValue = StrUtil.format("CT {}-{}", contact.getAirline(), contact.getText());
            }
            pnrCt.setInputValue(inputValue);

            addList.add(pnrCt);
        } else if ("CTCE".equals(contact.getType())) {
            MnjxPnrOsi pnrOsi = new MnjxPnrOsi();
            pnrOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrOsi.setPnrId(pnr.getPnrId());
            pnrOsi.setPnrIndex(0); // 后续重新排序
            pnrOsi.setPnrOsiType("CTCE");

            String inputValue = StrUtil.format("OSI {} CTCE {}", contact.getAirline(), contact.getText());
            pnrOsi.setInputValue(inputValue);

            addList.add(pnrOsi);
        }
    }

    /**
     * 修改联系方式
     */
    private void modifyContact(UpdatePnrDto.Contact contact, MnjxPnr pnr,
                               List<Object> updateList, List<Object> deleteList) {
        // 先删除原有记录
        if (CollUtil.isNotEmpty(contact.getDeleteIndexList())) {
            this.deleteContactByIndexes(contact.getDeleteIndexList(), pnr, deleteList);
        }

        // 再新增新记录
        this.addContact(contact, pnr, updateList);
    }

    /**
     * 删除联系方式
     */
    private void deleteContact(UpdatePnrDto.Contact contact, MnjxPnr pnr, List<Object> deleteList) {
        if (CollUtil.isNotEmpty(contact.getDeleteIndexList())) {
            this.deleteContactByIndexes(contact.getDeleteIndexList(), pnr, deleteList);
        }
    }

    /**
     * 根据索引删除联系方式
     */
    private void deleteContactByIndexes(List<Integer> deleteIndexList, MnjxPnr pnr, List<Object> deleteList) {
        for (Integer index : deleteIndexList) {
            // 查找对应的记录类型
            MnjxPnrRecord record = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrRecord::getPnrIndex, index)
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .one();

            if (record != null) {
                if ("CT".equals(record.getPnrType())) {
                    MnjxPnrCt pnrCt = iMnjxPnrCtService.lambdaQuery()
                            .eq(MnjxPnrCt::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrCt::getPnrIndex, index)
                            .one();
                    if (pnrCt != null) {
                        deleteList.add(pnrCt);
                    }
                }
                // 标记记录为删除
                record.setChangeMark("X");
                deleteList.add(record);
            }
        }
    }

    /**
     * 处理备注信息变更
     */
    private void processRemarks(List<UpdatePnrDto.Remark> remarks, MnjxPnr pnr,
                                List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        if (CollUtil.isEmpty(remarks)) {
            return;
        }

        for (UpdatePnrDto.Remark remark : remarks) {
            switch (remark.getOperateType()) {
                case "A":
                    this.addRemark(remark, pnr, addList);
                    break;
                case "M":
                    this.modifyRemark(remark, pnr, updateList, deleteList);
                    break;
                case "D":
                    this.deleteRemark(remark, pnr, deleteList);
                    break;
            }
        }
    }

    /**
     * 新增备注
     */
    private void addRemark(UpdatePnrDto.Remark remark, MnjxPnr pnr, List<Object> addList) {
        if ("RMK".equals(remark.getType())) {
            MnjxPnrRmk pnrRmk = new MnjxPnrRmk();
            pnrRmk.setPnrRmkId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrRmk.setPnrId(pnr.getPnrId());
            pnrRmk.setPnrIndex(0); // 后续重新排序
            pnrRmk.setRmkName("FREE");

            String inputValue = StrUtil.format("RMK {}", remark.getText());
            pnrRmk.setRmkInfo(inputValue);
            pnrRmk.setInputValue(inputValue);

            addList.add(pnrRmk);
        } else if ("OSI".equals(remark.getType())) {
            MnjxPnrOsi pnrOsi = new MnjxPnrOsi();
            pnrOsi.setPnrOsiId(IdUtil.getSnowflake(1, 1).nextIdStr());
            pnrOsi.setPnrId(pnr.getPnrId());
            pnrOsi.setPnrIndex(0); // 后续重新排序

            if (remark.getText().startsWith("CTCT")) {
                pnrOsi.setPnrOsiType("CTCT");
            } else if (remark.getText().startsWith("CTCE")) {
                pnrOsi.setPnrOsiType("CTCE");
            }

            String inputValue = StrUtil.format("OSI {} {}", remark.getAirline(), remark.getText());
            pnrOsi.setInputValue(inputValue);
            pnrOsi.setPnrOsiInfo(inputValue);

            addList.add(pnrOsi);
        }
    }

    /**
     * 修改备注
     */
    private void modifyRemark(UpdatePnrDto.Remark remark, MnjxPnr pnr,
                              List<Object> updateList, List<Object> deleteList) {
        // 先删除原有记录
        if (CollUtil.isNotEmpty(remark.getDeleteIndexList())) {
            this.deleteRemarkByIndexes(remark.getDeleteIndexList(), pnr, deleteList);
        }

        // 再新增新记录
        this.addRemark(remark, pnr, updateList);
    }

    /**
     * 删除备注
     */
    private void deleteRemark(UpdatePnrDto.Remark remark, MnjxPnr pnr, List<Object> deleteList) {
        if (CollUtil.isNotEmpty(remark.getDeleteIndexList())) {
            this.deleteRemarkByIndexes(remark.getDeleteIndexList(), pnr, deleteList);
        }
    }

    /**
     * 根据索引删除备注
     */
    private void deleteRemarkByIndexes(List<Integer> deleteIndexList, MnjxPnr pnr, List<Object> deleteList) {
        for (Integer index : deleteIndexList) {
            MnjxPnrRecord record = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrRecord::getPnrIndex, index)
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .one();

            if (record != null) {
                if ("RMK".equals(record.getPnrType())) {
                    MnjxPnrRmk pnrRmk = iMnjxPnrRmkService.lambdaQuery()
                            .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrRmk::getPnrIndex, index)
                            .one();
                    if (pnrRmk != null) {
                        deleteList.add(pnrRmk);
                    }
                } else if ("OSI".equals(record.getPnrType())) {
                    MnjxPnrOsi pnrOsi = iMnjxPnrOsiService.lambdaQuery()
                            .eq(MnjxPnrOsi::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrOsi::getPnrIndex, index)
                            .one();
                    if (pnrOsi != null) {
                        deleteList.add(pnrOsi);
                    }
                }
                // 标记记录为删除
                record.setChangeMark("X");
                deleteList.add(record);
            }
        }
    }

    /**
     * 处理特殊服务变更
     */
    private void processSpecialServices(List<UpdatePnrDto.SpecialService> specialServices, MnjxPnr pnr,
                                        List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (CollUtil.isEmpty(specialServices)) {
            return;
        }

        for (UpdatePnrDto.SpecialService service : specialServices) {
            switch (service.getOperateType()) {
                case "A":
                    this.addSpecialService(service, pnr, addList);
                    break;
                case "M":
                    this.modifySpecialService(service, pnr, updateList, deleteList);
                    break;
                case "D":
                    this.deleteSpecialService(service, pnr, deleteList);
                    break;
            }
        }
    }

    /**
     * 新增特殊服务
     */
    private void addSpecialService(UpdatePnrDto.SpecialService service, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        // 查找对应的航段
        MnjxPnrSeg pnrSeg = this.findPnrSegment(service.getOrigin(), service.getDestination(),
                service.getFlightNumber(), service.getFlightDate(), pnr.getPnrId());

        if (pnrSeg == null) {
            throw new SguiResultException("未找到对应的航段信息");
        }

        for (Integer paxId : service.getPaxIds()) {
            // 查找旅客
            MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrNm::getPsgIndex, paxId)
                    .one();

            if (pnrNm != null) {
                MnjxNmSsr nmSsr = new MnjxNmSsr();
                nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
                nmSsr.setPnrNmId(pnrNm.getPnrNmId());
                nmSsr.setPnrIndex(0); // 后续重新排序
                nmSsr.setSsrType(service.getSsrCode());
                nmSsr.setActionCode("HK"); // 更新后修改为HK
                nmSsr.setAirlineCode(service.getAirline());
                nmSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
                nmSsr.setOrgDst(service.getOrigin() + service.getDestination());
                nmSsr.setFltDate(DateUtils.ymd2Com(service.getFlightDate()));

                String ssrInfo = StrUtil.format("SSR {} {} HK1 {}{} {}/P{}",
                        service.getSsrCode(), service.getAirline(), service.getOrigin(), service.getDestination(),
                        StrUtil.isNotEmpty(service.getText()) ? service.getText() : "", paxId);
                nmSsr.setSsrInfo(ssrInfo);
                nmSsr.setInputValue(ssrInfo);

                addList.add(nmSsr);
            }
        }
    }

    /**
     * 查找PNR航段
     */
    private MnjxPnrSeg findPnrSegment(String origin, String destination, String flightNumber,
                                      String flightDate, String pnrId) {
        return iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrId)
                .eq(MnjxPnrSeg::getOrg, origin)
                .eq(MnjxPnrSeg::getDst, destination)
                .like(MnjxPnrSeg::getFlightNo, flightNumber)
                .eq(MnjxPnrSeg::getFlightDate, DateUtils.ymd2Com(flightDate))
                .one();
    }

    /**
     * 修改特殊服务
     */
    private void modifySpecialService(UpdatePnrDto.SpecialService service, MnjxPnr pnr,
                                      List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        // 先删除原有记录
        if (CollUtil.isNotEmpty(service.getDeleteIndexList())) {
            this.deleteSpecialServiceByIndexes(service.getDeleteIndexList(), pnr, deleteList);
        }

        // 再新增新记录
        this.addSpecialService(service, pnr, updateList);
    }

    /**
     * 删除特殊服务
     */
    private void deleteSpecialService(UpdatePnrDto.SpecialService service, MnjxPnr pnr, List<Object> deleteList) {
        if (CollUtil.isNotEmpty(service.getDeleteIndexList())) {
            this.deleteSpecialServiceByIndexes(service.getDeleteIndexList(), pnr, deleteList);
        }
    }

    /**
     * 根据索引删除特殊服务
     */
    private void deleteSpecialServiceByIndexes(List<Integer> deleteIndexList, MnjxPnr pnr, List<Object> deleteList) {
        for (Integer index : deleteIndexList) {
            MnjxPnrRecord record = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrRecord::getPnrIndex, index)
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .one();

            if (record != null && "NM SSR".equals(record.getPnrType())) {
                // 查找对应的SSR记录
                List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrIndex, index)
                        .list();

                deleteList.addAll(ssrList);

                // 标记记录为删除
                record.setChangeMark("X");
                deleteList.add(record);
            }
        }
    }

    /**
     * 处理出票时限变更
     */
    private void processIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr,
                                   List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        if (issueLimit == null) {
            return;
        }

        switch (issueLimit.getOperateType()) {
            case "A":
                this.addIssueLimit(issueLimit, pnr, addList);
                break;
            case "M":
                this.modifyIssueLimit(issueLimit, pnr, updateList);
                break;
            case "D":
                this.deleteIssueLimit(issueLimit, pnr, deleteList);
                break;
        }
    }

    /**
     * 新增出票时限
     */
    private void addIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrTk pnrTk = new MnjxPnrTk();
        pnrTk.setPnrTkId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrTk.setPnrId(pnr.getPnrId());
        pnrTk.setPnrIndex(0); // 后续重新排序
        String issueLimitTime = issueLimit.getIssueLimitTime();
        Date timeLimitDate = DateUtil.parse(issueLimitTime);
        pnrTk.setPlanEtdzDate(DateUtil.format(timeLimitDate, "yyyy-MM-dd"));
        pnrTk.setPlanEtdzTime(DateUtil.format(timeLimitDate, "HHmm"));

        String inputValue = StrUtil.format("TL/{}/{}/{}", pnrTk.getPlanEtdzTime(), pnrTk.getPlanEtdzDate(), pnrTk.getEtdzOffice());
        pnrTk.setInputValue(inputValue);

        addList.add(pnrTk);
    }

    /**
     * 修改出票时限
     */
    private void modifyIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr, List<Object> updateList) {
        // 查找现有的出票时限记录
        MnjxPnrTk existingTk = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .one();

        if (existingTk != null) {
            String issueLimitTime = issueLimit.getIssueLimitTime();
            Date timeLimitDate = DateUtil.parse(issueLimitTime);
            existingTk.setPlanEtdzDate(DateUtil.format(timeLimitDate, "yyyy-MM-dd"));
            existingTk.setPlanEtdzTime(DateUtil.format(timeLimitDate, "HHmm"));
            existingTk.setEtdzOffice(issueLimit.getIssueLimitOffice());

            String inputValue = StrUtil.format("TL/{}/{}/{}", existingTk.getPlanEtdzTime(), existingTk.getPlanEtdzDate(), existingTk.getEtdzOffice());
            existingTk.setInputValue(inputValue);

            updateList.add(existingTk);
        }
    }

    /**
     * 删除出票时限
     */
    private void deleteIssueLimit(UpdatePnrDto.IssueLimit issueLimit, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrTk existingTk = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .one();

        if (existingTk != null) {
            deleteList.add(existingTk);
        }
    }

    /**
     * 处理手工运价
     */
    private void processManualFare(UpdatePnrDto.ManualFare manualFare, MnjxPnr pnr,
                                   List<Object> addList, List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        if (manualFare == null) {
            return;
        }

        switch (manualFare.getOperateType()) {
            case "A":
                this.addManualFare(manualFare, pnr, addList);
                break;
            case "M":
                this.modifyManualFare(manualFare, pnr, updateList, deleteList);
                break;
            case "D":
                this.deleteManualFare(manualFare, pnr, deleteList);
                break;
        }
    }

    /**
     * 新增手工运价
     */
    private void addManualFare(UpdatePnrDto.ManualFare manualFare, MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        UpdatePnrDto.PnrInfo pnrInfo = manualFare.getPnrInfo();
        if (pnrInfo == null) {
            return;
        }

        // 处理FC信息
        if (CollUtil.isNotEmpty(pnrInfo.getFcInfos())) {
            this.processFcInfos(pnrInfo.getFcInfos(), manualFare, pnr, addList);
        }

        // 处理FN信息
        if (CollUtil.isNotEmpty(pnrInfo.getFnInfos())) {
            this.processFnInfos(pnrInfo.getFnInfos(), manualFare, pnr, addList);
        }

        // 处理FP信息
        if (CollUtil.isNotEmpty(pnrInfo.getFpInfos())) {
            this.processFpInfos(pnrInfo.getFpInfos(), manualFare, pnr, addList);
        }

        // 处理EI信息
        if (CollUtil.isNotEmpty(pnrInfo.getEiInfos())) {
            this.processEiInfos(pnrInfo.getEiInfos(), manualFare, pnr, addList);
        }
    }

    /**
     * 处理FC信息
     */
    private void processFcInfos(List<UpdatePnrDto.FareInfo> fcInfos, UpdatePnrDto.ManualFare manualFare,
                                MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (UpdatePnrDto.FareInfo fcInfo : fcInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(fcInfo.getText()), StandardCharsets.UTF_8);

            // 解析旅客ID
            Map<String, List<Integer>> pendingTicketPaxId = manualFare.getPendingTicketPaxId();

            if (pendingTicketPaxId != null && !pendingTicketPaxId.isEmpty()) {
                // 旅客级别FC
                this.createNmFcRecords(decodedText, fcInfo.getInfSign(), pendingTicketPaxId, pnr, addList);
            } else {
                // PNR级别FC
                this.createPnrFcRecord(decodedText, pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别FC记录
     */
    private void createNmFcRecords(String decodedText, Boolean infSign, Map<String, List<Integer>> pendingTicketPaxId,
                                   MnjxPnr pnr, List<Object> addList) {
        String passengerType = Boolean.TRUE.equals(infSign) ? "INF" : "ADT";
        List<Integer> passengerIds = pendingTicketPaxId.get(passengerType);

        if (CollUtil.isNotEmpty(passengerIds)) {
            for (Integer passengerId : passengerIds) {
                MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                        .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrNm::getPsgIndex, passengerId)
                        .one();

                if (pnrNm != null) {
                    MnjxNmFc nmFc = new MnjxNmFc();
                    nmFc.setNmFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    nmFc.setPnrNmId(pnrNm.getPnrNmId());
                    nmFc.setPnrIndex(0); // 后续重新排序

                    // 修改文本中的旅客引用
                    String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId);
                    nmFc.setInputValue(modifiedText);

                    addList.add(nmFc);
                }
            }
        }
    }

    /**
     * 创建PNR级别FC记录
     */
    private void createPnrFcRecord(String decodedText, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrFc pnrFc = new MnjxPnrFc();
        pnrFc.setPnrFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFc.setPnrId(pnr.getPnrId());
        pnrFc.setPnrIndex(0); // 后续重新排序
        pnrFc.setInputValue(decodedText);

        addList.add(pnrFc);
    }

    /**
     * 处理FN信息
     */
    private void processFnInfos(List<UpdatePnrDto.FareInfo> fnInfos, UpdatePnrDto.ManualFare manualFare,
                                MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (UpdatePnrDto.FareInfo fnInfo : fnInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(fnInfo.getText()), StandardCharsets.UTF_8);

            Map<String, List<Integer>> pendingTicketPaxId = manualFare.getPendingTicketPaxId();

            if (pendingTicketPaxId != null && !pendingTicketPaxId.isEmpty()) {
                // 旅客级别FN
                this.createNmFnRecords(decodedText, fnInfo.getInfSign(), pendingTicketPaxId, pnr, addList);
            } else {
                // PNR级别FN
                this.createPnrFnRecord(decodedText, pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别FN记录
     */
    private void createNmFnRecords(String decodedText, Boolean infSign, Map<String, List<Integer>> pendingTicketPaxId,
                                   MnjxPnr pnr, List<Object> addList) {
        String passengerType = Boolean.TRUE.equals(infSign) ? "INF" : "ADT";
        List<Integer> passengerIds = pendingTicketPaxId.get(passengerType);

        if (CollUtil.isNotEmpty(passengerIds)) {
            for (Integer passengerId : passengerIds) {
                MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                        .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrNm::getPsgIndex, passengerId)
                        .one();

                if (pnrNm != null) {
                    MnjxNmFn nmFn = new MnjxNmFn();
                    nmFn.setNmFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    nmFn.setPnrNmId(pnrNm.getPnrNmId());
                    nmFn.setPnrIndex(0); // 后续重新排序

                    // 修改文本中的旅客引用
                    String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId);
                    nmFn.setInputValue(modifiedText);

                    addList.add(nmFn);
                }
            }
        }
    }

    /**
     * 创建PNR级别FN记录
     */
    private void createPnrFnRecord(String decodedText, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrFn pnrFn = new MnjxPnrFn();
        pnrFn.setPnrFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFn.setPnrId(pnr.getPnrId());
        pnrFn.setPnrIndex(0); // 后续重新排序
        pnrFn.setInputValue(decodedText);

        addList.add(pnrFn);
    }

    /**
     * 处理FP信息
     */
    private void processFpInfos(List<String> fpInfos, UpdatePnrDto.ManualFare manualFare,
                                MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (String fpInfo : fpInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(fpInfo), StandardCharsets.UTF_8);

            Map<String, List<Integer>> pendingTicketPaxId = manualFare.getPendingTicketPaxId();

            if (pendingTicketPaxId != null && !pendingTicketPaxId.isEmpty()) {
                // 旅客级别FP
                this.createNmFpRecords(decodedText, pendingTicketPaxId, pnr, addList);
            } else {
                // PNR级别FP
                this.createPnrFpRecord(decodedText, pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别FP记录
     */
    private void createNmFpRecords(String decodedText, Map<String, List<Integer>> pendingTicketPaxId,
                                   MnjxPnr pnr, List<Object> addList) {
        // 解析旅客ID
        for (Map.Entry<String, List<Integer>> entry : pendingTicketPaxId.entrySet()) {
            List<Integer> passengerIds = entry.getValue();

            if (CollUtil.isNotEmpty(passengerIds)) {
                for (Integer passengerId : passengerIds) {
                    MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                            .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrNm::getPsgIndex, passengerId)
                            .one();

                    if (pnrNm != null) {
                        MnjxNmFp nmFp = new MnjxNmFp();
                        nmFp.setNmFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmFp.setPnrNmId(pnrNm.getPnrNmId());
                        nmFp.setPnrIndex(0); // 后续重新排序

                        // 修改文本中的旅客引用
                        String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId);
                        nmFp.setInputValue(modifiedText);

                        addList.add(nmFp);
                    }
                }
            }
        }
    }

    /**
     * 创建PNR级别FP记录
     */
    private void createPnrFpRecord(String decodedText, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrFp pnrFp = new MnjxPnrFp();
        pnrFp.setPnrFpId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrFp.setPnrId(pnr.getPnrId());
        pnrFp.setPnrIndex(0); // 后续重新排序
        pnrFp.setInputValue(decodedText);

        addList.add(pnrFp);
    }

    /**
     * 处理EI信息
     */
    private void processEiInfos(List<String> eiInfos, UpdatePnrDto.ManualFare manualFare,
                                MnjxPnr pnr, List<Object> addList) throws SguiResultException {
        for (String eiInfo : eiInfos) {
            // Base64解码
            String decodedText = new String(Base64.getDecoder().decode(eiInfo), StandardCharsets.UTF_8);

            Map<String, List<Integer>> pendingTicketPaxId = manualFare.getPendingTicketPaxId();

            if (pendingTicketPaxId != null && !pendingTicketPaxId.isEmpty()) {
                // 旅客级别EI
                this.createNmEiRecords(decodedText, pendingTicketPaxId, pnr, addList);
            } else {
                // PNR级别EI
                this.createPnrEiRecord(decodedText, pnr, addList);
            }
        }
    }

    /**
     * 创建旅客级别EI记录
     */
    private void createNmEiRecords(String decodedText, Map<String, List<Integer>> pendingTicketPaxId,
                                   MnjxPnr pnr, List<Object> addList) {
        for (Map.Entry<String, List<Integer>> entry : pendingTicketPaxId.entrySet()) {
            List<Integer> passengerIds = entry.getValue();

            if (CollUtil.isNotEmpty(passengerIds)) {
                for (Integer passengerId : passengerIds) {
                    MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                            .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                            .eq(MnjxPnrNm::getPsgIndex, passengerId)
                            .one();

                    if (pnrNm != null) {
                        MnjxNmEi nmEi = new MnjxNmEi();
                        nmEi.setNmEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                        nmEi.setPnrNmId(pnrNm.getPnrNmId());
                        nmEi.setPnrIndex(0); // 后续重新排序

                        // 修改文本中的旅客引用
                        String modifiedText = decodedText.replaceAll("/P\\d+(/\\d+)*", "/P" + passengerId);
                        nmEi.setInputValue(modifiedText);

                        addList.add(nmEi);
                    }
                }
            }
        }
    }

    /**
     * 创建PNR级别EI记录
     */
    private void createPnrEiRecord(String decodedText, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrEi pnrEi = new MnjxPnrEi();
        pnrEi.setPnrEiId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrEi.setPnrId(pnr.getPnrId());
        pnrEi.setPnrIndex(0); // 后续重新排序
        pnrEi.setInputValue(decodedText);

        addList.add(pnrEi);
    }

    /**
     * 修改手工运价
     */
    private void modifyManualFare(UpdatePnrDto.ManualFare manualFare, MnjxPnr pnr,
                                  List<Object> updateList, List<Object> deleteList) throws SguiResultException {
        // 先删除原有记录
        if (CollUtil.isNotEmpty(manualFare.getElementNbrList())) {
            this.deleteManualFareByIndexes(manualFare.getElementNbrList(), pnr, deleteList);
        }

        // 再新增新记录
        this.addManualFare(manualFare, pnr, updateList);
    }

    /**
     * 删除手工运价
     */
    private void deleteManualFare(UpdatePnrDto.ManualFare manualFare, MnjxPnr pnr, List<Object> deleteList) {
        if (CollUtil.isNotEmpty(manualFare.getElementNbrList())) {
            this.deleteManualFareByIndexes(manualFare.getElementNbrList(), pnr, deleteList);
        }
    }

    /**
     * 根据索引删除手工运价
     */
    private void deleteManualFareByIndexes(List<Integer> elementNbrList, MnjxPnr pnr, List<Object> deleteList) {
        for (Integer index : elementNbrList) {
            MnjxPnrRecord record = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrRecord::getPnrIndex, index)
                    .isNull(MnjxPnrRecord::getChangeMark)
                    .one();

            if (record != null) {
                String pnrType = record.getPnrType();
                switch (pnrType) {
                    case "FC":
                        this.deletePnrFcByIndex(index, pnr, deleteList);
                        break;
                    case "FN":
                        this.deletePnrFnByIndex(index, pnr, deleteList);
                        break;
                    case "FP":
                        this.deletePnrFpByIndex(index, pnr, deleteList);
                        break;
                    case "EI":
                        this.deletePnrEiByIndex(index, pnr, deleteList);
                        break;
                    case "NM FC":
                        this.deleteNmFcByIndex(index, pnr, deleteList);
                        break;
                    case "NM FN":
                        this.deleteNmFnByIndex(index, pnr, deleteList);
                        break;
                    case "NM FP":
                        this.deleteNmFpByIndex(index, pnr, deleteList);
                        break;
                    case "NM EI":
                        this.deleteNmEiByIndex(index, pnr, deleteList);
                        break;
                }

                // 标记记录为删除
                record.setChangeMark("X");
                deleteList.add(record);
            }
        }
    }

    /**
     * 删除PNR FC记录
     */
    private void deletePnrFcByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrFc pnrFc = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrFc::getPnrIndex, index)
                .one();
        if (pnrFc != null) {
            deleteList.add(pnrFc);
        }
    }

    /**
     * 删除PNR FN记录
     */
    private void deletePnrFnByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrFn::getPnrIndex, index)
                .one();
        if (pnrFn != null) {
            deleteList.add(pnrFn);
        }
    }

    /**
     * 删除PNR FP记录
     */
    private void deletePnrFpByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrFp pnrFp = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrFp::getPnrIndex, index)
                .one();
        if (pnrFp != null) {
            deleteList.add(pnrFp);
        }
    }

    /**
     * 删除PNR EI记录
     */
    private void deletePnrEiByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        MnjxPnrEi pnrEi = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .eq(MnjxPnrEi::getPnrIndex, index)
                .one();
        if (pnrEi != null) {
            deleteList.add(pnrEi);
        }
    }

    /**
     * 删除旅客FC记录
     */
    private void deleteNmFcByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        List<MnjxNmFc> nmFcList = iMnjxNmFcService.lambdaQuery()
                .eq(MnjxNmFc::getPnrIndex, index)
                .list();
        deleteList.addAll(nmFcList);
    }

    /**
     * 删除旅客FN记录
     */
    private void deleteNmFnByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        List<MnjxNmFn> nmFnList = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrIndex, index)
                .list();
        deleteList.addAll(nmFnList);
    }

    /**
     * 删除旅客FP记录
     */
    private void deleteNmFpByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        List<MnjxNmFp> nmFpList = iMnjxNmFpService.lambdaQuery()
                .eq(MnjxNmFp::getPnrIndex, index)
                .list();
        deleteList.addAll(nmFpList);
    }

    /**
     * 删除旅客EI记录
     */
    private void deleteNmEiByIndex(Integer index, MnjxPnr pnr, List<Object> deleteList) {
        List<MnjxNmEi> nmEiList = iMnjxNmEiService.lambdaQuery()
                .eq(MnjxNmEi::getPnrIndex, index)
                .list();
        deleteList.addAll(nmEiList);
    }

    /**
     * 处理航段信息变更
     */
    private void processSegments(List<UpdatePnrDto.Segment> segments, MnjxPnr pnr,
                                 List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        if (CollUtil.isEmpty(segments)) {
            return;
        }

        for (UpdatePnrDto.Segment segment : segments) {
            switch (segment.getOperateType()) {
                case "A":
                    this.addSegment(segment, pnr, addList);
                    break;
                case "M":
                    // 修改航段暂不处理
                    break;
                case "D":
                    this.deleteSegment(segment, pnr, deleteList);
                    break;
            }
        }
    }

    /**
     * 新增航段
     */
    private void addSegment(UpdatePnrDto.Segment segment, MnjxPnr pnr, List<Object> addList) {
        MnjxPnrSeg pnrSeg = new MnjxPnrSeg();
        pnrSeg.setPnrSegId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrSeg.setPnrId(pnr.getPnrId());
        pnrSeg.setPnrIndex(0); // 后续重新排序
        pnrSeg.setFlightNo(segment.getFlightNo());
        pnrSeg.setSellCabin(segment.getCabin());
        pnrSeg.setOrg(segment.getDepartureAirport());
        pnrSeg.setDst(segment.getArrivalAirport());
        pnrSeg.setFlightDate(DateUtils.ymd2Com(segment.getDepartureDateTime()));

        // 获取当前最大航段序号
        MnjxPnrSeg maxPnrSeg = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByDesc(MnjxPnrSeg::getPnrSegNo)
                .last("LIMIT 1")
                .one();
        Integer maxSegNo = maxPnrSeg != null ? maxPnrSeg.getPnrSegNo() : 0;

        pnrSeg.setPnrSegNo(maxSegNo == null ? 1 : maxSegNo + 1);

        String inputValue = StrUtil.format("{} {} {} {} {}",
                segment.getFlightNo(), segment.getCabin(), segment.getDepartureAirport(),
                segment.getArrivalAirport(), segment.getDepartureDateTime());
        pnrSeg.setInputValue(inputValue);

        addList.add(pnrSeg);
    }

    /**
     * 删除航段
     */
    private void deleteSegment(UpdatePnrDto.Segment segment, MnjxPnr pnr, List<Object> deleteList) {
        if (CollUtil.isNotEmpty(segment.getDeleteIndexList())) {
            for (Integer index : segment.getDeleteIndexList()) {
                MnjxPnrSeg pnrSeg = iMnjxPnrSegService.lambdaQuery()
                        .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrSeg::getPnrIndex, index)
                        .one();
                if (pnrSeg != null) {
                    deleteList.add(pnrSeg);
                }

                // 标记记录为删除
                MnjxPnrRecord record = iMnjxPnrRecordService.lambdaQuery()
                        .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                        .eq(MnjxPnrRecord::getPnrIndex, index)
                        .isNull(MnjxPnrRecord::getChangeMark)
                        .one();
                if (record != null) {
                    record.setChangeMark("X");
                    deleteList.add(record);
                }
            }
        }
    }

    /**
     * 处理自动运价
     */
    private void processAutoFares(List<UpdatePnrDto.AutoFare> autoFares, MnjxPnr pnr,
                                  List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        if (CollUtil.isEmpty(autoFares)) {
            return;
        }

        for (UpdatePnrDto.AutoFare autoFare : autoFares) {
            switch (autoFare.getOperateType()) {
                case "A":
                    this.addAutoFare(autoFare, pnr, addList);
                    break;
                case "M":
                    // 修改自动运价暂不处理
                    break;
                case "D":
                    // 删除自动运价暂不处理
                    break;
            }
        }
    }

    /**
     * 新增自动运价
     */
    private void addAutoFare(UpdatePnrDto.AutoFare autoFare, MnjxPnr pnr, List<Object> addList) {
        // 自动运价处理逻辑参考bookPnr中的processAutoFare方法
        // 这里简化处理，实际需要根据具体需求实现
        log.info("处理自动运价: {}", autoFare);
    }

    /**
     * 处理婴儿SSR INFT修改
     */
    private void processInfantSsrUpdate(MnjxPnr pnr) {
        // 查找PNR中的婴儿
        List<MnjxNmXn> infantList = iMnjxNmXnService.lambdaQuery()
                .in(MnjxNmXn::getPnrNmId,
                    iMnjxPnrNmService.lambdaQuery()
                            .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                            .list()
                            .stream()
                            .map(MnjxPnrNm::getPnrNmId)
                            .collect(Collectors.toList()))
                .list();

        if (CollUtil.isNotEmpty(infantList)) {
            // 查找婴儿的SSR INFT记录
            for (MnjxNmXn infant : infantList) {
                List<MnjxNmSsr> ssrList = iMnjxNmSsrService.lambdaQuery()
                        .eq(MnjxNmSsr::getPnrNmId, infant.getPnrNmId())
                        .eq(MnjxNmSsr::getSsrType, "INFT")
                        .ne(MnjxNmSsr::getActionCode, "HK")
                        .list();

                for (MnjxNmSsr ssr : ssrList) {
                    // 修改行动代码为HK
                    ssr.setActionCode("HK");

                    // 更新ssr_info和input_value中的行动代码
                    String ssrInfo = ssr.getSsrInfo();
                    String inputValue = ssr.getInputValue();

                    if (StrUtil.isNotEmpty(ssrInfo)) {
                        ssrInfo = ssrInfo.replaceAll("\\b\\w{2}\\d+", "HK1");
                        ssr.setSsrInfo(ssrInfo);
                    }

                    if (StrUtil.isNotEmpty(inputValue)) {
                        inputValue = inputValue.replaceAll("\\b\\w{2}\\d+", "HK1");
                        ssr.setInputValue(inputValue);
                    }

                    // 更新数据库
                    iMnjxNmSsrService.updateById(ssr);
                }
            }
        }
    }

    /**
     * 重新排序所有项的pnr_index
     */
    private void reorderAllPnrIndexes(MnjxPnr pnr) throws SguiResultException {
        // 查询所有PNR相关数据
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        List<MnjxPnrCt> pnrCtList = iMnjxPnrCtService.lambdaQuery()
                .eq(MnjxPnrCt::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrOsi> pnrOsiList = iMnjxPnrOsiService.lambdaQuery()
                .eq(MnjxPnrOsi::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrRmk> pnrRmkList = iMnjxPnrRmkService.lambdaQuery()
                .eq(MnjxPnrRmk::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrTk> pnrTkList = iMnjxPnrTkService.lambdaQuery()
                .eq(MnjxPnrTk::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrFc> pnrFcList = iMnjxPnrFcService.lambdaQuery()
                .eq(MnjxPnrFc::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrFp> pnrFpList = iMnjxPnrFpService.lambdaQuery()
                .eq(MnjxPnrFp::getPnrId, pnr.getPnrId())
                .list();

        List<MnjxPnrEi> pnrEiList = iMnjxPnrEiService.lambdaQuery()
                .eq(MnjxPnrEi::getPnrId, pnr.getPnrId())
                .list();

        // 查询旅客级别数据
        List<String> pnrNmIds = pnrNmList.stream()
                .map(MnjxPnrNm::getPnrNmId)
                .collect(Collectors.toList());

        List<MnjxNmSsr> nmSsrList = new ArrayList<>();
        List<MnjxNmOsi> nmOsiList = new ArrayList<>();
        List<MnjxNmRmk> nmRmkList = new ArrayList<>();
        List<MnjxNmFc> nmFcList = new ArrayList<>();
        List<MnjxNmFn> nmFnList = new ArrayList<>();
        List<MnjxNmFp> nmFpList = new ArrayList<>();
        List<MnjxNmEi> nmEiList = new ArrayList<>();
        List<MnjxNmXn> nmXnList = new ArrayList<>();

        if (CollUtil.isNotEmpty(pnrNmIds)) {
            nmSsrList = iMnjxNmSsrService.lambdaQuery()
                    .in(MnjxNmSsr::getPnrNmId, pnrNmIds)
                    .list();

            nmOsiList = iMnjxNmOsiService.lambdaQuery()
                    .in(MnjxNmOsi::getPnrNmId, pnrNmIds)
                    .list();

            nmRmkList = iMnjxNmRmkService.lambdaQuery()
                    .in(MnjxNmRmk::getPnrNmId, pnrNmIds)
                    .list();

            nmFcList = iMnjxNmFcService.lambdaQuery()
                    .in(MnjxNmFc::getPnrNmId, pnrNmIds)
                    .list();

            nmFnList = iMnjxNmFnService.lambdaQuery()
                    .in(MnjxNmFn::getPnrNmId, pnrNmIds)
                    .list();

            nmFpList = iMnjxNmFpService.lambdaQuery()
                    .in(MnjxNmFp::getPnrNmId, pnrNmIds)
                    .list();

            nmEiList = iMnjxNmEiService.lambdaQuery()
                    .in(MnjxNmEi::getPnrNmId, pnrNmIds)
                    .list();

            nmXnList = iMnjxNmXnService.lambdaQuery()
                    .in(MnjxNmXn::getPnrNmId, pnrNmIds)
                    .list();
        }

        // 重新排序并更新索引
        this.reorderAndUpdateIndexes(pnr, pnrNmList, pnrSegList, pnrCtList, pnrOsiList, pnrRmkList,
                pnrTkList, pnrFcList, pnrFnList, pnrFpList, pnrEiList,
                nmSsrList, nmOsiList, nmRmkList, nmFcList, nmFnList, nmFpList, nmEiList, nmXnList);
    }

    /**
     * 重新排序并更新索引
     */
    private void reorderAndUpdateIndexes(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                                         List<MnjxPnrCt> pnrCtList, List<MnjxPnrOsi> pnrOsiList, List<MnjxPnrRmk> pnrRmkList,
                                         List<MnjxPnrTk> pnrTkList, List<MnjxPnrFc> pnrFcList, List<MnjxPnrFn> pnrFnList,
                                         List<MnjxPnrFp> pnrFpList, List<MnjxPnrEi> pnrEiList,
                                         List<MnjxNmSsr> nmSsrList, List<MnjxNmOsi> nmOsiList, List<MnjxNmRmk> nmRmkList,
                                         List<MnjxNmFc> nmFcList, List<MnjxNmFn> nmFnList, List<MnjxNmFp> nmFpList,
                                         List<MnjxNmEi> nmEiList, List<MnjxNmXn> nmXnList) {
        int newIndex = 1;
        List<MnjxPnrRecord> pnrRecordList = new ArrayList<>();

        // 1. pnr nm组（按psgIndex排序）
        if (CollUtil.isNotEmpty(pnrNmList)) {
            pnrNmList.sort(Comparator.comparing(MnjxPnrNm::getPsgIndex));
            for (MnjxPnrNm pnrNm : pnrNmList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrNm.getInputValue(), "NM"));
                pnrNm.setPnrIndex(newIndex++);
            }
        }

        // 2. pnr seg组
        if (CollUtil.isNotEmpty(pnrSegList)) {
            for (MnjxPnrSeg pnrSeg : pnrSegList) {
                MnjxPnrRecord pnrRecord = this.constructRecord(pnr.getPnrId(), newIndex, pnrSeg.getInputValue(), "SEG");
                pnrRecord.setPnrSegId(pnrSeg.getPnrSegId());
                pnrRecordList.add(pnrRecord);
                pnrSeg.setPnrIndex(newIndex++);
            }
        }

        // 3. pnr ct组
        if (CollUtil.isNotEmpty(pnrCtList)) {
            for (MnjxPnrCt pnrCt : pnrCtList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrCt.getInputValue(), "CT"));
                pnrCt.setPnrIndex(newIndex++);
            }
        }

        // 4. pnr tk组
        if (CollUtil.isNotEmpty(pnrTkList)) {
            for (MnjxPnrTk pnrTk : pnrTkList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrTk.getInputValue(), "TK"));
                pnrTk.setPnrIndex(newIndex++);
            }
        }

        // 5. pnr fc组
        if (CollUtil.isNotEmpty(pnrFcList)) {
            for (MnjxPnrFc pnrFc : pnrFcList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrFc.getInputValue(), "FC"));
                pnrFc.setPnrIndex(newIndex++);
            }
        }

        // 6. nm fc组
        if (CollUtil.isNotEmpty(nmFcList)) {
            for (MnjxNmFc nmFc : nmFcList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmFc.getInputValue(), "NM FC"));
                nmFc.setPnrIndex(newIndex++);
            }
        }

        // 7. nm ssr组
        if (CollUtil.isNotEmpty(nmSsrList)) {
            for (MnjxNmSsr nmSsr : nmSsrList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmSsr.getInputValue(), "NM SSR"));
                nmSsr.setPnrIndex(newIndex++);
            }
        }

        // 8. pnr osi组
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            for (MnjxPnrOsi pnrOsi : pnrOsiList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrOsi.getInputValue(), "OSI"));
                pnrOsi.setPnrIndex(newIndex++);
            }
        }

        // 9. nm osi组
        if (CollUtil.isNotEmpty(nmOsiList)) {
            for (MnjxNmOsi nmOsi : nmOsiList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmOsi.getInputValue(), "NM OSI"));
                nmOsi.setPnrIndex(newIndex++);
            }
        }

        // 10. pnr rmk组
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            for (MnjxPnrRmk pnrRmk : pnrRmkList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrRmk.getInputValue(), "RMK"));
                pnrRmk.setPnrIndex(newIndex++);
            }
        }

        // 11. nm rmk组
        if (CollUtil.isNotEmpty(nmRmkList)) {
            for (MnjxNmRmk nmRmk : nmRmkList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmRmk.getInputValue(), "NM RMK"));
                nmRmk.setPnrIndex(newIndex++);
            }
        }

        // 12. pnr fn组
        if (CollUtil.isNotEmpty(pnrFnList)) {
            for (MnjxPnrFn pnrFn : pnrFnList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrFn.getInputValue(), "FN"));
                pnrFn.setPnrIndex(newIndex++);
            }
        }

        // 13. nm fn组
        if (CollUtil.isNotEmpty(nmFnList)) {
            for (MnjxNmFn nmFn : nmFnList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmFn.getInputValue(), "NM FN"));
                nmFn.setPnrIndex(newIndex++);
            }
        }

        // 14. pnr ei组
        if (CollUtil.isNotEmpty(pnrEiList)) {
            for (MnjxPnrEi pnrEi : pnrEiList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrEi.getInputValue(), "EI"));
                pnrEi.setPnrIndex(newIndex++);
            }
        }

        // 15. nm ei组
        if (CollUtil.isNotEmpty(nmEiList)) {
            for (MnjxNmEi nmEi : nmEiList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmEi.getInputValue(), "NM EI"));
                nmEi.setPnrIndex(newIndex++);
            }
        }

        // 16. nm xn组
        if (CollUtil.isNotEmpty(nmXnList)) {
            for (MnjxNmXn nmXn : nmXnList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmXn.getInputValue(), "XN"));
                nmXn.setPnrIndex(newIndex++);
            }
        }

        // 17. pnr fp组
        if (CollUtil.isNotEmpty(pnrFpList)) {
            for (MnjxPnrFp pnrFp : pnrFpList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, pnrFp.getInputValue(), "FP"));
                pnrFp.setPnrIndex(newIndex++);
            }
        }

        // 18. nm fp组
        if (CollUtil.isNotEmpty(nmFpList)) {
            for (MnjxNmFp nmFp : nmFpList) {
                pnrRecordList.add(this.constructRecord(pnr.getPnrId(), newIndex, nmFp.getInputValue(), "NM FP"));
                nmFp.setPnrIndex(newIndex++);
            }
        }

        // 更新PNR最大索引值
        pnr.setMaxIndex(newIndex - 1);

        // 批量更新数据库
        this.batchUpdateIndexes(pnr, pnrNmList, pnrSegList, pnrCtList, pnrOsiList, pnrRmkList,
                pnrTkList, pnrFcList, pnrFnList, pnrFpList, pnrEiList,
                nmSsrList, nmOsiList, nmRmkList, nmFcList, nmFnList, nmFpList, nmEiList, nmXnList, pnrRecordList);
    }

    /**
     * 构建PNR记录
     */
    private MnjxPnrRecord constructRecord(String pnrId, Integer pnrIndex, String inputValue, String pnrType) {
        MnjxPnrRecord record = new MnjxPnrRecord();
        record.setPnrRecordId(IdUtil.getSnowflake(1, 1).nextIdStr());
        record.setPnrId(pnrId);
        record.setPnrIndex(pnrIndex);
        record.setInputValue(inputValue);
        record.setPnrType(pnrType);
        record.setChangeMark(null);
        record.setChangeAtNo(null);
        return record;
    }

    /**
     * 批量更新索引
     */
    private void batchUpdateIndexes(MnjxPnr pnr, List<MnjxPnrNm> pnrNmList, List<MnjxPnrSeg> pnrSegList,
                                    List<MnjxPnrCt> pnrCtList, List<MnjxPnrOsi> pnrOsiList, List<MnjxPnrRmk> pnrRmkList,
                                    List<MnjxPnrTk> pnrTkList, List<MnjxPnrFc> pnrFcList, List<MnjxPnrFn> pnrFnList,
                                    List<MnjxPnrFp> pnrFpList, List<MnjxPnrEi> pnrEiList,
                                    List<MnjxNmSsr> nmSsrList, List<MnjxNmOsi> nmOsiList, List<MnjxNmRmk> nmRmkList,
                                    List<MnjxNmFc> nmFcList, List<MnjxNmFn> nmFnList, List<MnjxNmFp> nmFpList,
                                    List<MnjxNmEi> nmEiList, List<MnjxNmXn> nmXnList, List<MnjxPnrRecord> pnrRecordList) {
        // 更新PNR
        iMnjxPnrService.updateById(pnr);

        // 批量更新各项索引
        if (CollUtil.isNotEmpty(pnrNmList)) {
            iMnjxPnrNmService.updateBatchById(pnrNmList);
        }
        if (CollUtil.isNotEmpty(pnrSegList)) {
            iMnjxPnrSegService.updateBatchById(pnrSegList);
        }
        if (CollUtil.isNotEmpty(pnrCtList)) {
            iMnjxPnrCtService.updateBatchById(pnrCtList);
        }
        if (CollUtil.isNotEmpty(pnrOsiList)) {
            iMnjxPnrOsiService.updateBatchById(pnrOsiList);
        }
        if (CollUtil.isNotEmpty(pnrRmkList)) {
            iMnjxPnrRmkService.updateBatchById(pnrRmkList);
        }
        if (CollUtil.isNotEmpty(pnrTkList)) {
            iMnjxPnrTkService.updateBatchById(pnrTkList);
        }
        if (CollUtil.isNotEmpty(pnrFcList)) {
            iMnjxPnrFcService.updateBatchById(pnrFcList);
        }
        if (CollUtil.isNotEmpty(pnrFnList)) {
            iMnjxPnrFnService.updateBatchById(pnrFnList);
        }
        if (CollUtil.isNotEmpty(pnrFpList)) {
            iMnjxPnrFpService.updateBatchById(pnrFpList);
        }
        if (CollUtil.isNotEmpty(pnrEiList)) {
            iMnjxPnrEiService.updateBatchById(pnrEiList);
        }
        if (CollUtil.isNotEmpty(nmSsrList)) {
            iMnjxNmSsrService.updateBatchById(nmSsrList);
        }
        if (CollUtil.isNotEmpty(nmOsiList)) {
            iMnjxNmOsiService.updateBatchById(nmOsiList);
        }
        if (CollUtil.isNotEmpty(nmRmkList)) {
            iMnjxNmRmkService.updateBatchById(nmRmkList);
        }
        if (CollUtil.isNotEmpty(nmFcList)) {
            iMnjxNmFcService.updateBatchById(nmFcList);
        }
        if (CollUtil.isNotEmpty(nmFnList)) {
            iMnjxNmFnService.updateBatchById(nmFnList);
        }
        if (CollUtil.isNotEmpty(nmFpList)) {
            iMnjxNmFpService.updateBatchById(nmFpList);
        }
        if (CollUtil.isNotEmpty(nmEiList)) {
            iMnjxNmEiService.updateBatchById(nmEiList);
        }
        if (CollUtil.isNotEmpty(nmXnList)) {
            iMnjxNmXnService.updateBatchById(nmXnList);
        }

        // 删除旧的记录并插入新的记录
        iMnjxPnrRecordService.lambdaUpdate()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .remove();

        if (CollUtil.isNotEmpty(pnrRecordList)) {
            iMnjxPnrRecordService.saveBatch(pnrRecordList);
        }
    }

    /**
     * 批量执行数据库操作
     */
    private void batchExecuteOperations(List<Object> addList, List<Object> updateList, List<Object> deleteList) {
        // 批量新增
        if (CollUtil.isNotEmpty(addList)) {
            this.batchSave(addList);
        }

        // 批量更新
        if (CollUtil.isNotEmpty(updateList)) {
            this.batchUpdate(updateList);
        }

        // 批量删除
        if (CollUtil.isNotEmpty(deleteList)) {
            this.batchDelete(deleteList);
        }
    }

    /**
     * 批量保存
     */
    private void batchSave(List<Object> addList) {
        // 按类型分组批量保存
        Map<Class<?>, List<Object>> groupedMap = addList.stream()
                .collect(Collectors.groupingBy(Object::getClass));

        for (Map.Entry<Class<?>, List<Object>> entry : groupedMap.entrySet()) {
            Class<?> clazz = entry.getKey();
            List<Object> objects = entry.getValue();

            if (clazz == MnjxPnrNm.class) {
                iMnjxPnrNmService.saveBatch((List<MnjxPnrNm>) objects);
            } else if (clazz == MnjxPnrSeg.class) {
                iMnjxPnrSegService.saveBatch((List<MnjxPnrSeg>) objects);
            } else if (clazz == MnjxPnrCt.class) {
                iMnjxPnrCtService.saveBatch((List<MnjxPnrCt>) objects);
            } else if (clazz == MnjxPnrOsi.class) {
                iMnjxPnrOsiService.saveBatch((List<MnjxPnrOsi>) objects);
            } else if (clazz == MnjxPnrRmk.class) {
                iMnjxPnrRmkService.saveBatch((List<MnjxPnrRmk>) objects);
            } else if (clazz == MnjxPnrTk.class) {
                iMnjxPnrTkService.saveBatch((List<MnjxPnrTk>) objects);
            } else if (clazz == MnjxPnrFc.class) {
                iMnjxPnrFcService.saveBatch((List<MnjxPnrFc>) objects);
            } else if (clazz == MnjxPnrFn.class) {
                iMnjxPnrFnService.saveBatch((List<MnjxPnrFn>) objects);
            } else if (clazz == MnjxPnrFp.class) {
                iMnjxPnrFpService.saveBatch((List<MnjxPnrFp>) objects);
            } else if (clazz == MnjxPnrEi.class) {
                iMnjxPnrEiService.saveBatch((List<MnjxPnrEi>) objects);
            } else if (clazz == MnjxNmSsr.class) {
                iMnjxNmSsrService.saveBatch((List<MnjxNmSsr>) objects);
            } else if (clazz == MnjxNmOsi.class) {
                iMnjxNmOsiService.saveBatch((List<MnjxNmOsi>) objects);
            } else if (clazz == MnjxNmRmk.class) {
                iMnjxNmRmkService.saveBatch((List<MnjxNmRmk>) objects);
            } else if (clazz == MnjxNmFc.class) {
                iMnjxNmFcService.saveBatch((List<MnjxNmFc>) objects);
            } else if (clazz == MnjxNmFn.class) {
                iMnjxNmFnService.saveBatch((List<MnjxNmFn>) objects);
            } else if (clazz == MnjxNmFp.class) {
                iMnjxNmFpService.saveBatch((List<MnjxNmFp>) objects);
            } else if (clazz == MnjxNmEi.class) {
                iMnjxNmEiService.saveBatch((List<MnjxNmEi>) objects);
            } else if (clazz == MnjxNmXn.class) {
                iMnjxNmXnService.saveBatch((List<MnjxNmXn>) objects);
            }
        }
    }

    /**
     * 批量更新
     */
    private void batchUpdate(List<Object> updateList) {
        // 按类型分组批量更新
        Map<Class<?>, List<Object>> groupedMap = updateList.stream()
                .collect(Collectors.groupingBy(Object::getClass));

        for (Map.Entry<Class<?>, List<Object>> entry : groupedMap.entrySet()) {
            Class<?> clazz = entry.getKey();
            List<Object> objects = entry.getValue();

            if (clazz == MnjxPnrNm.class) {
                iMnjxPnrNmService.updateBatchById((List<MnjxPnrNm>) objects);
            } else if (clazz == MnjxPnrSeg.class) {
                iMnjxPnrSegService.updateBatchById((List<MnjxPnrSeg>) objects);
            } else if (clazz == MnjxPnrCt.class) {
                iMnjxPnrCtService.updateBatchById((List<MnjxPnrCt>) objects);
            } else if (clazz == MnjxPnrOsi.class) {
                iMnjxPnrOsiService.updateBatchById((List<MnjxPnrOsi>) objects);
            } else if (clazz == MnjxPnrRmk.class) {
                iMnjxPnrRmkService.updateBatchById((List<MnjxPnrRmk>) objects);
            } else if (clazz == MnjxPnrTk.class) {
                iMnjxPnrTkService.updateBatchById((List<MnjxPnrTk>) objects);
            } else if (clazz == MnjxPnrFc.class) {
                iMnjxPnrFcService.updateBatchById((List<MnjxPnrFc>) objects);
            } else if (clazz == MnjxPnrFn.class) {
                iMnjxPnrFnService.updateBatchById((List<MnjxPnrFn>) objects);
            } else if (clazz == MnjxPnrFp.class) {
                iMnjxPnrFpService.updateBatchById((List<MnjxPnrFp>) objects);
            } else if (clazz == MnjxPnrEi.class) {
                iMnjxPnrEiService.updateBatchById((List<MnjxPnrEi>) objects);
            } else if (clazz == MnjxNmSsr.class) {
                iMnjxNmSsrService.updateBatchById((List<MnjxNmSsr>) objects);
            } else if (clazz == MnjxNmOsi.class) {
                iMnjxNmOsiService.updateBatchById((List<MnjxNmOsi>) objects);
            } else if (clazz == MnjxNmRmk.class) {
                iMnjxNmRmkService.updateBatchById((List<MnjxNmRmk>) objects);
            } else if (clazz == MnjxNmFc.class) {
                iMnjxNmFcService.updateBatchById((List<MnjxNmFc>) objects);
            } else if (clazz == MnjxNmFn.class) {
                iMnjxNmFnService.updateBatchById((List<MnjxNmFn>) objects);
            } else if (clazz == MnjxNmFp.class) {
                iMnjxNmFpService.updateBatchById((List<MnjxNmFp>) objects);
            } else if (clazz == MnjxNmEi.class) {
                iMnjxNmEiService.updateBatchById((List<MnjxNmEi>) objects);
            } else if (clazz == MnjxNmXn.class) {
                iMnjxNmXnService.updateBatchById((List<MnjxNmXn>) objects);
            } else if (clazz == MnjxPnrRecord.class) {
                iMnjxPnrRecordService.updateBatchById((List<MnjxPnrRecord>) objects);
            }
        }
    }

    /**
     * 批量删除
     */
    private void batchDelete(List<Object> deleteList) {
        // 按类型分组批量删除
        Map<Class<?>, List<Object>> groupedMap = deleteList.stream()
                .collect(Collectors.groupingBy(Object::getClass));

        for (Map.Entry<Class<?>, List<Object>> entry : groupedMap.entrySet()) {
            Class<?> clazz = entry.getKey();
            List<Object> objects = entry.getValue();

            if (clazz == MnjxPnrNm.class) {
                List<String> ids = ((List<MnjxPnrNm>) objects).stream()
                        .map(MnjxPnrNm::getPnrNmId)
                        .collect(Collectors.toList());
                iMnjxPnrNmService.removeByIds(ids);
            } else if (clazz == MnjxPnrSeg.class) {
                List<String> ids = ((List<MnjxPnrSeg>) objects).stream()
                        .map(MnjxPnrSeg::getPnrSegId)
                        .collect(Collectors.toList());
                iMnjxPnrSegService.removeByIds(ids);
            } else if (clazz == MnjxPnrCt.class) {
                List<String> ids = ((List<MnjxPnrCt>) objects).stream()
                        .map(MnjxPnrCt::getPnrCtId)
                        .collect(Collectors.toList());
                iMnjxPnrCtService.removeByIds(ids);
            } else if (clazz == MnjxPnrOsi.class) {
                List<String> ids = ((List<MnjxPnrOsi>) objects).stream()
                        .map(MnjxPnrOsi::getPnrOsiId)
                        .collect(Collectors.toList());
                iMnjxPnrOsiService.removeByIds(ids);
            } else if (clazz == MnjxPnrRmk.class) {
                List<String> ids = ((List<MnjxPnrRmk>) objects).stream()
                        .map(MnjxPnrRmk::getPnrRmkId)
                        .collect(Collectors.toList());
                iMnjxPnrRmkService.removeByIds(ids);
            } else if (clazz == MnjxPnrTk.class) {
                List<String> ids = ((List<MnjxPnrTk>) objects).stream()
                        .map(MnjxPnrTk::getPnrTkId)
                        .collect(Collectors.toList());
                iMnjxPnrTkService.removeByIds(ids);
            } else if (clazz == MnjxPnrFc.class) {
                List<String> ids = ((List<MnjxPnrFc>) objects).stream()
                        .map(MnjxPnrFc::getPnrFcId)
                        .collect(Collectors.toList());
                iMnjxPnrFcService.removeByIds(ids);
            } else if (clazz == MnjxPnrFn.class) {
                List<String> ids = ((List<MnjxPnrFn>) objects).stream()
                        .map(MnjxPnrFn::getPnrFnId)
                        .collect(Collectors.toList());
                iMnjxPnrFnService.removeByIds(ids);
            } else if (clazz == MnjxPnrFp.class) {
                List<String> ids = ((List<MnjxPnrFp>) objects).stream()
                        .map(MnjxPnrFp::getPnrFpId)
                        .collect(Collectors.toList());
                iMnjxPnrFpService.removeByIds(ids);
            } else if (clazz == MnjxPnrEi.class) {
                List<String> ids = ((List<MnjxPnrEi>) objects).stream()
                        .map(MnjxPnrEi::getPnrEiId)
                        .collect(Collectors.toList());
                iMnjxPnrEiService.removeByIds(ids);
            } else if (clazz == MnjxNmSsr.class) {
                List<String> ids = ((List<MnjxNmSsr>) objects).stream()
                        .map(MnjxNmSsr::getNmSsrId)
                        .collect(Collectors.toList());
                iMnjxNmSsrService.removeByIds(ids);
            } else if (clazz == MnjxNmOsi.class) {
                List<String> ids = ((List<MnjxNmOsi>) objects).stream()
                        .map(MnjxNmOsi::getPnrOsiId)
                        .collect(Collectors.toList());
                iMnjxNmOsiService.removeByIds(ids);
            } else if (clazz == MnjxNmRmk.class) {
                List<String> ids = ((List<MnjxNmRmk>) objects).stream()
                        .map(MnjxNmRmk::getNmRmkId)
                        .collect(Collectors.toList());
                iMnjxNmRmkService.removeByIds(ids);
            } else if (clazz == MnjxNmFc.class) {
                List<String> ids = ((List<MnjxNmFc>) objects).stream()
                        .map(MnjxNmFc::getNmFcId)
                        .collect(Collectors.toList());
                iMnjxNmFcService.removeByIds(ids);
            } else if (clazz == MnjxNmFn.class) {
                List<String> ids = ((List<MnjxNmFn>) objects).stream()
                        .map(MnjxNmFn::getNmFnId)
                        .collect(Collectors.toList());
                iMnjxNmFnService.removeByIds(ids);
            } else if (clazz == MnjxNmFp.class) {
                List<String> ids = ((List<MnjxNmFp>) objects).stream()
                        .map(MnjxNmFp::getNmFpId)
                        .collect(Collectors.toList());
                iMnjxNmFpService.removeByIds(ids);
            } else if (clazz == MnjxNmEi.class) {
                List<String> ids = ((List<MnjxNmEi>) objects).stream()
                        .map(MnjxNmEi::getNmEiId)
                        .collect(Collectors.toList());
                iMnjxNmEiService.removeByIds(ids);
            } else if (clazz == MnjxNmXn.class) {
                List<String> ids = ((List<MnjxNmXn>) objects).stream()
                        .map(MnjxNmXn::getNmXnId)
                        .collect(Collectors.toList());
                iMnjxNmXnService.removeByIds(ids);
            } else if (clazz == MnjxPnrRecord.class) {
                List<String> ids = ((List<MnjxPnrRecord>) objects).stream()
                        .map(MnjxPnrRecord::getPnrRecordId)
                        .collect(Collectors.toList());
                iMnjxPnrRecordService.removeByIds(ids);
            }
        }
    }

    /**
     * 生成封口记录
     */
    private void generateSealingRecord(MnjxPnr pnr, String envelopType) throws SguiResultException {
        // 生成新的封口编号
        String newAtNo = iSguiCommonService.generateNewAtNo(pnr.getPnrId());

        // 创建封口记录
        MnjxPnrAt pnrAt = new MnjxPnrAt();
        pnrAt.setPnrAtId(IdUtil.getSnowflake(1, 1).nextIdStr());
        pnrAt.setPnrId(pnr.getPnrId());
        pnrAt.setAtNo(newAtNo);

        // 设置封口类型
        if (StrUtil.isNotEmpty(envelopType)) {
            if ("I".equals(envelopType)) {
                pnrAt.setAtType("I");
            } else if ("KI".equals(envelopType)) {
                pnrAt.setAtType("KI");
            }
        }

        // 获取当前用户信息
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        if (userInfo != null) {
            pnrAt.setAtSiId(userInfo.getSiId());
        }

        pnrAt.setAtDateTime(new Date());

        // 保存封口记录
        iMnjxPnrAtService.save(pnrAt);

        // 更新PNR记录中的changeAtNo
        this.updatePnrRecordChangeAtNo(pnr.getPnrId(), newAtNo);
    }

    /**
     * 更新PNR记录的changeAtNo
     */
    private void updatePnrRecordChangeAtNo(String pnrId, String newAtNo) {
        List<MnjxPnrRecord> recordList = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnrId)
                .isNull(MnjxPnrRecord::getChangeMark)
                .list();

        for (MnjxPnrRecord record : recordList) {
            record.setChangeAtNo(newAtNo);
        }

        if (CollUtil.isNotEmpty(recordList)) {
            iMnjxPnrRecordService.updateBatchById(recordList);
        }
    }
}
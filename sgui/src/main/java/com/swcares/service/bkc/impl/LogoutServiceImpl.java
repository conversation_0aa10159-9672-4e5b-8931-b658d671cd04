package com.swcares.service.bkc.impl;

import com.swcares.core.security.TokenJwtManager;
import com.swcares.core.utils.RedisKeyUtils;
import com.swcares.service.bkc.ILogoutService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:10
 */
@Service
public class LogoutServiceImpl implements ILogoutService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private TokenJwtManager tokenJwtManager;

    @Override
    public void logout(HttpServletRequest request, String token) {
        // 解析用户名
        String username = tokenJwtManager.parseToken(token.replace("Bearer ", ""));

        // 删除Redis中的用户信息
        String key = RedisKeyUtils.getUserInfoKey(username + ":" + RedisKeyUtils.getSguiSessionId(request));
        stringRedisTemplate.delete(key);

        // 删除占位缓存
        String porCacheKey = "POR:" + username;
        stringRedisTemplate.delete(porCacheKey);

        SecurityContextHolder.clearContext();
    }
}

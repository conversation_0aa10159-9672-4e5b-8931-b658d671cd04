package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.UpdatePnrDto;
import com.swcares.obj.vo.UpdatePnrVo;

/**
 * 更新PNR服务接口
 *
 * <AUTHOR>
 * @date 2025/5/26 16:00
 */
public interface IUpdatePnrService {

    /**
     * 更新PNR
     *
     * @param dto 更新PNR请求参数
     * @return 更新结果
     * @throws SguiResultException 异常
     */
    UpdatePnrVo updatePnr(UpdatePnrDto dto) throws SguiResultException;
}

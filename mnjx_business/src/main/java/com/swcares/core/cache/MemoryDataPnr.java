package com.swcares.core.cache;

import cn.hutool.core.collection.CollUtil;
import com.swcares.core.util.IdUtils;
import com.swcares.entity.MnjxPnr;
import com.swcares.entity.MnjxPnrAt;
import com.swcares.entity.MnjxPnrRecord;
import com.swcares.entity.MnjxSpInfo;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.PatVoA;
import com.swcares.obj.vo.TriVo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class MemoryDataPnr implements Serializable {

    @Getter
    private final String memoryDataPnrId;

    {
        memoryDataPnrId = IdUtils.getId();
    }


    public MemoryDataPnr deepCopy() throws IOException, ClassNotFoundException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(bos);
        oos.writeObject(this);

        ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
        ObjectInputStream ois = new ObjectInputStream(bis);
        return (MemoryDataPnr) ois.readObject();
    }

    /**
     * PNR基础属性项(主对象)
     */
    @Getter
    @Setter
    private MnjxPnr mnjxPnr = new MnjxPnr();

    /**
     * 1 这个最多5段，包含缺口段 ,航段组
     */
    @Getter
    @Setter
    private List<PnrSegDto> pnrSegDtos = new Vector<>();

    /**
     * 团队组（如果有的情况下添加）
     * 这个有多个吗？？？
     */
    @Getter
    @Setter
    private List<PnrGnDto> pnrGnDtos = new Vector<>();

    /**
     * 2 姓名相关项
     */
    @Getter
    @Setter
    private List<PnrNmDto> pnrNmDtos = new Vector<>();

    /**
     * 出票组
     * 活动的出票组只允许有一项，但是在封口前可以添加多项，多项时在封口时再报错
     */
    @Getter
    @Setter
    private List<PnrTkDto> pnrTkDtos = new Vector<>();

    /**
     * 联系组
     */
    @Getter
    @Setter
    private List<PnrCtDto> pnrCtDtos = new Vector<>();

    /**
     * 其他组
     */
    @Getter
    @Setter
    private List<PnrOsiDto> pnrOsiDtos = new Vector<>();

    /**
     * 票价支付组
     */
    @Getter
    @Setter
    private List<PnrFpDto> pnrFpDtos = new Vector<>();

    /**
     * 票价计算组
     */
    @Getter
    @Setter
    private List<PnrFcDto> pnrFcDtos = new Vector<>();

    /**
     * 票价单位组
     */
    @Getter
    @Setter
    private List<PnrFnDto> pnrFnDtos = new Vector<>();

    /**
     * 旅游代码组
     */
    @Getter
    @Setter
    private List<PnrTcDto> pnrTcDtos = new Vector<>();

    /**
     * 备注组
     */
    @Getter
    @Setter
    private List<PnrRmkDto> pnrRmkDtos = new Vector<>();

    /**
     * EI 签注信息组
     */
    @Getter
    @Setter
    private List<PnrEiDto> pnrEiDtos = new Vector<>();
    /**
     * 记录PNR封口的历史信息
     * 例：001 CTU001 1001 2300 22JUN
     */
    @Getter
    @Setter
    private List<MnjxPnrAt> mnjxPnrAts = new Vector<>();
    /**
     * TN
     */
    @Getter
    @Setter
    private List<PnrNmTnDto> pnrNmTnDtos = new Vector<>();
    /**
     * PNR各项历史记录
     */
    @Getter
    @Setter
    private List<MnjxPnrRecord> mnjxPnrRecords = new Vector<>();
    /**
     * 用于处理回显排序列表及数据操作类选取序号对应的PNR项
     */
    @Getter
    @Setter
    private List<AbstractPnrDto> abstractPnrDtos = new Vector<>();
    /**
     * 调用回显时用于判断是否已存储了某历史项
     */
    @Getter
    @Setter
    private List<String> existHistoryPnrItem = new Vector<>();
    /**
     * ETDZ出票发生异常时，进行的一系列操作进行还原的数据项
     */
    @Getter
    @Setter
    private EtdzRecallDto etdzRecallDto = new EtdzRecallDto();
    /**
     * PNR项出现了修改
     */
    @Getter
    @Setter
    private boolean pnrChanged;

    /**
     * 执行了RT指令
     */
    @Getter
    @Setter
    private boolean byRt;

    /**
     * RT类型
     */
    @Getter
    @Setter
    private String rtType;

    /**
     * 执行了@K
     */
    @Getter
    @Setter
    private boolean byAtK;

    /**
     * 当前的封口次数（封口时使用这个次数）
     */
    @Getter
    @Setter
    private String thisAtNo;

    /**
     * 新建的PNR
     */
    @Getter
    @Setter
    private boolean newPnr;

    /**
     * 封口时是否需要生成航司反馈记录
     * 如果本次提交有新增或变更的内容涉及到旅客信息NM\GN\XN信息及SS\SSR\TK-TL\信息的修改（包括增加、删除、修改等）需要在封口记录后自动 生成一条航司系统反馈记录
     * 相当于2次封口，封口序号会再加一
     */
    @Setter
    @Getter
    private boolean needHdqca = false;

    /**
     * 当前PNR是否被控制
     */
    @Setter
    @Getter
    private boolean beControlled;

    /**
     * 有未封口的新增姓名或修改姓名
     */
    @Setter
    @Getter
    private boolean nmInsertOrUpdate;

    /**
     * 自动运价
     */
    @Setter
    @Getter
    private PatVoA patA;

    /**
     * 自动改期、升降舱
     */
    @Setter
    @Getter
    private TriVo triVo;

    /**
     *
     */
    @Setter
    @Getter
    private boolean isEtdz;

    /**
     * 当前控制PNR计时
     * key:当前memoryDataPnrId + PNR编号； value:控制PNR时的日期
     */
    @Getter
    @Setter
    private Map<String, Date> pnrControlTimeMap = new ConcurrentHashMap();

    @Getter
    @Setter
    private List<MnjxSpInfo> mnjxSpInfoList = new Vector<>();

    /**
     * 清理当前PNR的大对象
     */
    public void clearPnr() {
        // 用一个最新的属性对象（原始对象属性）覆盖现有的对象属性
        BeanUtils.copyProperties(new MnjxPnr(), this.mnjxPnr);
        BeanUtils.copyProperties(new EtdzRecallDto(), this.etdzRecallDto);
        this.mnjxPnrAts.clear();
        CollUtil.clear(this.pnrSegDtos);
        this.pnrSegDtos.clear();
        this.pnrNmDtos.clear();
        this.pnrGnDtos.clear();
        this.pnrTkDtos.clear();
        this.pnrCtDtos.clear();
        this.pnrOsiDtos.clear();
        this.pnrFpDtos.clear();
        this.pnrFcDtos.clear();
        this.pnrFnDtos.clear();
        this.pnrTcDtos.clear();
        this.pnrRmkDtos.clear();
        this.pnrEiDtos.clear();
        this.pnrNmTnDtos.clear();
        this.abstractPnrDtos.clear();
        this.mnjxPnrRecords.clear();
        this.existHistoryPnrItem.clear();
        this.pnrChanged = false;
        this.byRt = false;
        this.rtType = null;
        this.byAtK = false;
        this.thisAtNo = null;
        this.newPnr = false;
        this.needHdqca = false;
        this.beControlled = false;
        this.nmInsertOrUpdate = false;
        this.patA = null;
        this.triVo = null;
        this.isEtdz = false;
        this.pnrControlTimeMap.clear();
        this.mnjxSpInfoList.clear();
    }
}

package com.swcares.obj.type;

import com.swcares.core.util.Constant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * RMK 的类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumRmkType {
    /**
     * 自由文本记录行
     */
    FREE(Constant.RMK_TYPE_FREE),
    /**
     * ICS编码记录行
     */
    ICS(Constant.RMK_TYPE_ICS),
    /**
     * 军警残记录行
     */
    GMJC(Constant.RMK_TYPE_GMJC),
    /**
     * PNR分离标志记录行
     */
    SP(Constant.RMK_TYPE_SP),
    /**
     * 隐私保护记录行
     */
    TJ(Constant.RMK_TYPE_TJ),
    /**
     * TR记录行
     */
    TR(Constant.RMK_TYPE_TR),
    /**
     * 定额代理费记录行
     */
    CMSA(Constant.RMK_TYPE_CMSA);


    @Getter
    private final String describe;

    /**
     * 通过中文获取具体的枚举类型
     *
     * @param describe 中文描述
     * @return 具体的枚举类型
     */
    public static EnumPatType of(String describe) {
        return Arrays.stream(EnumPatType.values()).filter(enumPatType -> enumPatType.getCode().equalsIgnoreCase(describe)).findAny().orElse(null);
    }
}

package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.BeanUtils;
import com.swcares.core.util.Constant;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PnrCommandServicePartRt {
    @Resource
    private IMnjxPnrService iMnjxPnrService;
    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;
    @Resource
    private IMnjxPnrAtService iMnjxPnrAtService;
    @Resource
    private IMnjxPnrGnService iMnjxPnrGnService;
    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;
    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;
    @Resource
    private IMnjxPnrTkService iMnjxPnrTkService;
    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;
    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;
    @Resource
    private IMnjxNmXnService iMnjxNmXnService;
    @Resource
    private IMnjxPnrRmkService iMnjxPnrRmkService;
    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;
    @Resource
    private IMnjxPnrEiService iMnjxPnrEiService;
    @Resource
    private IMnjxPnrFpService iMnjxPnrFpService;
    @Resource
    private IMnjxPnrFcService iMnjxPnrFcService;
    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;
    @Resource
    private IMnjxPnrOsiService iMnjxPnrOsiService;
    @Resource
    private IMnjxPnrTcService iMnjxPnrTcService;
    @Resource
    private IMnjxNmOiService iMnjxNmOiService;
    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;
    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;
    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;
    @Resource
    private IMnjxNmCtService iMnjxNmCtService;
    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;
    @Resource
    private IMnjxNmEiService iMnjxNmEiService;
    @Resource
    private IMnjxNmFpService iMnjxNmFpService;
    @Resource
    private IMnjxNmFcService iMnjxNmFcService;
    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    public void rt(MemoryDataPnr memoryDataPnr, RtDto rtDto) throws UnifiedResultException {
        log.debug("---RT:提取Pnr指令信息[{}]", rtDto);
        if (StrUtil.isNotBlank(rtDto.getExtractParameter()) && StrUtil.isNotBlank(rtDto.getPnrCode())) {
            this.rtByParamAndCode(memoryDataPnr, rtDto.getPnrCode());
        } else if (StrUtil.isBlank(rtDto.getExtractParameter()) && StrUtil.isNotBlank(rtDto.getPnrCode())) {
            //如果是RT:待检索PNR编码
            if (StrUtil.isBlank(memoryDataPnr.getRtType())) {
                rtDto.setExtractParameter("A");
            } else {
                rtDto.setExtractParameter(memoryDataPnr.getRtType());
            }
            this.rtByParamAndCode(memoryDataPnr, rtDto.getPnrCode());
            // 第一次通过指令触发的RT:PNR编号（第一次提取该PNR时），添加当前时间，用于判断PNR控制超时
            this.addPnrControlDateTime(memoryDataPnr, rtDto.getPnrCode());
        } else if (StrUtil.isBlank(rtDto.getPnrCode()) && StrUtil.isNotBlank(rtDto.getExtractParameter())) {
            //如果是RT:提取参数
            //查看是否存在待合并PNR
            if (ObjectUtil.isNull(memoryDataPnr.getMnjxPnr().getPnrId())) {
                throw new UnifiedResultException(Constant.NO_PNR);
            }
        } else {
            //参数与PNR编码都为空
            if (ObjectUtil.isNull(memoryDataPnr.getMnjxPnr().getPnrId())) {
                throw new UnifiedResultException(Constant.NO_PNR);
            }
            if (StrUtil.isBlank(memoryDataPnr.getRtType())) {
                rtDto.setExtractParameter("A");
            } else {
                rtDto.setExtractParameter(memoryDataPnr.getRtType());
            }
        }
    }

    /**
     * Title: addPnrControlDateTime
     * Description: 控制PNR的时间
     *
     * @param memoryDataPnr memoryDataPnr
     * @param pnrCode       pnrCode
     * <AUTHOR>
     * @date 2022/8/29 9:47
     */
    private void addPnrControlDateTime(MemoryDataPnr memoryDataPnr, String pnrCode) {
        String key = StrUtil.format("{}{}", memoryDataPnr.getMemoryDataPnrId(), pnrCode);
        Date value = DateUtil.date();
        Map<String, Date> pnrControlTimeMap = memoryDataPnr.getPnrControlTimeMap();
        if (!pnrControlTimeMap.containsKey(key)) {
            pnrControlTimeMap.put(key, value);
            log.debug(StrUtil.format("用户{}控制PNR:{}开始时间:{}", memoryDataPnr.getMemoryDataPnrId(), pnrCode, DateUtil.formatDateTime(value)));
        }
    }

    /**
     * 根据编码装载MemoryDataPnr对象
     *
     * @param pnrCode 6字编码
     * @throws UnifiedResultException 统一异常
     */
    private void rtByParamAndCode(MemoryDataPnr memoryDataPnr, String pnrCode) throws UnifiedResultException {
        log.debug("-----RT通过pnr获取旅客信息");
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        log.debug("----记录RT前的Pnr:[{}]", mnjxPnr);
        // 当前无活动PNR
        if (ObjectUtil.isNull(mnjxPnr.getPnrId())) {
            bulidPnrDb(pnrCode, memoryDataPnr);
        }
        // 当前有活动PNR
        else {
            MnjxPnr mp = iMnjxPnrService.lambdaQuery().eq(MnjxPnr::getPnrId, mnjxPnr.getPnrId()).one();
            if (ObjectUtil.isNull(mp)) {
                memoryDataPnr.setBeControlled(false);
                //提取编码数据装载进“活动的PNR”
                this.bulidPnrDb(pnrCode, MemoryDataUtils.getMemoryData().getTmpMemoryDataPnr());
            } else {
                //判断当前活动PNR是否有尚未提交的修改
                if (memoryDataPnr.isPnrChanged()) {
                    //如果有修改，则提示：“NEED EOT -- 需要封口(@)或还原(IG)”
                    throw new UnifiedResultException(Constant.NEED_EOT);
                } else {
                    //如果无修改，则根据提取编码提取数据装载进“活动PNR”
                    memoryDataPnr.clearPnr();
                    //当前活动PNR无修改
//	                iPnrCommandService.rt(memoryDataPnr, pnrCode);
                    this.bulidPnrDb(pnrCode, memoryDataPnr);
                }
            }
        }
        log.debug("----记录RT前的Pnr:[{}]", memoryDataPnr.getMnjxPnr());
    }

    private void bulidPnrDb(String pnrCode, MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        memoryDataPnr.setBeControlled(true);
        MnjxPnr pnrDb = iMnjxPnrService.lambdaQuery().eq(MnjxPnr::getPnrCrs, pnrCode).one();
        if (ObjectUtil.isNull(pnrDb)) {
            throw new UnifiedResultException(Constant.NO_PNR);
        }
        // 将数据库的属性重新装载到内存对象中
        BeanUtils.copyProperties(pnrDb, memoryDataPnr.getMnjxPnr());

        List<MnjxPnrAt> mnjxPnrAts = iMnjxPnrAtService.lambdaQuery().eq(MnjxPnrAt::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrAt::getPnrAtId).list();
        List<MnjxPnrCt> mnjxPnrCts = iMnjxPnrCtService.lambdaQuery().eq(MnjxPnrCt::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrCt::getPnrIndex).list();
        List<MnjxPnrFc> mnjxPnrFcs = iMnjxPnrFcService.lambdaQuery().eq(MnjxPnrFc::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrFc::getPnrIndex).list();
        List<MnjxPnrFn> mnjxPnrFns = iMnjxPnrFnService.lambdaQuery().eq(MnjxPnrFn::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrFn::getPnrIndex).list();
        List<MnjxPnrFp> mnjxPnrFps = iMnjxPnrFpService.lambdaQuery().eq(MnjxPnrFp::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrFp::getPnrIndex).list();
        List<MnjxPnrGn> mnjxPnrGns = iMnjxPnrGnService.lambdaQuery().eq(MnjxPnrGn::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrGn::getPnrIndex).list();
        List<MnjxPnrNm> mnjxPnrNms = iMnjxPnrNmService.lambdaQuery().eq(MnjxPnrNm::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrNm::getPnrIndex).list();
        List<MnjxPnrOsi> mnjxPnrOsis = iMnjxPnrOsiService.lambdaQuery().eq(MnjxPnrOsi::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrOsi::getPnrIndex).list();
        List<MnjxPnrRecord> mnjxPnrRecords = iMnjxPnrRecordService.lambdaQuery().eq(MnjxPnrRecord::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrRecord::getPnrIndex).list();
        List<MnjxPnrRmk> mnjxPnrRmks = iMnjxPnrRmkService.lambdaQuery().eq(MnjxPnrRmk::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrRmk::getPnrIndex).list();
        List<MnjxPnrSeg> mnjxPnrSegs = iMnjxPnrSegService.lambdaQuery().eq(MnjxPnrSeg::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrSeg::getPnrIndex).list();
        List<MnjxPnrTk> mnjxPnrTks = iMnjxPnrTkService.lambdaQuery().eq(MnjxPnrTk::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrTk::getPnrIndex).list();
        List<MnjxPnrTc> mnjxPnrTcs = iMnjxPnrTcService.lambdaQuery().eq(MnjxPnrTc::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrTc::getPnrIndex).list();
        //EI选项
        List<MnjxPnrEi> pnrEis = iMnjxPnrEiService.lambdaQuery().eq(MnjxPnrEi::getPnrId, pnrDb.getPnrId()).orderByAsc(MnjxPnrEi::getPnrIndex).list();
        if (CollUtil.isNotEmpty(mnjxPnrAts)) {
            memoryDataPnr.getMnjxPnrAts().addAll(mnjxPnrAts);
        }
        if (CollUtil.isNotEmpty(mnjxPnrRecords)) {
            memoryDataPnr.getMnjxPnrRecords().addAll(mnjxPnrRecords);
        }
        List<PnrSegDto> segDtos = mnjxPnrSegs.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_SEG + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_SEG + Constant.COLON + it.getInputValue());
            }
            PnrSegDto dto = new PnrSegDto();
            dto.setMnjxPnrSeg(it);
            // 设置共享航班
            dto.setCarrierFlight(it.getCarrierFlight());
            return dto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(segDtos)) {
            memoryDataPnr.getPnrSegDtos().addAll(segDtos);
        }
        List<PnrTkDto> tkDtos = mnjxPnrTks.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_TK + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_TK + Constant.COLON + it.getInputValue());
            }
            PnrTkDto tkDto = new PnrTkDto();
            tkDto.setMnjxPnrTk(it);
            return tkDto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tkDtos)) {
            memoryDataPnr.getPnrTkDtos().addAll(tkDtos);
        }
        List<PnrCtDto> ctDtos = mnjxPnrCts.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_CT + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_CT + Constant.COLON + it.getInputValue());
            }
            PnrCtDto dto = new PnrCtDto();
            dto.setMnjxPnrCt(it);
            return dto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ctDtos)) {
            memoryDataPnr.getPnrCtDtos().addAll(ctDtos);
        }
        List<PnrOsiDto> osiDtos = mnjxPnrOsis.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_OSI + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_OSI + Constant.COLON + it.getInputValue());
            }
            PnrOsiDto osiDto = new PnrOsiDto();
            osiDto.setMnjxPnrOsi(it);
            return osiDto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(osiDtos)) {
            memoryDataPnr.getPnrOsiDtos().addAll(osiDtos);
        }
        List<PnrFcDto> fcDtos = mnjxPnrFcs.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_FC + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_FC + Constant.COLON + it.getInputValue());
            }
            PnrFcDto dto = new PnrFcDto();
            dto.setMnjxPnrFc(it);
            return dto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(fcDtos)) {
            memoryDataPnr.getPnrFcDtos().addAll(fcDtos);
        }
        List<PnrFnDto> fnDtos = mnjxPnrFns.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_FN + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_FN + Constant.COLON + it.getInputValue());
            }
            PnrFnDto dto = new PnrFnDto();
            dto.setMnjxPnrFn(it);
            return dto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(fnDtos)) {
            memoryDataPnr.getPnrFnDtos().addAll(fnDtos);
        }
        List<PnrFpDto> fpDtos = mnjxPnrFps.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_FP + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_FP + Constant.COLON + it.getInputValue());
            }
            PnrFpDto dto = new PnrFpDto();
            dto.setMnjxPnrFp(it);
            return dto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(fpDtos)) {
            memoryDataPnr.getPnrFpDtos().addAll(fpDtos);
        }
        List<PnrGnDto> gnDtos = mnjxPnrGns.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_GN + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_GN + Constant.COLON + it.getInputValue());
            }
            PnrGnDto dto = new PnrGnDto();
            dto.setMnjxPnrGn(it);
            return dto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(gnDtos)) {
            memoryDataPnr.getPnrGnDtos().addAll(gnDtos);
        }
        List<PnrNmDto> nmDtos = mnjxPnrNms.stream().map(mnjxPnrNm -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.NM + Constant.COLON + mnjxPnrNm.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.NM + Constant.COLON + mnjxPnrNm.getInputValue());
            }
            PnrNmDto dto = new PnrNmDto();
            dto.setMnjxPnrNm(mnjxPnrNm);
            //设置ssr
            List<MnjxNmSsr> pnrSsrs = iMnjxNmSsrService.lambdaQuery().eq(MnjxNmSsr::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmSsr::getPnrIndex).list();
            List<PnrSsrDto> pnrSsrDto = pnrSsrs.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_SSR + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_SSR + Constant.COLON + itt.getInputValue());
                }
                PnrSsrDto ssrDto = new PnrSsrDto();
                ssrDto.setMnjxNmSsr(itt);
                return ssrDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrSsrDto)) {
                dto.getPnrSsrDtos().addAll(pnrSsrDto);
            }
            //设置旅客CT项
            List<MnjxNmCt> mnjxNmCts = iMnjxNmCtService.lambdaQuery().eq(MnjxNmCt::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmCt::getPnrIndex).list();
            List<PnrNmCtDto> pnrNmCtDtos = mnjxNmCts.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.NM_CT + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.NM_CT + Constant.COLON + itt.getInputValue());
                }
                PnrNmCtDto nmCtDto = new PnrNmCtDto();
                nmCtDto.setMnjxNmCt(itt);
                return nmCtDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmCtDtos)) {
                dto.getPnrNmCtDtos().addAll(pnrNmCtDtos);
            }
            //设置旅客OSI项
            List<MnjxNmOsi> mnjxNmOsis = iMnjxNmOsiService.lambdaQuery().eq(MnjxNmOsi::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmOsi::getPnrIndex).list();
            List<PnrNmOsiDto> nmOsiDtos = mnjxNmOsis.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.NM_OSI + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.NM_OSI + Constant.COLON + itt.getInputValue());
                }
                PnrNmOsiDto nmOsiDto = new PnrNmOsiDto();
                nmOsiDto.setMnjxNmOsi(itt);
                return nmOsiDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmOsiDtos)) {
                dto.getPnrNmOsiDtos().addAll(nmOsiDtos);
            }
            //设置旅客RMK项
            List<MnjxNmRmk> nmRmks = iMnjxNmRmkService.lambdaQuery().eq(MnjxNmRmk::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmRmk::getPnrIndex).list();
            List<PnrNmRmkDto> nmRmkDtos = nmRmks.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.NM_RMK + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.NM_RMK + Constant.COLON + itt.getInputValue());
                }
                PnrNmRmkDto nmRmkDto = new PnrNmRmkDto();
                nmRmkDto.setMnjxNmRmk(itt);
                return nmRmkDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmRmkDtos)) {
                dto.getPnrNmRmkDtos().addAll(nmRmkDtos);
            }
            //旅客无陪儿童信息
            MnjxPnrNmUm nmUm = iMnjxPnrNmUmService.lambdaQuery().eq(MnjxPnrNmUm::getPnrNmId, mnjxPnrNm.getPnrNmId()).one();
            if (ObjectUtil.isNotNull(nmUm)) {
                dto.setMnjxPnrNmUm(nmUm);
            }
            //旅客携带婴儿信息
            MnjxNmXn pnrXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, mnjxPnrNm.getPnrNmId())
                    .orderByAsc(MnjxNmXn::getPnrIndex)
                    .one();
            if (ObjectUtil.isNotEmpty(pnrXn)) {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_XN + Constant.COLON + pnrXn.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_XN + Constant.COLON + pnrXn.getInputValue());
                }
                PnrXnDto xnDto = new PnrXnDto();
                xnDto.setMnjxNmXn(pnrXn);
                dto.getPnrXnDtos().add(xnDto);
            }

            //XN TN
            if (ObjectUtil.isNotEmpty(pnrXn)) {
                List<MnjxPnrNmTn> pnrXnTns = iMnjxPnrNmTnService.lambdaQuery().eq(ObjectUtil.isNotEmpty(pnrXn), MnjxPnrNmTn::getNmXnId, pnrXn.getNmXnId()).list();
                List<PnrNmTnDto> xnTnDtos = pnrXnTns.stream().map(itt -> {
                    if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_TN + Constant.COLON + itt.getInputValue())) {
                        memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_TN + Constant.COLON + itt.getInputValue());
                    }
                    PnrNmTnDto nmTnDto = new PnrNmTnDto();
                    nmTnDto.setMnjxPnrNmTn(itt);
                    return nmTnDto;
                }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(xnTnDtos)) {
                    memoryDataPnr.getPnrNmTnDtos().addAll(xnTnDtos);
                }
            }

            //旅客出票记录
            List<MnjxPnrNmTn> pnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, mnjxPnrNm.getPnrNmId())
                    .or(ObjectUtil.isNotEmpty(pnrXn), i -> i.eq(MnjxPnrNmTn::getNmXnId, pnrXn.getNmXnId()))
                    .orderByAsc(MnjxPnrNmTn::getPnrIndex)
                    .list();
            if (CollUtil.isNotEmpty(pnrNmTnList)) {
                List<String> tnIdList = pnrNmTnList.stream()
                        .map(MnjxPnrNmTn::getTnId)
                        .collect(Collectors.toList());
                List<MnjxPnrNmTicket> nmTickets = iMnjxPnrNmTicketService.lambdaQuery()
                        .in(MnjxPnrNmTicket::getPnrNmTnId, tnIdList)
                        .list();
                if (CollUtil.isNotEmpty(nmTickets)) {
                    dto.getMnjxPnrNmTickets().addAll(nmTickets);
                }
            }
            //旅客改签、换开信息
            List<MnjxNmOi> pnrOis = iMnjxNmOiService.lambdaQuery().eq(MnjxNmOi::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmOi::getPnrIndex).list();
            List<PnrOiDto> oiDtos = pnrOis.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_OI + Constant.COLON + itt.getOiInfo())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_OI + Constant.COLON + itt.getOiInfo());
                }
                PnrOiDto pnrOiDto = new PnrOiDto();
                pnrOiDto.setMnjxNmOi(itt);
                return pnrOiDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(oiDtos)) {
                dto.getPnrOiDtos().addAll(oiDtos);
            }
            //EI项 签注信息组
            List<MnjxNmEi> nmEis = iMnjxNmEiService.lambdaQuery().eq(MnjxNmEi::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmEi::getPnrIndex).list();
            List<PnrNmEiDto> nmEiDtos = nmEis.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.NM_EI + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.NM_EI + Constant.COLON + itt.getInputValue());
                }
                PnrNmEiDto nmEiDto = new PnrNmEiDto();
                nmEiDto.setMnjxNmEi(itt);
                return nmEiDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmEiDtos)) {
                dto.getPnrNmEiDtos().addAll(nmEiDtos);
            }
            //FP
            List<MnjxNmFp> nmFps = iMnjxNmFpService.lambdaQuery().eq(MnjxNmFp::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmFp::getPnrIndex).list();
            List<PnrNmFpDto> nmFpDtos = nmFps.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.NM_FP + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.NM_FP + Constant.COLON + itt.getInputValue());
                }
                PnrNmFpDto nmFpDto = new PnrNmFpDto();
                nmFpDto.setMnjxNmFp(itt);
                return nmFpDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmFpDtos)) {
                dto.getPnrNmFpDtos().addAll(nmFpDtos);
            }
            //FC
            List<MnjxNmFc> mnjxNmFcs = iMnjxNmFcService.lambdaQuery().eq(MnjxNmFc::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmFc::getPnrIndex).list();
            List<PnrNmFcDto> pnrNmFcDtos = mnjxNmFcs.stream().map(mnjxNmFc -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.RT_NM_FC + Constant.COLON + mnjxNmFc.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.RT_NM_FC + Constant.COLON + mnjxNmFc.getInputValue());
                }
                PnrNmFcDto pnrNmFcDto = new PnrNmFcDto();
                pnrNmFcDto.setMnjxNmFc(mnjxNmFc);
                return pnrNmFcDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmFcDtos)) {
                dto.getPnrNmFcDtos().addAll(pnrNmFcDtos);
            }
            //FN
            List<MnjxNmFn> nmFns = iMnjxNmFnService.lambdaQuery().eq(MnjxNmFn::getPnrNmId, mnjxPnrNm.getPnrNmId()).orderByAsc(MnjxNmFn::getPnrIndex).list();
            List<PnrNmFnDto> nmFnDtos = nmFns.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.NM_FN + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.NM_FN + Constant.COLON + itt.getInputValue());
                }
                PnrNmFnDto nmFnDto = new PnrNmFnDto();
                nmFnDto.setMnjxNmFn(itt);
                return nmFnDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmFnDtos)) {
                dto.getPnrNmFnDtos().addAll(nmFnDtos);
            }
            //TN
            List<MnjxPnrNmTn> pnrNmTns = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getPnrNmId, mnjxPnrNm.getPnrNmId()).list();
            List<PnrNmTnDto> nmTnDtos = pnrNmTns.stream().map(itt -> {
                if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_TN + Constant.COLON + itt.getInputValue())) {
                    memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_TN + Constant.COLON + itt.getInputValue());
                }
                PnrNmTnDto nmTnDto = new PnrNmTnDto();
                nmTnDto.setMnjxPnrNmTn(itt);
                return nmTnDto;
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmTnDtos)) {
                memoryDataPnr.getPnrNmTnDtos().addAll(nmTnDtos);
            }
            return dto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(nmDtos)) {
            memoryDataPnr.getPnrNmDtos().addAll(nmDtos);
        }
        List<PnrTcDto> tcDtos = mnjxPnrTcs.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_TC + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_TC + Constant.COLON + it.getInputValue());
            }
            PnrTcDto tcDto = new PnrTcDto();
            tcDto.setMnjxPnrTc(it);
            return tcDto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tcDtos)) {
            memoryDataPnr.getPnrTcDtos().addAll(tcDtos);
        }
        List<PnrRmkDto> rmkDtos = mnjxPnrRmks.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_RMK + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_RMK + Constant.COLON + it.getInputValue());
            }
            PnrRmkDto rmkDto = new PnrRmkDto();
            rmkDto.setMnjxPnrRmk(it);
            return rmkDto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(rmkDtos)) {
            memoryDataPnr.getPnrRmkDtos().addAll(rmkDtos);
        }
        List<PnrEiDto> eiDtos = pnrEis.stream().map(it -> {
            if (!memoryDataPnr.getExistHistoryPnrItem().contains(Constant.PNR_EI + Constant.COLON + it.getInputValue())) {
                memoryDataPnr.getExistHistoryPnrItem().add(Constant.PNR_EI + Constant.COLON + it.getInputValue());
            }
            PnrEiDto eiDto = new PnrEiDto();
            eiDto.setMnjxPnrEi(it);
            return eiDto;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(eiDtos)) {
            memoryDataPnr.getPnrEiDtos().addAll(eiDtos);
        }
    }
}

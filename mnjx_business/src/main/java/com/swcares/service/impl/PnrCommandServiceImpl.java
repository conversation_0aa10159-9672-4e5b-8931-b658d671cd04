package com.swcares.service.impl;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxSi;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.AvVo;
import com.swcares.obj.vo.EtdzVo;
import com.swcares.service.IPnrCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * pnr操作的相关实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class PnrCommandServiceImpl implements IPnrCommandService {
    @Resource
    private PnrCommandServicePartNm pnrCommandServicePartNm;
    @Resource
    private PnrCommandServicePartAv pnrCommandServicePartAv;
    @Resource
    private PnrCommandServicePartBp pnrCommandServicePartBp;
    @Resource
    private PnrCommandServicePartXe pnrCommandServicePartXe;
    @Resource
    private PnrCommandServicePartSd pnrCommandServicePartSd;
    @Resource
    private PnrCommandServicePartSsr pnrCommandServicePartSsr;
    @Resource
    private PnrCommandServicePartPat pnrCommandServicePartPat;
    @Resource
    private PnrCommandServicePartAt pnrCommandServicePartAt;
    @Resource
    private PnrCommandServicePartEtdz pnrCommandServicePartEtdz;
    @Resource
    private PnrCommandServicePartCt pnrCommandServicePartCt;
    @Resource
    private PnrCommandServicePartSfc pnrCommandServicePartSfc;
    @Resource
    private PnrCommandServicePartRt pnrCommandServicePartRt;
    @Resource
    private PnrCommandServicePartOsi pnrCommandServicePartOsi;
    @Resource
    private PnrCommandServicePartTk pnrCommandServicePartTk;
    @Resource
    private PnrCommandServicePartXn pnrCommandServicePartXn;
    @Resource
    private PnrCommandServicePartGn pnrCommandServicePartGn;
    @Resource
    private PnrCommandServicePartRmk pnrCommandServicePartRmk;
    @Resource
    private PnrCommandServicePartSs pnrCommandServicePartSs;

    @Override
    public EtdzVo etdz(MemoryDataPnr memoryDataPnr, EtdzDto etdzDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        return pnrCommandServicePartEtdz.etdz(memoryDataPnr, etdzDto, mnjxOffice, mnjxSi);
    }

    @Override
    public void xe(MemoryDataPnr memoryDataPnr, XeDto xeDto) throws UnifiedResultException {
        pnrCommandServicePartXe.xe(memoryDataPnr, xeDto);
    }

    @Override
    public String at(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        return pnrCommandServicePartAt.at(memoryDataPnr, mnjxOffice, mnjxSi);
    }

    @Override
    public void rt(MemoryDataPnr memoryDataPnr, RtDto rtDto) throws UnifiedResultException {
        pnrCommandServicePartRt.rt(memoryDataPnr, rtDto);
    }


    @Override
    public void nm(MemoryDataPnr memoryDataPnr, List<NmDto> nmDtos, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartNm.nm(memoryDataPnr, nmDtos, mnjxOffice, mnjxSi);
    }


    @Override
    public void sd(MemoryDataPnr memoryDataPnr, SdDto sdDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartSd.sd(memoryDataPnr, sdDto, mnjxOffice, mnjxSi);
    }


    @Override
    public void tk(MemoryDataPnr memoryDataPnr, TkDto tkDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartTk.tk(memoryDataPnr, tkDto, mnjxOffice, mnjxSi);
    }

    @Override
    public void ct(MemoryDataPnr memoryDataPnr, CtDto ctDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartCt.ct(memoryDataPnr, ctDto, mnjxOffice, mnjxSi);
    }


    @Override
    public void ssr(MemoryDataPnr memoryDataPnr, SsrDto ssrDto) throws UnifiedResultException {
        pnrCommandServicePartSsr.ssr(memoryDataPnr, ssrDto);
    }

    @Override
    public void pat(MemoryDataPnr memoryDataPnr, PatDto patDto) throws UnifiedResultException {
        pnrCommandServicePartPat.pat(memoryDataPnr, patDto);
    }

    @Override
    public boolean bp(String flightId) throws UnifiedResultException {
        return pnrCommandServicePartBp.bp(flightId);
    }

    @Override
    public void osi(MemoryDataPnr memoryDataPnr, OsiDto osiDto) throws UnifiedResultException {
        pnrCommandServicePartOsi.osi(memoryDataPnr, osiDto);
    }

    @Override
    public void xn(MemoryDataPnr memoryDataPnr, XnDto xnDto) {
        pnrCommandServicePartXn.xn(memoryDataPnr, xnDto);
    }

    @Override
    public void gn(MemoryDataPnr memoryDataPnr, GnDto gnDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartGn.gn(memoryDataPnr, gnDto, mnjxOffice, mnjxSi);
    }

    @Override
    public void rmk(MemoryDataPnr memoryDataPnr, RmkDto rmkDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartRmk.rmk(memoryDataPnr, rmkDto, mnjxOffice, mnjxSi);
    }

    @Override
    public void ss(MemoryDataPnr memoryDataPnr, SsDto ssDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        pnrCommandServicePartSs.ss(memoryDataPnr, ssDto, mnjxOffice, mnjxSi);
    }

    @Override
    public void sfc(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        pnrCommandServicePartSfc.sfc(memoryDataPnr);
    }

    @Cacheable(key = "'avResult' + #memoryDataPnr.memoryDataPnrId")
    @Override
    public List<AvVo> getAvResultCache(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        throw new UnifiedResultException(Constant.NO_DISPLAY_SYMBOL);
    }

    @Override
    public List<AvVo> av(MemoryDataPnr memoryDataPnr, AvDto avDto) throws UnifiedResultException {
        return pnrCommandServicePartAv.av(memoryDataPnr, avDto);
    }

    @Override
    public void avCurrentPageCacheDate(MemoryDataPnr memoryDataPnr, List<Integer> avVos) {
        pnrCommandServicePartAv.avCurrentPageCacheDate(memoryDataPnr, avVos);
    }

    @Cacheable(key = "'avCurrentPageCache' + #memoryDataPnr.memoryDataPnrId")
    @Override
    public List<Integer> getAvCurrentPageCache(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        throw new UnifiedResultException(Constant.NO_DISPLAY_SYMBOL);
    }
}

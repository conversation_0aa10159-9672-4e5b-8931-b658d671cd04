package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.NumberUtils;
import com.swcares.entity.*;
import com.swcares.mapper.PnrCommandPartPatMapper;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.PatVoA;
import com.swcares.service.IMnjxAirportService;
import com.swcares.service.IMnjxCnYqService;
import com.swcares.service.IMnjxStandardPatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.eterm.crs.service.impl.PatServiceImpl <br>
 * description：PatServiceImpl <br>
 *
 * <AUTHOR> <br>
 * date 2022/01/13 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class PnrCommandServicePartPat {

    @Resource
    private PnrCommandPartPatMapper pnrCommandPartPatMapper;

    @Resource
    private IMnjxCnYqService iMnjxCnYqService;

    @Resource
    private IMnjxStandardPatService iMnjxStandardPatService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    /**
     * 儿童
     */
    private final String CH = "CH";

    /**
     * 婴儿
     */
    private final String IN = "IN";

    /**
     * GM
     */
    private final String GM = "GM";

    /**
     * JC
     */
    private final String JC = "JC";

    /**
     * GMJC
     */
    private final String GMJC = "GMJC";

    /**
     * 运价类型
     */
    private final List<String> patTypeList = Arrays.asList("CH", "IN", "GM", "JC");

    private final static String ONE_STR = "1.00";


    public void pat(MemoryDataPnr memoryDataPnr, PatDto pat) throws UnifiedResultException {
        // 去掉被X、SA的无效航班
        List<PnrSegDto> segList = memoryDataPnr.getPnrSegDtos().stream()
                .filter(k -> !k.isXe())
                .filter(k -> !"SA".equals(k.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.toList());
        log.debug("Pat生效的航段信息：[{}]", segList);
        if (CollUtil.isEmpty(segList)) {
            throw new UnifiedResultException(Constant.NO_SEGMENT);
        }

        // 遍历航段查询航段的开舱运价
        List<MnjxPnrSeg> pnrSegs = memoryDataPnr.getPnrSegDtos().stream()
                .filter(k -> !k.isXe())
                .map(PnrSegDto::getMnjxPnrSeg)
                .collect(Collectors.toList());

        // 如果运价模式为手动
        if (StrUtil.isEmpty(pat.getPat())) {
            handlePat(memoryDataPnr, pat, pnrSegs, segList, patTypeList);
        } else {
            handlePatA(memoryDataPnr, pat, pnrSegs, segList, patTypeList);
        }
    }

    /**
     * handlePatA
     *
     * @param memoryDataPnr
     * @param patDto        解析数据
     * @param pnrSegs       所有航段
     * @param segList       有效航段
     * @param patTypeList
     * @throws UnifiedResultException
     */
    private void handlePatA(MemoryDataPnr memoryDataPnr, PatDto patDto, List<MnjxPnrSeg> pnrSegs, List<PnrSegDto> segList, List<String> patTypeList) throws UnifiedResultException {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        log.debug("----当前的Pnr:[{}]", mnjxPnr);
        List<PnrNmDto> nmDtos = memoryDataPnr.getPnrNmDtos().stream()
                .filter(k -> !k.isXe())
                .filter(k -> !Constant.BE_UPDATED.equals(k.getUpdateMark()))
                .collect(Collectors.toList());
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        //如果团队旅客没有添加旅客姓名时，允许进行下一步的运价操作
        if (CollUtil.isEmpty(nmDtos) && CollUtil.isEmpty(pnrGnDtos)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }

        //如果是团队旅客时，团队旅客中输入了旅客姓名在添加姓名时，就必须指定旅客序号
        if (CollUtil.isNotEmpty(pnrGnDtos) && CollUtil.isNotEmpty(nmDtos) && StrUtil.isEmpty(patDto.getPNo())) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        if (StrUtil.isNotEmpty(patDto.getPNo())) {
            int psgId = Integer.parseInt(patDto.getPNo());
            nmDtos = nmDtos.stream()
                    .filter(k -> k.getPnrIndex() == psgId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(nmDtos)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
        }
        // 儿童
        if (CH.equals(patDto.getPatType())) {
            if (StrUtil.isNotEmpty(patDto.getPNo())) {
                PnrNmDto pnrNmDto = nmDtos.get(0);
                // 获取儿童或无陪儿童
                List<PnrSsrDto> chldOrUmnr = pnrNmDto.getPnrSsrDtos().stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> "CHLD".equals(k.getMnjxNmSsr().getSsrType()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(chldOrUmnr)) {
                    throw new UnifiedResultException("CAN NOT USE *CH FOR NON CHD PASSENGER");
                }
            } else {
                List<PnrSsrDto> chldOrUmnr = new ArrayList<>();
                nmDtos.forEach(nmDto -> {
                    List<PnrSsrDto> ssrDtos = nmDto.getPnrSsrDtos().stream()
                            .filter(k -> !k.isXe())
                            .filter(k -> "CHLD".equals(k.getMnjxNmSsr().getSsrType()))
                            .collect(Collectors.toList());
                    chldOrUmnr.addAll(ssrDtos);
                });
                if (chldOrUmnr.size() != nmDtos.size()) {
                    throw new UnifiedResultException("CAN NOT USE *CH FOR NON CHD PASSENGER");
                }
            }
        }
        // 代表伤残军人、因公带伤警察
        else if (GM.equals(patDto.getPatType()) || JC.equals(patDto.getPatType())) {
            nmDtos = nmDtos.stream()
                    .filter(k -> "0".equals(k.getMnjxPnrNm().getPsgType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(nmDtos) && StrUtil.isEmpty(patDto.getPNo())) {
                throw new UnifiedResultException("CAN NOT USE *GM OR *JC FOR NON RMK GMJC");
            }
            if (StrUtil.isEmpty(patDto.getPNo()) && nmDtos.size() == 1) {
                // PNR
                List<PnrRmkDto> gmjc = memoryDataPnr.getPnrRmkDtos().stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> GMJC.equals(k.getMnjxPnrRmk().getRmkName()))
                        .collect(Collectors.toList());
                // NM
                List<PnrNmRmkDto> pnrNmRmkDtos = nmDtos.get(0).getPnrNmRmkDtos().stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> GMJC.equals(k.getMnjxNmRmk().getRmkName()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(gmjc) && CollUtil.isEmpty(pnrNmRmkDtos)) {
                    throw new UnifiedResultException("CAN NOT USE *GM OR *JC FOR NON RMK GMJC");
                }
            } else {
                if (StrUtil.isEmpty(patDto.getPNo())) {
                    throw new UnifiedResultException("CAN NOT USE *GM OR *JC FOR NON RMK GMJC");
                }
                // 验证姓名组中是否包含了GMJC
                PnrNmDto pnrNmDto = nmDtos.get(0);
                List<PnrNmRmkDto> pnrNmRmkDtos = pnrNmDto.getPnrNmRmkDtos().stream()
                        .filter(pnrNmRmkDto -> !pnrNmRmkDto.isXe())
                        .filter(pnrNmRmkDto -> GMJC.equals(pnrNmRmkDto.getMnjxNmRmk().getRmkName()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(pnrNmRmkDtos)) {
                    throw new UnifiedResultException("CAN NOT USE *GM OR *JC FOR NON RMK GMJC");
                }
            }
        }
        log.debug("----执行PAT:A自动运价:[{}][{}]", mnjxPnr.getPnrCrs(), mnjxPnr.getPnrIcs());
        // 机场
        Map<String, MnjxAirport> airportMap = retrieveAirports();
        // 运价
        Map<String, MnjxStandardPat> standardPatMap = retrieveStandardPat();
        // 获取基建燃油
        MnjxCnYq mnjxCnYq = retrieveCnYq();

        StringBuilder echo = new StringBuilder();
        echo.append(">PAT:A");
        if (patTypeList.contains(patDto.getPatType())) {
            echo.append("*").append(patDto.getPatType());
        }
        if (StrUtil.isNotEmpty(patDto.getPNo())) {
            echo.append("/P").append(patDto.getPNo());
        }
        echo.append(StrUtil.CRLF);
        // 舱位
        StringBuilder sellCabin = new StringBuilder();
        // 票面价
        BigDecimal ticketPrice = new BigDecimal("0.00");
        // 机建费
        BigDecimal tax = new BigDecimal(mnjxCnYq.getCn()).multiply(new BigDecimal(segList.size())).setScale(2, RoundingMode.HALF_UP);
        // 燃油费
        BigDecimal yq = new BigDecimal("0.00");
        // 航段信息
        List<String> fcSegList = new ArrayList<>();
        StringBuilder sfn = new StringBuilder();
        int segId = 1;
        // 折扣
        int discount = 0;
        // 是否是ARNK
        boolean isArnk = false;
        String cityId = "";
        for (MnjxPnrSeg pnrSeg : pnrSegs) {
            if (!"SA".equals(pnrSeg.getPnrSegType())) {
                // 验证多个航段不连续的航段，如果是ARNK跳过连续性判断
                if (!isArnk) {
                    String tmpCityId = "";
                    if (airportMap.containsKey(pnrSeg.getOrg())) {
                        tmpCityId = airportMap.get(pnrSeg.getOrg()).getCityId();
                    }
                    if (StrUtil.isNotEmpty(cityId) && !cityId.equals(tmpCityId)) {
                        throw new UnifiedResultException(Constant.CONTINUITY);
                    }
                } else {
                    isArnk = false;
                }
                if (airportMap.containsKey(pnrSeg.getDst())) {
                    cityId = airportMap.get(pnrSeg.getDst()).getCityId();
                }
                // 获取开舱数据、处理共享航班数据
                MnjxOpenCabin openCabin = retrieveOpenCabin(pnrSeg);
                BigDecimal sellCabinPrice = new BigDecimal(openCabin.getSellCabinPrice());

                // 处理需要打折的
                if (patTypeList.contains(patDto.getPatType())) {
                    // Y价格
                    BigDecimal yPrice = getPriceY(pnrSeg);
                    // 如果当前航段预订座位的舱位折扣是高于Y价的，则订的什么舱位，就以该舱位的价格为基础进行打折
                    if (sellCabinPrice.doubleValue() < yPrice.doubleValue()) {
                        sellCabinPrice = yPrice;
                    }
                }

                if (CH.equals(patDto.getPatType())) {
                    discount = BigDecimal.valueOf(mnjxCnYq.getChildDiscount()).multiply(new BigDecimal(100)).intValue();
                    sellCabinPrice = sellCabinPrice.multiply(BigDecimal.valueOf(mnjxCnYq.getChildDiscount()));
                } else if (IN.equals(patDto.getPatType())) {
                    discount = BigDecimal.valueOf(mnjxCnYq.getInfiDiscount()).multiply(new BigDecimal(100)).intValue();
                    sellCabinPrice = sellCabinPrice.multiply(BigDecimal.valueOf(mnjxCnYq.getInfiDiscount()));
                } else if (GM.equals(patDto.getPatType()) || JC.equals(patDto.getPatType())) {
                    discount = BigDecimal.valueOf(mnjxCnYq.getPoliceDiscount()).multiply(new BigDecimal(100)).intValue();
                    sellCabinPrice = sellCabinPrice.multiply(BigDecimal.valueOf(mnjxCnYq.getPoliceDiscount()));
                }

                if (StrUtil.isEmpty(sellCabin)) {
                    sellCabin.append(pnrSeg.getSellCabin());
                } else {
                    sellCabin.append("+").append(pnrSeg.getSellCabin());
                }
                if (StrUtil.isNotEmpty(patDto.getPatType())) {
                    sellCabin.append(patDto.getPatType());
                }
                // 只有成人单段的时候才显示共享航班信息（婴儿，儿童，军警残、联程保持原样。）
                if (segList.size() == 1 && !patTypeList.contains(patDto.getPatType())) {
                    // 添加承运航班、销售航班
                    if (StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
                        sellCabin.append(StrUtil.format("{}{}", pnrSeg.getCarrierFlight().substring(0, 2), pnrSeg.getFlightNo().substring(0, 2)));
                    }
                }
                sfn.append(String.format("    \u0010SFN:01/%02d", segId));
                // 获取运价
                MnjxStandardPat standardPat = getStandardPat(airportMap, standardPatMap, pnrSeg.getOrg(), pnrSeg.getDst());
                if (ObjectUtil.isEmpty(standardPat)) {
                    throw new UnifiedResultException(StrUtil.format(">PAT:E{}没有符合条件的运价, 请使用手工处理", StrUtil.CRLF));
                }
                if (standardPat.getDistance() > mnjxCnYq.getThreshold()) {
                    yq = yq.add(new BigDecimal(mnjxCnYq.getYqHigh()));
                } else {
                    yq = yq.add(new BigDecimal(mnjxCnYq.getYqLow()));
                }
                segId++;
                String airline = pnrSeg.getFlightNo().substring(0, 2);
                ticketPrice = ticketPrice.add(sellCabinPrice).setScale(2, RoundingMode.HALF_UP);
                // CTU CA PEK 1100.00Y
                sellCabinPrice = sellCabinPrice.multiply(new BigDecimal(1)).setScale(2, RoundingMode.HALF_UP);
                // 处理小时个位不为0
                sellCabinPrice = NumberUtils.dealTicketPrice(sellCabinPrice);
                String fcSeg = StrUtil.format("{} {} {} {}{}", pnrSeg.getOrg(), airline, pnrSeg.getDst(),
                        sellCabinPrice.toString(), pnrSeg.getSellCabin());
                // 如果是共享航班取承运航班信息

                // 只有成人单段的时候才显示共享航班信息（婴儿，儿童，军警残、联程保持原样。）
                if (segList.size() == 1 && !patTypeList.contains(patDto.getPatType())) {
                    // 添加承运航班、销售航班
                    if (StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
                        // 承运+销售航司
                        String carrierOrSalesAirline = StrUtil.format("{}{}", pnrSeg.getCarrierFlight().substring(0, 2), pnrSeg.getFlightNo().substring(0, 2));
                        fcSeg = StrUtil.format("{} {} {} {}{}{}", pnrSeg.getOrg(), airline, pnrSeg.getDst(),
                                sellCabinPrice.toString(), pnrSeg.getSellCabin(), carrierOrSalesAirline);
                    }
                }
                fcSegList.add(fcSeg);
            } else {
                fcSegList.add("//");
                isArnk = true;
            }
        }
        if (patTypeList.contains(patDto.getPatType())) {
            sellCabin.append(100 - discount);
        }
        String taxStr = StrUtil.format("CNY{}", tax.toString());
        String yqStr = StrUtil.format("CNY{}", yq.toString());
        if (IN.equals(patDto.getPatType())) {
            tax = new BigDecimal("0.00");
            yq = new BigDecimal("0.00");
            taxStr = "TEXEMPTCN";
            yqStr = "TEXEMPTYQ";
        } else if (CH.equals(patDto.getPatType())) {
            tax = new BigDecimal("0.00");
            taxStr = "TEXEMPTCN";
            yq = yq.multiply(new BigDecimal(discount).divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
            yqStr = StrUtil.format("CNY{}", yq.toString());
        } else if (GM.equals(patDto.getPatType()) || JC.equals(patDto.getPatType())) {
            yq = yq.multiply(new BigDecimal(discount).divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
            yqStr = StrUtil.format("CNY{}", yq.toString());
        }
        ticketPrice = ticketPrice.setScale(2, RoundingMode.HALF_UP);
        ticketPrice = NumberUtils.dealTicketPrice(ticketPrice);
        BigDecimal totalPrice = ticketPrice.add(tax).add(yq).setScale(2, RoundingMode.HALF_UP);
        totalPrice = NumberUtils.dealTicketPrice(totalPrice);
        echo.append(StrUtil.format("01 {} FARE:CNY{} TAX:{} YQ:{}  TOTAL:{}", sellCabin, ticketPrice.toString(), taxStr, yqStr, totalPrice.toString()));
        echo.append(StrUtil.CRLF);
        if (segList.size() == 1) {
            sfn.setLength(0);
            sfn.append("    \u0010SFN:01");
        }
        echo.append("\u0010SFC:01").append(sfn);
        echo.append(StrUtil.CRLF);
        echo.append("*01*BRAND:*NO-AUTH BUSINESS STANDARD");
        log.debug("pat:A,Pnr[{}],[{}]运价[{}]", mnjxPnr.getPnrCrs(), mnjxPnr.getPnrIcs(), echo.toString());
        MnjxPnrSeg pnrSeg = segList.get(0).getMnjxPnrSeg();
        PatVoA patA = new PatVoA();
        patA.setOrgDst(pnrSeg.getOrg() + pnrSeg.getDst());
        patA.setFlightDate(pnrSeg.getFlightDate());
        patA.setAirline(pnrSeg.getFlightNo().substring(0, 2));
        patA.setSfn(sfn.toString());
        patA.setPatType(patDto.getPatType());
        patA.setPId(patDto.getPNo());
        patA.setCn(tax);
        patA.setYq(yq);
        patA.setFPrice(ticketPrice);
        if (IN.equals(patDto.getPatType())) {
            patA.setDiscount(discount);
        }
        patA.setFc(fcSegList);
        memoryDataPnr.setPatA(patA);
        log.debug("执行的PatA对象的结果:[{}]", patA);
        patDto.setResult(echo.toString());
    }

    /**
     * handlePat
     *
     * @param memoryDataPnr
     * @param patDto
     * @param pnrSegs       有效航段
     * @param segList       有效航段
     * @param patTypeList
     * @throws UnifiedResultException
     */
    private void handlePat(MemoryDataPnr memoryDataPnr, PatDto patDto, List<MnjxPnrSeg> pnrSegs, List<PnrSegDto> segList, List<String> patTypeList) throws UnifiedResultException {
        // 配置回显信息
        StringBuilder echo = new StringBuilder();
        echo.append("\u0010").append("PAT:M");
        // 儿童
        if (CH.equals(patDto.getPatType())) {
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream().filter(k -> !k.isXe()).filter(k -> "0".equals(k.getMnjxPnrNm().getPsgType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmDtos)) {
                if (StrUtil.isEmpty(patDto.getPNo())) {
                    throw new UnifiedResultException("CAN NOT USE *CH FOR NON CHD PASSENGER");
                }
                Integer psgId = Integer.valueOf(patDto.getPNo());
                pnrNmDtos = pnrNmDtos.stream().filter(k -> k.getPnrIndex() == psgId).filter(k -> "1".equals(k.getMnjxPnrNm().getPsgType())).collect(Collectors.toList());
                if (CollUtil.isEmpty(pnrNmDtos)) {
                    throw new UnifiedResultException("CAN NOT USE *CH FOR NON CHD PASSENGER");
                }
                echo.append("/P").append(psgId);
                List<PnrSsrDto> ssrDtos = pnrNmDtos.get(0).getPnrSsrDtos().stream().filter(k -> !k.isXe()).filter(k -> "CHLD".equals(k.getMnjxNmSsr().getSsrType())).collect(Collectors.toList());
                if (CollUtil.isEmpty(ssrDtos)) {
                    throw new UnifiedResultException("CAN NOT USE *CH FOR NON CHD PASSENGER");
                }
            }
        }
        if (patTypeList.contains(patDto.getPatType())) {
            echo.append("*").append(patDto.getPatType());
        }
        echo.append(StrUtil.CRLF);
        // 机场
        Map<String, MnjxAirport> airportMap = retrieveAirports();
        // 运价
        Map<String, MnjxStandardPat> standardPatMap = retrieveStandardPat();
        // 获取基建燃油
        MnjxCnYq mnjxCnYq = retrieveCnYq();
        // 旅客折扣
//		MnjxPsgDiscount mnjxPsgDiscount = retrievePsgDiscount();

        // 计算fn
        BigDecimal fCny = new BigDecimal("0.00");
        // 代理费
        BigDecimal c = new BigDecimal("0.00");
        // 机建 乘以有效SS航段数
        BigDecimal cn = new BigDecimal(mnjxCnYq.getCn()).multiply(new BigDecimal(segList.size()));
        if (CH.equals(patDto.getPatType()) || IN.equals(patDto.getPatType())) {
            cn = new BigDecimal("0.00");
        }
        // 燃油
        BigDecimal yq = new BigDecimal("0.00");

        // fc
        StringBuilder fc = new StringBuilder();
        BigDecimal fcTotalPrice = new BigDecimal("0.00");
        MnjxStandardPat standardPat = null;
        // 上一次的航班是否是SA
        boolean lastIsSa = false;
        for (MnjxPnrSeg pnrSeg : pnrSegs) {
            // 获取运价
            standardPat = getStandardPat(airportMap, standardPatMap, pnrSeg.getOrg(), pnrSeg.getDst());
            if (ObjectUtil.isNotEmpty(standardPat) && standardPat.getDistance() > mnjxCnYq.getThreshold()) {
                yq = yq.add(new BigDecimal(mnjxCnYq.getYqHigh()));
            } else {
                yq = yq.add(new BigDecimal(mnjxCnYq.getYqLow()));
            }
            if (CH.equals(patDto.getPatType())) {
                yq = yq.multiply(BigDecimal.valueOf(mnjxCnYq.getChildDiscount()));
            } else if (GM.equals(patDto.getPatType()) || JC.equals(patDto.getPatType())) {
                yq = yq.multiply(BigDecimal.valueOf(mnjxCnYq.getPoliceDiscount()));
            }
            BigDecimal sellCabinPrice = new BigDecimal("0.00");
            // 有效航段
            if (!"SA".equals(pnrSeg.getPnrSegType())) {
                MnjxOpenCabin openCabin = retrieveOpenCabin(pnrSeg);
                MnjxPlaneModel planeModel = retrievePlaneModel(pnrSeg);

                String org = pnrSeg.getOrg();
                String flightNo = pnrSeg.getFlightNo();
                String flightDate = DateUtils.ymd2Com(pnrSeg.getFlightDate());
                String estimateOff = pnrSeg.getEstimateOff();
                String estimateArr = pnrSeg.getEstimateArr();
                String airline = pnrSeg.getFlightNo().substring(0, 2);
                String dst = pnrSeg.getDst();
                sellCabinPrice = new BigDecimal(openCabin.getSellCabinPrice());
                // 处理需要打折的
                if (patTypeList.contains(patDto.getPatType())) {
                    // Y价格
                    BigDecimal yPrice = getPriceY(pnrSeg);
                    // 如果当前航段预订座位的舱位折扣是高于Y价的，则订的什么舱位，就以该舱位的价格为基础进行打折
                    if (sellCabinPrice.doubleValue() < yPrice.doubleValue()) {
                        sellCabinPrice = yPrice;
                    }
                }
                sellCabinPrice = calSellCabinPrice(patDto.getPatType(), sellCabinPrice, mnjxCnYq);
                fCny = fCny.add(sellCabinPrice);
                String sellCabin = pnrSeg.getSellCabin();
                if (ObjectUtil.isEmpty(standardPat)) {
                    sellCabinPrice = new BigDecimal("0.00");
                }
                if (patTypeList.contains(patDto.getPatType())) {
                    int discount = 0;
                    if (CH.equals(patDto.getPatType())) {
                        discount = BigDecimal.valueOf(mnjxCnYq.getChildDiscount()).multiply(new BigDecimal(100)).intValue();
                    } else if (IN.equals(patDto.getPatType())) {
                        discount = BigDecimal.valueOf(mnjxCnYq.getInfiDiscount()).multiply(new BigDecimal(100)).intValue();
                    } else if (GM.equals(patDto.getPatType()) || JC.equals(patDto.getPatType())) {
                        discount = BigDecimal.valueOf(mnjxCnYq.getPoliceDiscount()).multiply(new BigDecimal(100)).intValue();
                    }
                    echo.append(StrUtil.format("{} {} {}  {}  {}{}           {}%     {} {} {} {} {}", org, flightNo, dst, sellCabinPrice.toString(), sellCabin, patDto.getPatType(), discount, sellCabinPrice.toString(), planeModel.getPlaneModelType(), flightDate, estimateOff, estimateArr)).append(StrUtil.CRLF);
                } else {
                    echo.append(StrUtil.format("{} {} {}  {}  {}           %     {} {} {} {} {}", org, flightNo, dst, sellCabinPrice.toString(), sellCabin, sellCabinPrice.toString(), planeModel.getPlaneModelType(), flightDate, estimateOff, estimateArr)).append(StrUtil.CRLF);
                }
                if (StrUtil.isEmpty(fc)) {
                    fc.append("FC ");
                    if (IN.equals(patDto.getPatType())) {
                        fc.append("/IN/");
                    }
                    if (ObjectUtil.isEmpty(standardPat)) {
                        fc.append(StrUtil.format("{} {} {} -.--{}", org, airline, dst, sellCabin));
                    } else {
                        if (patTypeList.contains(patDto.getPatType())) {
                            fc.append(StrUtil.format("{} {} {} {}{}{}", org, airline, dst, sellCabinPrice.toString(), sellCabin, patDto.getPatType()));
                        } else {
                            fc.append(StrUtil.format("{} {} {} {}{}", org, airline, dst, sellCabinPrice.toString(), sellCabin));
                        }
                    }
                } else {
                    if (ObjectUtil.isEmpty(standardPat)) {
                        fc.append(StrUtil.format(" {} {} {} -.--{}", org, airline, dst, sellCabin));
                    } else {
                        if (patTypeList.contains(patDto.getPatType())) {
                            if (lastIsSa) {
                                fc.append(StrUtil.format(" {} {} {} {}{}{}", org, airline, dst, sellCabinPrice.toString(), sellCabin, patDto.getPatType()));
                            } else {
                                fc.append(StrUtil.format(" {} {} {}{}{}", airline, dst, sellCabinPrice.toString(), sellCabin, patDto.getPatType()));
                            }
                        } else {
                            if (lastIsSa) {
                                fc.append(StrUtil.format(" {} {} {} {}{}", org, airline, dst, sellCabinPrice.toString(), sellCabin));
                            } else {
                                fc.append(StrUtil.format(" {} {} {}{}", airline, dst, sellCabinPrice.toString(), sellCabin));
                            }
                        }
                        lastIsSa = false;
                    }
                }
            } else {
                lastIsSa = true;
                if (StrUtil.isEmpty(fc)) {
                    fc.append("FC //");
                } else {
                    fc.append(" //");
                }
            }
            fcTotalPrice = fcTotalPrice.add(sellCabinPrice);
        }
        fcTotalPrice = fcTotalPrice.setScale(2, RoundingMode.HALF_UP);
        fcTotalPrice = NumberUtils.dealTicketPrice(fcTotalPrice);
        fc.append(StrUtil.CRLF);
        fc.append(StrUtil.format("-  CNY {} END", fcTotalPrice.toString()));

        cn = cn.setScale(2, RoundingMode.HALF_UP);
        yq = yq.setScale(2, RoundingMode.HALF_UP);
        // 保留小数点后两位
        fCny = fCny.setScale(2, RoundingMode.HALF_UP);
        // 实收票价
        BigDecimal sCny = fCny.setScale(2, RoundingMode.HALF_UP);
        // FN
        StringBuilder fn = new StringBuilder();
        // FP
        StringBuilder fp = new StringBuilder();
        fn.append("FN ");
        fp.append("FP ");
        if (IN.equals(patDto.getPatType())) {
            fp.append("/IN/");
            fn.append("/IN/");
        }
        fp.append("CASH, CNY");
        if (ObjectUtil.isEmpty(standardPat)) {
            fCny = new BigDecimal("0.00");
            sCny = new BigDecimal("0.00");
        }
        fn.append(StrUtil.format("FCNY {}/ SCNY{}/ C{}/", fCny.toString(), sCny.toString(), c.toString()));
        String cnStr = StrUtil.format("TCNY {}CN", cn.toString());
        String yqStr = StrUtil.format("TCNY {}YQ", yq.toString());
        if (CH.equals(patDto.getPatType())) {
            cnStr = "TEXEMPTCN";
            yqStr = StrUtil.format("TCNY {}YQ", yq.toString());
        } else if (IN.equals(patDto.getPatType())) {
            cnStr = "TEXEMPTCN";
            yqStr = "TEXEMPTYQ";
        }
        fn.append(StrUtil.format(" {}/ {}", cnStr, yqStr));
        // PID
        if (StrUtil.isNotEmpty(patDto.getPNo())) {
            String pValue = StrUtil.format("/P{}", patDto.getPNo());
            fn.append(pValue);
            fc.append(pValue);
            fp.append(pValue);
        }
        echo.append(StrUtil.CRLF);
        echo.append("\u0010");
        echo.append(fn).append(StrUtil.CRLF);
        echo.append(fc).append(StrUtil.CRLF);
        echo.append(fp);
        patDto.setResult(echo.toString());
    }

    /**
     * 获取开舱数据
     *
     * @return 获取开舱数据
     * @throws UnifiedResultException 异常
     */
    private MnjxOpenCabin retrieveOpenCabin(MnjxPnrSeg usedSeg) throws UnifiedResultException {
        String date = StrUtil.format("{} 00:00:00", usedSeg.getFlightDate());
        List<String> orgDstList = Arrays.asList(usedSeg.getOrg(), usedSeg.getDst());
        List<MnjxTcardSection> mnjxTcardSections = pnrCommandPartPatMapper.retrieveTcardSection(usedSeg.getFlightNo(), orgDstList);
        // 如果TCard数据空，从承运航班查询
        if (CollUtil.isEmpty(mnjxTcardSections) && StrUtil.isNotEmpty(usedSeg.getCarrierFlight())) {
            mnjxTcardSections = pnrCommandPartPatMapper.retrieveTcardSection(usedSeg.getCarrierFlight(), orgDstList);
        }
        String org = mnjxTcardSections.get(0).getAirportId();
        mnjxTcardSections = mnjxTcardSections.stream().sorted(Comparator.comparing(MnjxTcardSection::getSectionNo).reversed()).collect(Collectors.toList());
        String dst = mnjxTcardSections.get(0).getAirportId();
        List<MnjxOpenCabin> openCabins = pnrCommandPartPatMapper.retrieveOpenCabin(usedSeg.getFlightNo(), usedSeg.getFlightDate(),
                usedSeg.getCabinClass(), usedSeg.getSellCabin(), date, org, dst);
        // 如果开舱数据空，从承运航班查询
        if (CollUtil.isEmpty(openCabins) && StrUtil.isNotEmpty(usedSeg.getCarrierFlight())) {
            openCabins = pnrCommandPartPatMapper.retrieveOpenCabin(usedSeg.getCarrierFlight(), usedSeg.getFlightDate(),
                    usedSeg.getCabinClass(), usedSeg.getSellCabin(), date, org, dst);
        }
        if (ObjectUtil.isEmpty(openCabins)) {
            throw new UnifiedResultException("未找到开舱数据");
        }
        return openCabins.get(0);
    }

    /**
     * 获取机型对应的版本
     *
     * @param pnrSeg 航段信息
     * @return 获取机型对应的版本
     * @throws UnifiedResultException
     */
    private MnjxPlaneModel retrievePlaneModel(MnjxPnrSeg pnrSeg) throws UnifiedResultException {
        MnjxPlaneModel planeModel = pnrCommandPartPatMapper.retrievePlaneModel(pnrSeg.getFlightNo(), pnrSeg.getFlightDate());
        if (ObjectUtil.isEmpty(planeModel) && StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
            planeModel = pnrCommandPartPatMapper.retrievePlaneModel(pnrSeg.getCarrierFlight(), pnrSeg.getFlightDate());
        }
        if (ObjectUtil.isEmpty(planeModel)) {
            throw new UnifiedResultException("找不到对应机型");
        }
        return planeModel;
    }

    /**
     * 查询机场对应的城市
     *
     * @return 查询机场对应的城市
     * @throws UnifiedResultException 异常
     */
    private Map<String, MnjxAirport> retrieveAirports() throws UnifiedResultException {
        // 获取所有的机场三字码对应的城市信息
        List<MnjxAirport> mnjxAirports = iMnjxAirportService.lambdaQuery().list();
        if (CollUtil.isEmpty(mnjxAirports)) {
            throw new UnifiedResultException("机场信息不存在");
        }
        return mnjxAirports.stream().collect(Collectors.toMap(MnjxAirport::getAirportCode, k -> k));
    }

    /**
     * 查询运价
     *
     * @return 查询运价
     * @throws UnifiedResultException 异常
     */
    private Map<String, MnjxStandardPat> retrieveStandardPat() throws UnifiedResultException {
        // 获取所有的机场三字码对应的城市信息
        List<MnjxAirport> mnjxAirports = iMnjxAirportService.lambdaQuery().list();
        if (CollUtil.isEmpty(mnjxAirports)) {
            throw new UnifiedResultException("机场信息不存在");
        }
        // 运价
        List<MnjxStandardPat> standardPats = iMnjxStandardPatService.lambdaQuery().list();
        if (CollUtil.isEmpty(standardPats)) {
            throw new UnifiedResultException("无基础运价信息");
        }
        return standardPats.stream().collect(Collectors.toMap(k -> String.format("%s-%s", k.getOrgCityId(), k.getDstCityId()), k -> k));
    }

    /**
     * 获取机建、燃油税
     *
     * @return 获取机建、燃油税
     * @throws UnifiedResultException 异常
     */
    private MnjxCnYq retrieveCnYq() throws UnifiedResultException {
        // 获取基建燃油
        List<MnjxCnYq> cnYqs = iMnjxCnYqService.lambdaQuery().list();
        if (CollUtil.isEmpty(cnYqs)) {
            throw new UnifiedResultException("无机建、燃油税");
        }
        return cnYqs.get(0);
    }

    /**
     * 获取运价
     *
     * @param airportMap     airportMap
     * @param standardPatMap standardPatMap
     * @param org            出发地
     * @param dst            目的地
     * @return 获取运价
     */
    private MnjxStandardPat getStandardPat(Map<String, MnjxAirport> airportMap, Map<String, MnjxStandardPat> standardPatMap, String org, String dst) {
        String orgCityId = airportMap.get(org).getCityId();
        String dstCityId = airportMap.get(dst).getCityId();
        String orgDstKey = String.format("%s-%s", orgCityId, dstCityId);
        String dstOrgKey = String.format("%s-%s", dstCityId, orgCityId);
        MnjxStandardPat standardPat = null;
        if (standardPatMap.containsKey(orgDstKey) || standardPatMap.containsKey(dstOrgKey)) {
            standardPat = standardPatMap.get(orgDstKey);
            if (ObjectUtil.isEmpty(standardPat)) {
                standardPat = standardPatMap.get(dstOrgKey);
            }
        }
        return standardPat;
    }

    /**
     * 计算销售舱位价格
     *
     * @param patType
     * @param sellCabinPrice
     * @param mnjxCnYq
     * @return
     */
    private BigDecimal calSellCabinPrice(String patType, BigDecimal sellCabinPrice, MnjxCnYq mnjxCnYq) {
        BigDecimal cabinPrice;
        if (CH.equals(patType)) {
            cabinPrice = sellCabinPrice.multiply(BigDecimal.valueOf(mnjxCnYq.getChildDiscount()));
        } else if (IN.equals(patType)) {
            cabinPrice = sellCabinPrice.multiply(BigDecimal.valueOf(mnjxCnYq.getInfiDiscount()));
        } else if (GM.equals(patType) || JC.equals(patType)) {
            cabinPrice = sellCabinPrice.multiply(BigDecimal.valueOf(mnjxCnYq.getPoliceDiscount()));
        } else {
            cabinPrice = sellCabinPrice;
        }
        // 不要修改 需求是算出小数
        return cabinPrice.multiply(new BigDecimal("1.00")).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取Y价格
     */
    private BigDecimal getPriceY(MnjxPnrSeg pnrSeg) throws UnifiedResultException {
        // 航班号
        String flightNo = pnrSeg.getFlightNo();
        // 航班日期
        String flightDate = pnrSeg.getFlightDate();

        // 舱位折扣
        Map<String, String> cabinDiscountMap = new HashMap<>();
        // Cnd
        List<MnjxCnd> cndList = pnrCommandPartPatMapper.retrieveCnd(flightNo, flightDate);
        if (CollUtil.isEmpty(cndList) && StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
            cndList = pnrCommandPartPatMapper.retrieveCnd(pnrSeg.getCarrierFlight(), flightDate);
        }
        if (CollUtil.isNotEmpty(cndList)) {
            MnjxCnd cnd = cndList.get(0);
            // 一
            if (StrUtil.isNotEmpty(cnd.getFirstCabinClass())) {
                cabinDiscountMap.put(cnd.getFirstDiscount().toString(), cnd.getFirstCabinClass());
            }
            // 二
            if (StrUtil.isNotEmpty(cnd.getSecondCabinClass())) {
                cabinDiscountMap.put(cnd.getSecondDiscount().toString(), cnd.getSecondCabinClass());
            }
            // 三
            if (StrUtil.isNotEmpty(cnd.getThirdCabinClass())) {
                cabinDiscountMap.put(cnd.getThirdDiscount().toString(), cnd.getThirdCabinClass());
            }
            // 四
            if (StrUtil.isNotEmpty(cnd.getFourthCabinClass())) {
                cabinDiscountMap.put(cnd.getFourthDiscount().toString(), cnd.getFourthCabinClass());
            }
            // 五
            if (StrUtil.isNotEmpty(cnd.getFifthCabinClass())) {
                cabinDiscountMap.put(cnd.getFifthDiscount().toString(), cnd.getFifthCabinClass());
            }
        }
        // 获取100%的折扣舱位
        if (CollUtil.isNotEmpty(cabinDiscountMap) && cabinDiscountMap.containsKey(ONE_STR)) {
            String cabinClass = cabinDiscountMap.get("1.00");
            String date = StrUtil.format("{} 00:00:00", pnrSeg.getFlightDate());
            List<String> orgDstList = Arrays.asList(pnrSeg.getOrg(), pnrSeg.getDst());
            List<MnjxTcardSection> mnjxTcardSections = pnrCommandPartPatMapper.retrieveTcardSection(pnrSeg.getFlightNo(), orgDstList);
            if (CollUtil.isEmpty(mnjxTcardSections) && StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
                mnjxTcardSections = pnrCommandPartPatMapper.retrieveTcardSection(pnrSeg.getCarrierFlight(), orgDstList);
            }
            String org = mnjxTcardSections.get(0).getAirportId();
            mnjxTcardSections = mnjxTcardSections.stream().sorted(Comparator.comparing(MnjxTcardSection::getSectionNo).reversed()).collect(Collectors.toList());
            String dst = mnjxTcardSections.get(0).getAirportId();
            List<MnjxOpenCabin> openCabins = pnrCommandPartPatMapper.retrieveOpenCabin(flightNo, flightDate,
                    cabinClass, null, date, org, dst);
            if (CollUtil.isEmpty(openCabins) && StrUtil.isNotEmpty(pnrSeg.getCarrierFlight())) {
                openCabins = pnrCommandPartPatMapper.retrieveOpenCabin(pnrSeg.getCarrierFlight(), flightDate,
                        cabinClass, null, date, org, dst);
            }
            if (CollUtil.isNotEmpty(openCabins)) {
                openCabins = openCabins.stream().sorted(Comparator.comparing(MnjxOpenCabin::getSellCabinPrice).reversed()).collect(Collectors.toList());
                return new BigDecimal(openCabins.get(0).getSellCabinPrice());
            }
        }
        throw new UnifiedResultException("Y PRICE NOT FOUND");
    }

}

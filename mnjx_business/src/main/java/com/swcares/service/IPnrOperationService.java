package com.swcares.service;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.*;
import com.swcares.obj.dto.PnrSegDto;

import java.util.List;

/**
 * Pnr操作的接口
 *
 * <AUTHOR>
 */
public interface IPnrOperationService {
    /**
     * 生成新的PNR对象<br/>
     * 这个方法的使用我感觉有点奇怪
     *
     * @param memoryDataPnr 内存中的PNR大对象
     * @param mnjxOffice    登录的部门信息
     * @param mnjxSi        登录的工作号
     * @return PNR数据
     * @throws UnifiedResultException 统一异常
     */
    MnjxPnr perfectMdpMnjxPnrAndPnrCtDtos(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException;

    /**
     * PNR回显
     *
     * @param memoryDataPnr pnr内存大对象
     * @return PNR回显
     */
    String recall(MemoryDataPnr memoryDataPnr);

    /**
     * 创建新的PNR对象
     *
     * @param memoryDataPnr PNR内存大对象
     * @param mnjxOffice    部门
     * @param mnjxSi        工作号
     * @return PNR对象属性
     * @throws UnifiedResultException 统一异常
     */
    MnjxPnr createNewPnr(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException;

    /**
     * 所有机场数据
     *
     * @return 所有机场数据
     */
    List<MnjxAirport> getMnjxAirports();

    /**
     * 获得所有城市数据
     *
     * @return 城市数据
     */
    List<MnjxCity> getMnjxCities();

    /**
     * 所有基准运价数据
     *
     * @return 基准运价数据
     */
    List<MnjxStandardPat> getMnjxStandardPats();

    /**
     * RT C选项回显构建
     *
     * @param memoryDataPnr
     * @param pageType      分页行数
     * @return
     */
    String recallByRtC(MemoryDataPnr memoryDataPnr, Integer pageType);

    String getGroupName(MemoryDataPnr memoryDataPnr);

    List<MnjxPnrSeg> retrieveMnjxSegByFltNoAndDate(List<PnrSegDto> pnrSegDtos);
}

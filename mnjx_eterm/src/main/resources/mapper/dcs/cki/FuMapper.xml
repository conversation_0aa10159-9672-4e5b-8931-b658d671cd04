<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.FuMapper">

    <select id="retrieveFltDate" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt ON mt.flight_id = mf.flight_id
        WHERE mf.flight_no = #{fltNo}
          AND #{fltDate} BETWEEN mt.start_date AND mt.end_date
    </select>
    <select id="retrievePlanSection" resultType="com.swcares.eterm.dcs.cki.obj.dto.FuPlanSection">
        SELECT mps.plan_section_id,
               mps.plan_flight_id,
               mps.plane_id,
               mps.pre_plane_id,
               mps.gate,
               mps.is_last_section,
               (SELECT airport_code FROM mnjx_airport WHERE airport_id = mps.dep_apt_id) AS org,
               (SELECT airport_code FROM mnjx_airport WHERE airport_id = mps.arr_apt_id) AS dst,
               mps.estimate_off,
               mps.actual_off,
               mps.estimate_arr,
               mps.estimate_boarding,
               mpf.ck_status
        FROM mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt
                           ON mt.flight_id = mf.flight_id
                 LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
                 LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
        WHERE mf.flight_no = #{fltNo}
          AND mpf.flight_date = #{fltDate}
    </select>
    <select id="retrieveTcardSection" resultType="com.swcares.eterm.dcs.cki.obj.dto.FuTcardSection">
        SELECT mts.is_last_section,
               mts.section_no,
               (SELECT airport_code FROM mnjx_airport WHERE airport_id = mts.airport_id) AS airportCode
        FROM mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt ON mt.flight_id = mf.flight_id
                 LEFT JOIN mnjx_tcard_section mts ON mts.tcard_id = mt.tcard_id
        WHERE mf.flight_no = #{fltNo}
    </select>
    <update id="updatePlanSectionAndPlaneNo">
        update mnjx_plan_section
        <choose>
            <when test="isUpdate!='ERS' and planeId !=''">
                <set>
                    <trim prefix="plane_id=case" suffix="end,">
                        <foreach collection="planSectionList" item="item" index="index">
                            when plan_section_id=#{item.planSectionId} then #{planeId}
                        </foreach>
                    </trim>
                </set>
            </when>
            <when test="isUpdate=='ERS' or isUpdate=='RTNS'">
                <set>
                    <trim prefix="plane_id=case" suffix="end,">
                        <foreach collection="planSectionList" item="item" index="index">
                            <if test="item.prePlaneId !=null">
                                when plan_section_id=#{item.planSectionId} then #{item.prePlaneId}
                            </if>
                            <if test="item.prePlaneId ==null">
                                when plan_section_id=#{item.planSectionId} then mnjx_plan_section.prePlaneId
                            </if>
                        </foreach>
                    </trim>
                </set>
            </when>
        </choose>
        where plan_section_id in
        <foreach collection="planSectionList" item="item" index="index" separator="," open="(" close=")">
            #{item.planSectionId}
        </foreach>
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.RkMapper">

    <select id="retrievePlanFlight" resultType="java.lang.String">
        select mpf.plan_flight_id
        from mnjx_flight mf
                 LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
                 left join mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
        WHERE mf.flight_no = #{fltNo}
          AND mpf.flight_date = #{fltDate}
    </select>
</mapper>
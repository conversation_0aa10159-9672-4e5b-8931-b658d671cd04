<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.cki.mapper.CiMapper">


    <select id="retrieveFlightInfo" resultType="com.swcares.eterm.dcs.cki.obj.dto.CiDto">
		SELECT DISTINCT
			mpf.status_update_time,
			mpm.plane_model_type,
			mpm.plane_model_version,
				mf.flight_no,
				mpf.flight_date,
				moc.cabin_class,
				mpf.ck_status,
				mpf.pre_ck_status,
				IFNULL(mps.actual_boarding,mps.estimate_boarding) boarding,
				IFNULL(mps.actual_off,mps.estimate_off) off,
				mps.estimate_off,
				mps.estimate_off_change,
				mps.estimate_arr,
				mps.estimate_arr_change,
				mc.layout,
				/*
				出发机场
				*/
				( SELECT ma.airport_code FROM mnjx_airport ma WHERE mps.dep_apt_id = ma.airport_id ) dst,
					/*
				到达机场
				*/
				( SELECT ma.airport_code FROM mnjx_airport ma WHERE mps.arr_apt_id= ma.airport_id ) arr
		FROM
			mnjx_flight mf
			LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
			LEFT JOIN mnjx_plan_flight mpf ON mpf.tcard_id = mt.tcard_id
			LEFT JOIN mnjx_plan_section mps ON mps.plan_flight_id = mpf.plan_flight_id
			LEFT JOIN mnjx_plane mp ON mp.plane_id = mps.plane_id
			LEFT JOIN mnjx_plane_model mpm ON mpm.plane_model_id = mp.plane_model_id
			LEFT JOIN mnjx_cnd mc ON mc.cnd_id = mp.cnd_id
			LEFT JOIN mnjx_open_cabin moc ON moc.plan_section_id = mps.plan_section_id
		WHERE
			mf.flight_no = #{flightNo}
			AND mpf.flight_date = #{flightDate}
    </select>
</mapper>

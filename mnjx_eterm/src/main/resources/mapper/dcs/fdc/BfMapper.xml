<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.dcs.fdc.mapper.BfMapper">
    <select id="retrieveFlightByDate" resultType="com.swcares.entity.MnjxTcard">
        select
        *
        from
        mnjx_tcard mt
        where
        mt.flight_id = #{flightId}
        and mt.start_date &lt;= #{flightDate}
        and mt.end_date &gt;= #{flightDate}
    </select>
</mapper>

********************************************************************************
*                  CAAC  MIS  OPTAT  DAILY-SALES-REPORT                        *
*                                                                              *
*   OFFICE : #(resDto.office)    IATA NUMBER : #(resDto.iataNumber)    DEVICE : #(fillAfter(resDto.device,cn.hutool.core.util.CharUtil::SPACE,3))  #(resDto.pid)            *
*   DATE   : #(resDto.dateCom)                               AIRLINE:   #(resDto.airline)                *
--------------------------------------------------------------------------------
 #(paramsDto.param=="A"?"AGENT":"AIRLINE")    NOMAL FARE  COMM    REFUND FARE    COMM    TICKETS    VOID   REFUND#(wrap())
#for(t:resDto.ticketList)
 #(paramsDto.param=="A"?t.agentNo:t.settlementCode)      #(fillAfter(t.nomalFare,cn.hutool.core.util.CharUtil::SPACE,6))      0       #(fillAfter(t.refundFare,cn.hutool.core.util.CharUtil::SPACE,6))         0       #(fillAfter(t.tickets,cn.hutool.core.util.CharUtil::SPACE,3))        0      #(t.refund)#(wrap())
#end
*==============================================================================*
     TOTAL TICKETS:       #(resDto.totalTickets) (      0 TICKETS VOID /      #(resDto.ticketRefund) TICKETS REFUND )#(wrap())
----------------NORMAL TICKETS--------------------------------------------------#(wrap())
   NORMAL  FARE-- AMOUNT :          #(fillBefore(resDto.normalFare,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
      CARRIERS -- AMOUNT :          #(fillBefore(resDto.carries,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
        COMMIT -- AMOUNT :          #(fillBefore(resDto.commit,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
   NORMAL  TAX -- AMOUNT :          #(fillBefore(resDto.normalTax,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
----------------REFUND TICKETS--------------------------------------------------
    NET REFUND -- AMOUNT :          #(fillBefore(resDto.netRefund,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
     DEDUCTION -- AMOUNT :          #(fillBefore(resDto.deduction,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
 REFUND    TAX -- AMOUNT :          #(fillBefore(resDto.refundTax,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
 REFUND COMMIT -- AMOUNT :          #(fillBefore(resDto.refundCommit,cn.hutool.core.util.CharUtil::SPACE,9))               CNY#(wrap())
*==============================================================================*
 #(args[0].totalCmd)#(wrap())
#for(flight:results[0])
#if(for.index == 0)DEP TIME   ARR TIME   WEEK FLY   GROUND TERM   TYPE MEAL  #if(flight.carrierFlight??)OPE#end#(wrap())#end
#(flight.org) #(flight.estimateOff)#(flight.estimateOffChange??"  ") #(flight.dst) #(flight.estimateArr)#(flight.estimateArrChange??"  ") #(flight.week) #(flight.fly) #(flight.ground)  T1/T2  #(flight.planeModelType) #(flight.mealCode)     #if(flight.carrierFlight??)#(flight.carrierFlight)#end#(wrap())
#end
TOTAL JOURNEY TIME   #(results[1])#(wrap())
#for(flight:results[0])
#(flight.org)#(flight.dst)#for(x : flight.sellCabinAndSeat)#if(for.index == 19)#(wrap())#("      ")#(" ")#(subString(x,0,2))#else#(" ")#(subString(x,0,2))#end#end#(wrap())
#end
#if(results[0].get(0).carrierFlight??)
MEMBER OF SKYTEAM
#end
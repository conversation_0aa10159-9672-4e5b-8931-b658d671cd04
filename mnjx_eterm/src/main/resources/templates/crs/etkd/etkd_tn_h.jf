#set(ticketDto = resDto[0].ticketDto)
ETKD:TN/#(ticketDto.ticketNo)#(wrap())
TKTN:#(fillString(ticketDto.ticketNo,15,false))NAME: #((ticketDto.adlName??)?ticketDto.adlName:ticketDto.xnName+" INF")#(wrap())
IATA OFFC: #(maxLength(ticketDto.iataOffice,8,9))ISSUED: #(ticketDto.issuedDate) RVAL: 00#(wrap())
  1    #(subPre(ticketDto.issuedDate,5))/#(ticketDto.issuedTime)/#(ticketDto.issuedSiNo)     TRMK  #(ticketDto.officeNo)+DEV-#(ticketDto.printNo)
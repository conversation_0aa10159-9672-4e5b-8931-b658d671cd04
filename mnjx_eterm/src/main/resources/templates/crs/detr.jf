#set(retrieveDto = results[0],resDto= results[1])
#switch (retrieveDto.param)
    #case ("TN")
    #if(retrieveDto.option??&&retrieveDto.option=='F')
#include("detr/detr_tn_f.jf")
    #else if(retrieveDto.option=='X')
#include("detr/detr_tn_x.jf")
    #else if(retrieveDto.option=='H')
#include("detr/detr_tn_h.jf")
    #else
#include("detr/detr_tn.jf")
    #end
    #case ("NI","ID","PP")
    #if(resDto.size()>1)
#include("detr/detr_ni.jf")
    #else
#set(ticketDto=resDto[0].ticketDto)
DETR:TN/#(format("{}-{}",subPre(ticketDto.ticketNo,3),subSuf(ticketDto.ticketNo,3)))#(wrap())
#include("detr/detr_tn.jf")
    #end
    #case ("CN","NM")
    #if(resDto.size()>1)
#include("detr/detr_cn.jf")
    #else
#set(ticketDto=resDto[0].ticketDto)
DETR:TN/#(format("{}-{}",subPre(ticketDto.ticketNo,3),subSuf(ticketDto.ticketNo,3)))#(wrap())
#include("detr/detr_tn.jf")
    #end
    #default
#include("detr/detr_tn.jf")
#end

*******************************************************************************#(wrap())
*                       TICKET STORE/USE  REPORT#(wrap())
* AGENT : #(results[0].agent)                                    AIRLINE : #(results[0].airline)#(wrap())
* OFFICE: #(results[0].office)                                  IATA NO.: #(results[0].iataNo)#(wrap())
* DATE  : #(results[0].date)                                    TIME : #(results[0].time)#(wrap())
-------------------------------------------------------------------------------#(wrap())
        Form  First     Last       Granted  Granted By  Granted   Ticket  Allo#(wrap())
Office  Code  TKT no.   TKT   Qua  By OFF   Agent/Pid    Date     T/Tp/M  Tp/ST#(wrap())
-------------------------------------------------------------------------------#(wrap())
System   Ticket  Range   Qua   Start/End  Date Office  Agent/Pid  Dev  Use#(wrap())
-------------------------------------------------------------------------------#(wrap())
#if(results[1]??)
#for(r : results[1])
#(r)#(wrap())
#end
#end
===============================================================================#(wrap())
TOTAL TICKETS : #(results[0].totalTicket) TOTAL IN USE: #(results[0].totalInUser) TOTAL STORE: #(results[0].totalStore) *
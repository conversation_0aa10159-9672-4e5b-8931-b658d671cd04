#set(segSize = segs.size(), lastSeg = segs.get(segSize - 1), null)
#for(seg:segs)
#if(seg.isTransit())
TRANSIT R#(seg.rNo)   C#(seg.cNo)  B#(seg.bNo)  #if(seg.umNo??)UM#(seg.umNo)  #end#if(seg.wchNo??)WCH#(seg.wchNo)  #end#if(seg.inftNo??)I#(seg.inftNo)#end#(wrap())
#if(seg.chldNo??)                                     CHD#(seg.chldNo)#(wrap())#end
        SA#(seg.saNo)                EXST#(seg.exstNo) XCR#(seg.xcrNo) ZCR#(seg.zcrNo)#(wrap())
        BOARDING#(seg.boardingNo)          VIP#(seg.vipNo)#(wrap())
        CCHD#(seg.cchdNo)              CINF#(seg.cinfNo)#(wrap())
        BCHD#(seg.bchdNo)              BINF#(seg.binfNo)#(wrap())
        RET#(seg.retNo)               CET#(seg.cetNo)#(wrap())
        INF-CET#(seg.infCetNo)           INTER-CET#(seg.interCetNo)#(wrap())
        RETRP#(seg.retrpNo)             CETRP#(seg.cetrpNo)#(wrap())
#else
*#(seg.cityPair) R#(seg.rNo)   C#(seg.cNo)  B#(seg.bNo)  #if(seg.umNo??)UM#(seg.umNo)  #end#if(seg.wchNo??)WCH#(seg.wchNo)  #end#if(seg.inftNo??)I#(seg.inftNo)#end#(wrap())
        SB#(seg.sbNo)  B#(seg.sbBagNo)         #if(seg.avihNo??)AVIH#(seg.avihNo) #end#if(seg.chldNo??)CHD#(seg.chldNo)#end#(wrap())
        SA#(seg.saNo)                EXST#(seg.exstNo) XCR#(seg.xcrNo) ZCR#(seg.zcrNo)#(wrap())
        BOARDING#(seg.boardingNo)          VIP#(seg.vipNo)#(wrap())
        CCHD#(seg.cchdNo)              CINF#(seg.cinfNo)#(wrap())
        BCHD#(seg.bchdNo)              BINF#(seg.binfNo)#(wrap())
        RET#(seg.retNo)               CET#(seg.cetNo)#(wrap())
        INF-CET#(seg.infCetNo)           INTER-CET#(seg.interCetNo)#(wrap())
        RETRP#(seg.retrpNo)             CETRP#(seg.cetrpNo)#(wrap())
#end
#end
#if(segs.size() > 1)
TOTALS* R#(total.rNo)   C#(total.cNo)  B#(total.bNo)  #if(total.umNo??)UM#(total.umNo)  #end#if(total.wchNo??)WCH#(total.wchNo)  #end#if(total.inftNo??)I#(total.inftNo)#end#(wrap())
        SB#(total.sbNo)  B#(total.sbBagNo)         #if(total.avihNo??)AVIH#(total.avihNo) #end#if(total.chldNo??)CHD#(total.chldNo)#end#(wrap())
        SA#(total.saNo)                EXST#(total.exstNo) XCR#(total.xcrNo) ZCR#(total.zcrNo)#(wrap())
        BOARDING#(total.boardingNo)          VIP#(total.vipNo)#(wrap())
        CCHD#(total.cchdNo)              CINF#(total.cinfNo)#(wrap())
        BCHD#(total.bchdNo)              BINF#(total.binfNo)#(wrap())
        RET#(total.retNo)               CET#(total.cetNo)#(wrap())
        INF-CET#(total.infCetNo)           INTER-CET#(total.interCetNo)#(wrap())
        RETRP#(total.retrpNo)             CETRP#(total.cetrpNo)#(wrap())
#end
**********WELCOME TO #(lastSeg.airportInfo)**********#(wrap())
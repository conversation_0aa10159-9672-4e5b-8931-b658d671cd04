package com.swcares.server.netty;

import com.swcares.core.util.ArrayUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class EtermLoginDecoder extends ByteToMessageDecoder {
    private final static byte TAG = -94;

    private final static int PROTOCOL_HEADER_LENGTH =2;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf msg, List<Object> out) {
        byte[] bytes = ByteBufUtil.getBytes(msg, 0, msg.readableBytes(), true);
        ByteBuf buffer = ctx.alloc().buffer();
        if (bytes.length >= PROTOCOL_HEADER_LENGTH && bytes[0] == 1 && bytes[1] == TAG) {
            byte[] head = ArrayUtils.sub(bytes, 0, 1);
            byte[] tail = ArrayUtils.sub(bytes, 1, bytes.length);
            byte[] all = ArrayUtils.addAll(head, new byte[]{0, 0}, tail);
            all[3] = (byte) all.length;
            bytes = all;
        }
        msg.clear();
        log.debug("清理数据前:读指针{},写指针{}", buffer.readerIndex(), buffer.writerIndex());
        buffer.clear();
        log.debug("未写入数据前:读指针{},写指针{}", buffer.readerIndex(), buffer.writerIndex());
        buffer.writeBytes(bytes);
        log.debug("写入数据后:读指针{},写指针{}", buffer.readerIndex(), buffer.writerIndex());
        out.add(buffer);
    }
}

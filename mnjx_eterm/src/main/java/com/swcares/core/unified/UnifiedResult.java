package com.swcares.core.unified;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedResult implements Serializable {
    /**
     * 指令(instruction)
     * 不需要手动指定，程序会自动设置
     */
    private String action;
    /**
     * 指令的参数列表
     */
    private Object[] args;
    /**
     * 返回结果,可以返回多个结果
     */
    private Object[] results;
}

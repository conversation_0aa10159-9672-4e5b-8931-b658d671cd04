package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ListUtils;
import com.swcares.core.type.OperateType;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.service.ICiService;

import javax.annotation.Resource;

/**
 * 初始航班关闭
 *
 * <AUTHOR>
 */
@OperateType(action = "CI", shorthand = true, template = "/dcs/cki/ci.jf")
public class CiHandler implements Handler {

    @Resource
    private ICiService iCiService;


    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        Object ciDto = iCiService.handle(cmd);
        unifiedResult.setResults(ListUtils.toList(ciDto).toArray());
        unifiedResult.setArgs(ListUtils.toList(cmd).toArray());
        return unifiedResult;
    }
}

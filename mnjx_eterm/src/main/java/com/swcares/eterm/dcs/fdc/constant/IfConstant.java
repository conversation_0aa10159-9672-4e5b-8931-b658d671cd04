/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.dcs.fdc.constant;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.util.DateUtils;

/**
 * <AUTHOR>
 */
public class IfConstant {
    /**
     * 初始化的标志
     */
    public static final String IS_INIT = "Y";
    /**
     * 已经初始化
     */
    public static final String FLT_ALREADY_INITIALIZE = "FLT ALREADY INITIALIZE";

    /**
     * 如果当前系统时间大于要初始化航班的日期及出发时刻
     */
    public static final String FLGHT_ALREADY_DEPARTED= "FLGHT ALREADY DEPARTED";

    /**
     * 初始时间
     */
    public static final String CKI_TIME = StrUtil.format("CKI TIME {}", DateUtils.date2hm(DateUtil.date()));
    /**
     * 初始化失败
     */
    public static final String INITIALIZE_ERROR = "INITIALIZE ERROR";
}

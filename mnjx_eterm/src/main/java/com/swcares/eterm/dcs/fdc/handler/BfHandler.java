package com.swcares.eterm.dcs.fdc.handler;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.fdc.service.IBfService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.eterm.sendAgent.normal.fdc
 * Description：显示/建立/修改季节性航班的数据信息
 * date 2020-06-2020/6/16 14:59:39
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Slf4j
@OperateType(action = "BF", shorthand = true, template = "/dcs/fdc/BF.jf")
public class BfHandler implements Handler {

	@Resource
	private IBfService iBfService;

	@Override
	public Object handle(String cmd) throws UnifiedResultException {
		MemoryData memoryData = MemoryDataUtils.getMemoryData();
		return iBfService.handle(memoryData, cmd);
	}
}

package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.BtDto;

/**
 * BT指令接口
 *
 * <AUTHOR>
 */
public interface IBtService {
    /***
     * 参数解析
     * @param cmd
     * @return
     * @throws UnifiedResultException
     */
    BtDto parse(String cmd) throws UnifiedResultException;

    /***
     * 业务处理
     * @param btDto
     * @return
     * @throws UnifiedResultException
     */
    String handler(BtDto btDto) throws UnifiedResultException;
}

package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.mapper.JcsyMapper;
import com.swcares.eterm.dcs.cki.obj.dto.JcsyDto;
import com.swcares.eterm.dcs.cki.obj.vo.JcsyTotalVo;
import com.swcares.eterm.dcs.cki.obj.vo.JcsyVo;
import com.swcares.eterm.dcs.cki.service.IJcsyService;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/7 11:08
 */
@Slf4j
@Service
public class JcsyServiceImpl implements IJcsyService {

    private static final Pattern JCSY_PATTERN = Pattern.compile("^([A-Z0-9]+/)?(\\w+|[+-.])?(/\\w+)?,(\\w+)?(,(N)?(\\w+))?$");

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxCndService iMnjxCndService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private JcsyMapper jcsyMapper;

    @Override
    public JcsyDto parseCmd(String cmd) throws UnifiedResultException {
        String[] split = cmd.split(":");
        if (split.length != 2) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        JcsyDto jcsyDto = new JcsyDto();
        String param = split[1];
        if (ReUtil.isMatch(JCSY_PATTERN, param)) {
            List<String> allGroups = ReUtil.getAllGroups(JCSY_PATTERN, param);
            String flightNo = allGroups.get(1);
            String flightDate = allGroups.get(2);
            String orgCityCode = allGroups.get(3);
            String option = allGroups.get(4);
            String unMark = allGroups.get(6);
            String airlineCode = allGroups.get(7);
            // 验证航班格式
            if (StrUtil.isEmpty(flightNo)) {
                throw new UnifiedResultException(Constant.FLT_NBR);
            }
            flightNo = flightNo.replace("/", "");
            if (!flightNo.matches("[A-Z0-9]{5,6}")) {
                throw new UnifiedResultException(Constant.FLT_NBR);
            }
            // 验证日期格式
            if (StrUtil.isEmpty(flightDate)) {
                throw new UnifiedResultException(Constant.OPTION);
            }
            if (!flightDate.matches("(\\d{2}[A-Z]{3}(\\d{2})?)|([+.-])")) {
                throw new UnifiedResultException(Constant.DATE);
            }
            flightDate = DateUtils.com2ymd(flightDate);
            // 只能查当天和明天的数据
            if (DateUtil.compare(DateUtil.parseDate(flightDate), DateUtil.parseDate(DateUtil.today())) < 0
                    || DateUtil.compare(DateUtil.parseDate(flightDate), DateUtil.tomorrow()) > 0) {
                throw new UnifiedResultException(Constant.FLT_NBR);
            }
            // 验证出发城市（非必填）
            if (StrUtil.isNotEmpty(orgCityCode)) {
                orgCityCode = orgCityCode.replace("/", "");
                if (!orgCityCode.matches("[A-Z]{3}")) {
                    throw new UnifiedResultException(Constant.CITY);
                }
                jcsyDto.setOrgCityCode(orgCityCode);
            } else {
                jcsyDto.setOrgCityCode(MemoryDataUtils.getMemoryData().getMnjxOffice().getOfficeNo().substring(0, 3));
            }
            // 验证选项
            if (StrUtil.isEmpty(option)) {
                throw new UnifiedResultException(Constant.OPTION);
            }
            if (!option.matches("[IO]")) {
                throw new UnifiedResultException(Constant.OPTION);
            }
            // 验证非标识（非必填）
            if (StrUtil.isNotEmpty(unMark)) {
                jcsyDto.setUnMark(true);
            }
            // 验证航司（非必填）
            if (StrUtil.isNotEmpty(airlineCode)) {
                if (!airlineCode.matches("[A-Z0-9]{2}")) {
                    throw new UnifiedResultException(Constant.OPTION);
                }
                jcsyDto.setAirlineCode(airlineCode);
            }
            jcsyDto.setFlightNo(flightNo);
            jcsyDto.setFlightDate(flightDate);
            jcsyDto.setOption(option);
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return jcsyDto;
    }

    @Override
    public JcsyTotalVo handle(JcsyDto jcsyDto) throws UnifiedResultException {
        String flightNo = jcsyDto.getFlightNo();
        String option = jcsyDto.getOption();
        // 查询验证城市存在
        MnjxCity mnjxCity = iMnjxCityService.lambdaQuery()
                .eq(MnjxCity::getCityCode, jcsyDto.getOrgCityCode())
                .one();
        if (ObjectUtil.isEmpty(mnjxCity)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        List<MnjxAirport> airportList = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getCityId, mnjxCity.getCityId())
                .list();
        // 按条件查出指定的航段组
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getFlightNo, flightNo)
                .eq(MnjxPnrSeg::getFlightDate, jcsyDto.getFlightDate())
                .in(MnjxPnrSeg::getOrg, airportList.stream().map(MnjxAirport::getAirportCode).collect(Collectors.toList()))
                .list();
        if (CollUtil.isEmpty(pnrSegList)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        List<JcsyVo> jcsyVoList = new ArrayList<>();
        // 查出航段组所在PNR其他的进出港航段组
        List<MnjxPnrSeg> otherPnrSegList = iMnjxPnrSegService.lambdaQuery()
                .in(MnjxPnrSeg::getPnrId, pnrSegList.stream().map(MnjxPnrSeg::getPnrId).collect(Collectors.toList()))
                .notIn(MnjxPnrSeg::getPnrSegId, pnrSegList.stream().map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList()))
                .list();
        // 筛选航司
        if (StrUtil.isNotEmpty(jcsyDto.getAirlineCode())) {
            if (jcsyDto.isUnMark()) {
                otherPnrSegList = otherPnrSegList.stream()
                        .filter(s -> !jcsyDto.getAirlineCode().equals(s.getFlightNo().substring(0, 2)))
                        .collect(Collectors.toList());
            } else {
                otherPnrSegList = otherPnrSegList.stream()
                        .filter(s -> jcsyDto.getAirlineCode().equals(s.getFlightNo().substring(0, 2)))
                        .collect(Collectors.toList());
            }
        }
        if (CollUtil.isEmpty(otherPnrSegList)) {
            throw new UnifiedResultException(Constant.NIL);
        }
        // 筛选进/出港联程
        if ("O".equals(option)) {
            otherPnrSegList = otherPnrSegList.stream()
                    .filter(p -> pnrSegList.stream().anyMatch(s -> p.getPnrId().equals(s.getPnrId()) && p.getPnrSegNo() > s.getPnrSegNo()))
                    .collect(Collectors.toList());
        } else {
            otherPnrSegList = otherPnrSegList.stream()
                    .filter(p -> pnrSegList.stream().anyMatch(s -> p.getPnrId().equals(s.getPnrId()) && p.getPnrSegNo() < s.getPnrSegNo()))
                    .collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(otherPnrSegList)) {
            throw new UnifiedResultException(Constant.NIL);
        }

        int maxCabinClassNum = 0;
        StringBuilder totalBkdBuilder = new StringBuilder();
        StringBuilder totalChkNtcBuilder = new StringBuilder();
        StringBuilder totalChkTcBuilder = new StringBuilder();
        StringBuilder totalChkBuilder = new StringBuilder();
        StringBuilder totalNbrdBuilder = new StringBuilder();
        StringBuilder totalUckBuilder = new StringBuilder();
        StringBuilder totalBagBuilder = new StringBuilder();
        int totalBagNum = 0;
        int totalBagWeight = 0;

        // 按航班号分组，统计信息
        Map<String, List<MnjxPnrSeg>> flightNoSegListMap = otherPnrSegList.stream()
                .collect(Collectors.groupingBy(MnjxPnrSeg::getFlightNo));
        for (Map.Entry<String, List<MnjxPnrSeg>> entry : flightNoSegListMap.entrySet()) {
            String keyFlightNo = entry.getKey();
            MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, keyFlightNo)
                    .one();
            MnjxTcard mnjxTcard = iMnjxTcardService.lambdaQuery()
                    .eq(MnjxTcard::getFlightId, mnjxFlight.getFlightId())
                    .one();
            MnjxCnd mnjxCnd = iMnjxCndService.getById(mnjxTcard.getCndId());
            String[] layoutSplit = mnjxCnd.getLayout().split("/");
            if (maxCabinClassNum < layoutSplit.length) {
                maxCabinClassNum = layoutSplit.length;
            }
            JcsyVo jcsyVo = new JcsyVo();
            List<MnjxPnrSeg> segList = entry.getValue();
            StringBuilder bkdBuilder = new StringBuilder();
            StringBuilder chkNtcBuilder = new StringBuilder();
            StringBuilder chkTcBuilder = new StringBuilder();
            StringBuilder chkBuilder = new StringBuilder();
            StringBuilder nbrdBuilder = new StringBuilder();
            StringBuilder uckBuilder = new StringBuilder();
            StringBuilder bagBuilder = new StringBuilder();

            Map<String, List<MnjxPnrSeg>> cabinClassSegListMap = segList.stream()
                    .collect(Collectors.groupingBy(MnjxPnrSeg::getCabinClass));

            for (int i = 0; i < layoutSplit.length; i++) {
                String cabinClassSeats = layoutSplit[i];
                String cabinClass = cabinClassSeats.substring(0, 1);
                List<MnjxPnrSeg> cabinClassSegList = cabinClassSegListMap.get(cabinClass);

                // 这几个暂时不设值，还没弄清楚业务数据是什么
                if (layoutSplit.length > 2 && i == 0) {
                    bkdBuilder.append("00/");
                    chkNtcBuilder.append("00/");
                    chkTcBuilder.append("00/");
                    uckBuilder.append("00/");
                } else {
                    if (i < layoutSplit.length - 1) {
                        bkdBuilder.append("000/");
                        if ("O".equals(option)) {
                            chkNtcBuilder.append("000/");
                            chkTcBuilder.append("000/");
                        } else {
                            chkBuilder.append("000/");
                            nbrdBuilder.append("000/");
                        }
                        uckBuilder.append("000/");
                    } else {
                        bkdBuilder.append("000");
                        if ("O".equals(option)) {
                            chkNtcBuilder.append("000+00");
                            chkTcBuilder.append("000+00");
                        } else {
                            chkBuilder.append("000+00");
                            nbrdBuilder.append("000+00");
                        }
                        uckBuilder.append("000");
                    }
                }
            }
            // 计算行李
            // 行李
            List<MnjxLuggage> luggageList = jcsyMapper.retrieveLuggageFromSegList(segList.stream().map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList()));
            luggageList = luggageList.stream()
                    .filter(l -> StrUtil.isEmpty(l.getIsDel()) || !Constant.DELETE_TYPE.equals(l.getIsDel()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(luggageList)) {
                // 当前段行李
                // 行李重量，只统计已值机或登机的重量
                AtomicInteger bNoPart1 = new AtomicInteger();
                AtomicInteger bNoPart2 = new AtomicInteger();
                // 取出所有包含当前航段的、行李组分
                List<MnjxLuggage> currentLuggageList = luggageList.stream()
                        .filter(k -> StrUtil.isEmpty(k.getLuggageGroupId()))
                        .collect(Collectors.toList());
                // 取出同组行李，去除重复，只计算一次重量，数量要单独计算
                List<MnjxLuggage> luggageGroup = luggageList.stream()
                        .filter(k -> StrUtil.isNotEmpty(k.getLuggageGroupId()))
                        .filter(k -> StrUtil.equalsAny(k.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(luggageGroup)) {
                    currentLuggageList.addAll(luggageGroup.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MnjxLuggage::getLuggageGroupId))), ArrayList::new)
                            ));
                }
                List<MnjxLuggage> avihLuggageGroup = luggageList.stream()
                        .filter(k -> StrUtil.isNotEmpty(k.getLuggageGroupId()))
                        .filter(k -> StrUtil.equalsAny(k.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(avihLuggageGroup)) {
                    currentLuggageList.addAll(avihLuggageGroup.stream()
                            .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MnjxLuggage::getLuggageGroupId))), ArrayList::new)
                            ));
                }
                if (CollUtil.isNotEmpty(currentLuggageList)) {
                    long luggageSize = currentLuggageList.stream()
                            .filter(k -> StrUtil.equalsAny(k.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                            .count();
                    int groupTypeSize = luggageGroup.stream()
                            .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId))
                            .size();
                    // 计算件数时，luggageSie里面的件数是单件行李件数+组合行李个数（不是所有的组合行李，比如加的3/23，这里个数是1，件数是3，计算总件数应该加件数3），需要减去组合行李个数再加上组合行李件数
                    luggageSize = luggageSize + luggageGroup.size() - groupTypeSize;
                    bNoPart1.addAndGet((int) luggageSize);
                    // 计算重量
                    int sum = currentLuggageList.stream()
                            .filter(l -> ObjectUtil.isEmpty(l.getWeightPool()))
                            .mapToInt(MnjxLuggage::getLuggageWeight)
                            .sum();
                    sum += currentLuggageList.stream()
                            .filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()))
                            .mapToInt(MnjxLuggage::getWeightPool)
                            .sum();
                    bNoPart2.addAndGet(sum);
                }
                totalBagNum += bNoPart1.get();
                totalBagWeight += bNoPart2.get();
                bagBuilder.append(StrUtil.fill(bNoPart1.toString(), '0', 3, true)).append("/").append(StrUtil.fill(bNoPart2.toString(), '0', 4, true));
            } else {
                bagBuilder.append("000/0000");
            }

            MnjxPnrSeg tempSeg = segList.get(0);
            jcsyVo.setFlightNo(StrUtil.fill(keyFlightNo, ' ', 7, false));

            jcsyVo.setDest(tempSeg.getDst());
            jcsyVo.setDept(tempSeg.getEstimateOff());
            // 查询登机口
            MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                    .eq(MnjxPlanFlight::getTcardId, mnjxTcard.getTcardId())
                    .eq(MnjxPlanFlight::getFlightDate, jcsyDto.getFlightDate())
                    .one();
            String oOrgAirportId = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, tempSeg.getOrg())
                    .one()
                    .getAirportId();
            MnjxPlanSection planSection = iMnjxPlanSectionService.lambdaQuery()
                    .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                    .eq(MnjxPlanSection::getDepAptId, oOrgAirportId)
                    .one();
            jcsyVo.setGtd(StrUtil.fill(planSection.getGate(), ' ', 3, false));

            jcsyVo.setOrig(tempSeg.getOrg());
            jcsyVo.setArvl(tempSeg.getEstimateArr());

            jcsyVo.setBkd(bkdBuilder.toString());
            jcsyVo.setChkNtc(chkNtcBuilder.toString());
            jcsyVo.setChkTc(chkTcBuilder.toString());
            jcsyVo.setChk(chkBuilder.toString());
            jcsyVo.setUck(uckBuilder.toString());
            jcsyVo.setNbrd(nbrdBuilder.toString());
            jcsyVo.setBag(bagBuilder.toString());
            jcsyVo.setCabinClassNum(layoutSplit.length);
            jcsyVoList.add(jcsyVo);
        }

        totalBagBuilder
                .append(StrUtil.fill(StrUtil.toString(totalBagNum), '0', 3, true))
                .append("/")
                .append(StrUtil.fill(StrUtil.toString(totalBagWeight), '0', 4, true));

        // 这几个暂时不设值，还没弄清楚业务数据是什么
        if (maxCabinClassNum == 3) {
            totalBkdBuilder.append("00/000/000");
            if ("O".equals(option)) {
                totalChkNtcBuilder.append("00/000/000+00");
                totalChkTcBuilder.append("00/000/000+00");
            } else {
                totalChkBuilder.append("00/000/000+00");
                totalNbrdBuilder.append("00/000/000+00");
            }
            totalUckBuilder.append("00/000/000");
        } else {
            totalBkdBuilder.append("000/000");
            if ("O".equals(option)) {
                totalChkNtcBuilder.append("000/000+00");
                totalChkTcBuilder.append("000/000+00");
            } else {
                totalChkBuilder.append("000/000+00");
                totalNbrdBuilder.append("000/000+00");
            }
            totalUckBuilder.append("000/000");
        }

        JcsyTotalVo jcsyTotalVo = new JcsyTotalVo();
        jcsyTotalVo.setMaxCabinClassNum(maxCabinClassNum);
        jcsyTotalVo.setTotalBkd(totalBkdBuilder.toString());
        jcsyTotalVo.setTotalChkNtc(totalChkNtcBuilder.toString());
        jcsyTotalVo.setTotalChkTc(totalChkTcBuilder.toString());
        jcsyTotalVo.setTotalChk(totalChkBuilder.toString());
        jcsyTotalVo.setTotalUck(totalUckBuilder.toString());
        jcsyTotalVo.setTotalNbrd(totalNbrdBuilder.toString());
        jcsyTotalVo.setTotalBag(totalBagBuilder.toString());
        jcsyTotalVo.getJcsyVoList().addAll(jcsyVoList);
        return jcsyTotalVo;
    }
}

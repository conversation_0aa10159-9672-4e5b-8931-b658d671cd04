package com.swcares.eterm.dcs.fdc.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.fdc.service.ISflService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * ClassName：com.swcares.eterm.sendAgent.normal.fdc
 * Description：查询未生效航班
 * date 2020-06-2020/6/16 14:59:39
 *
 * <AUTHOR>
 * @version v1.0.0
 */
@Slf4j
@OperateType(action = "SFL", template = "/dcs/fdc/SFL.jf")
public class SflHandler implements Handler {

	@Resource
	private ISflService iSflService;

	@Override
	public Object handle(String cmd) throws UnifiedResultException {
		return iSflService.handle(cmd);
	}
}

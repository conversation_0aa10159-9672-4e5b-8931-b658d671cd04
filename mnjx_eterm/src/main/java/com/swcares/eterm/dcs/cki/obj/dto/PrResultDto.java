package com.swcares.eterm.dcs.cki.obj.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PrResultDto implements Serializable {

    private List<PrDto> prDtos = new ArrayList<>();

    private PdInfoDto pdInfoDto;

    /**
     * 参数
     */
    private String param;

    /**
     * 舱位
     */
    private String cabin;

    /**
     * 是否允许展示多条
     */
    private boolean isMultiple;
}

package com.swcares.eterm.dcs.fdc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.MnjxAirline;
import com.swcares.eterm.dcs.fdc.dto.ExDto;
import com.swcares.eterm.dcs.fdc.dto.ExParamDto;
import com.swcares.eterm.dcs.fdc.mapper.ExMapper;
import com.swcares.eterm.dcs.fdc.service.IExService;
import com.swcares.service.IMnjxAirlineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2022-05-12 10:08:49
 */
@Service
@Slf4j
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
public class ExServiceImpl implements IExService {

    private static final String REG_FMT_EX = "EX:(\\w{2})/?(\\w*)?";

    @Resource
    private ExMapper exMapper;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Override
    @Cacheable(key = "'exParamDto'+#memoryData.memoryDataId")
    public ExParamDto parseEx(MemoryData memoryData) throws UnifiedResultException {
        throw new UnifiedResultException(Constant.NO_DISPLAY_SYMBOL);
    }

    @Override
    @CachePut(key = "'exParamDto' + #memoryData.memoryDataId")
    public ExParamDto parseEx(MemoryData memoryData, String cmd) throws UnifiedResultException {
        ExParamDto exParamDto = new ExParamDto();
        // 1、验证指令格式
        if (!ReUtil.isMatch(REG_FMT_EX, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(REG_FMT_EX), cmd, false);
        String airlineCode = allGroups.get(0);
        String planeModelType = allGroups.get(1);
        exParamDto.setAirlineCode(airlineCode);
        exParamDto.setPlaneModelType(planeModelType);
        return exParamDto;
    }

    @Override
    @Cacheable(key = "'exResult' + #memoryData.memoryDataId")
    public List<ExDto> handle(MemoryData memoryData) throws UnifiedResultException {
        throw new UnifiedResultException(Constant.NO_DISPLAY_SYMBOL);
    }

    /**
     * 获取机型数据
     *
     * @return 机型数据
     */
    @Override
    @CachePut(key = "'exResult' + #memoryData.memoryDataId")
    public List<ExDto> handle(MemoryData memoryData, ExParamDto exParamDto) throws UnifiedResultException {
        //缓存EX指令，SEI指令需要
        String airlineCode = exParamDto.getAirlineCode();
        String planeModelType = exParamDto.getPlaneModelType();
        //根据航空公司二字码获取航空公司id
        MnjxAirline mnjxAirline = iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineCode, exParamDto.getAirlineCode())
                .one();
        if (ObjectUtil.isEmpty(mnjxAirline)) {
            log.error(StrUtils.format("没有查询到与{}相关的航空公司", airlineCode));
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        //查询相应的机型数据
        List<ExDto> exDtos = exMapper.retrievePlaneModel(mnjxAirline.getAirlineId(), planeModelType, Constant.IS_USE);
        if (CollUtil.isEmpty(exDtos)) {
            log.error(StrUtils.format("没有查询到与{}相关的机型数据", planeModelType));
            throw new UnifiedResultException(Constant.NIL);
        }
        //过滤重复数据，多个字段组合去重
        List<ExDto> distinctExDto = exDtos.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(exDto -> StrUtil.format("{}{}", exDto.getPlaneModelType(), exDto.getPlaneModelVersion())))), ArrayList::new))
                .stream().sorted(Comparator.comparing(ExDto::getPlaneModelType)).collect(Collectors.toList());

        //字段处理
        distinctExDto.forEach(exDto -> {
            exDto.setPreLayout(exDto.getLayout());
            //时间转换成航信日期2020-02-01 转换为 01FEB20，补全截止日期，这里截止日期目前需求是直接写死2099-12-31
            exDto.setAvailableDate(DateUtils.ymd2Com(DateUtils.ymdhms2YmdStr(exDto.getAvailableDate())));
            exDto.setUnavailableDate(DateUtils.ymd2Com("2099-12-31"));
            //处理布局中的/,字符串长度不够进行补全
            String tmp = StrUtils.isEmpty(exDto.getLayout()) ? StrUtils.EMPTY : exDto.getLayout().replace(StrUtils.SLASH, StrUtils.EMPTY);
            exDto.setLayout(StrUtils.fillAfter(tmp, CharUtil.SPACE, 15));
        });
        return distinctExDto;
    }
}
package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.obj.dto.*;
import com.swcares.eterm.dcs.cki.service.IBcService;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/***
 * 获取旅客的详细信息
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BcServiceImpl implements IBcService {

    /**
     * 白屏发送指令格式
     * BC:CA1234/03AUG22NCTU,BN002,R;BN003,R
     * 默认航班格式
     * BC:*,BN001,R;BN002,R
     */
    private static final Pattern ANGEL_PARAM_PATTERN = Pattern.compile("(((\\w{5,6})/(\\d{2}[A-Z]{3}\\d{2})([A-Z])([A-Z]{3}))|([*])),((BN\\d+,R)(;BN\\d+,R)*)");

    /**
     * BC:1,R
     * BC:1-3,R
     * BC:1PD,R
     * BC:1PD-3PD,R
     * BC:1-3PD,R
     */
    private static final Pattern PSG_NUMBER_PATTERN1 = Pattern.compile("(\\d+)(PD)?(-(\\d+)(PD)?)?,R");

    /**
     * BC:1,R
     * BC:1,R;3,R
     * BC:1PD,R
     * BC:1PD,R;3PD,R
     */
    private static final Pattern PSG_NUMBER_PATTERN2 = Pattern.compile("(\\d+)(PD)?,R(;\\d+(PD)?,R)*");

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Resource
    private IMnjxPrintDataService iMnjxPrintDataService;

    /**
     * BC指令的service方法
     *
     * @param bcDtoList 指令参数
     * @return 要打印的对象
     */
    @Override
    public List<PsgInfoDto> dealCmd(List<BcDto> bcDtoList) throws UnifiedResultException {
        List<PsgInfoDto> psgInfoList = new ArrayList<>();
        BcDto bcDto = bcDtoList.get(0);
        String type = bcDto.getType();
        String commonDate;
        if (StrUtil.isNotEmpty(bcDto.getFlightDate())) {
            commonDate = bcDto.getFlightDate().contains("-") ? bcDto.getFlightDate() : DateUtils.com2ymd(bcDto.getFlightDate());
        } else {
            commonDate = DateUtil.today();
        }
        bcDtoList.forEach(b -> b.setFlightDate(commonDate));
        if (Constant.STR_ONE.equals(type)) {
            psgInfoList = this.constructPsgInfoByBnNumber(bcDto);
        } else if (Constant.STR_TWO.equals(type)) {
            psgInfoList = this.getPsgInfoDtosByOrders(bcDtoList);
        }
        return psgInfoList;
    }

    /**
     * 根据登机号获得旅客列表
     *
     * @param bcDto bcDto
     * @return 根据登机号获得旅客列表
     */
    @Override
    public List<PsgInfoDto> constructPsgInfoByBnNumber(BcDto bcDto) throws UnifiedResultException {
        List<PsgInfoDto> psgInfoList = new ArrayList<>();
        String[] aboardNoArr = bcDto.getAboardNoArrStr().split(";");
        for (String aboardNo : aboardNoArr) {
            this.constructPsgInfoList(psgInfoList, bcDto, aboardNo);
        }
        return psgInfoList;
    }

    /**
     * 生成婴儿的打票信息
     *
     * @param dto 成人的打票信息
     * @return 生成婴儿的打票信息
     */
    private PsgInfoDto generatePsgInf(PsgInfoDto dto, String xnCname, String xnTicketNo) {
        PsgInfoDto psgInf = new PsgInfoDto();
        BeanUtil.copyProperties(dto, psgInf, true);
        MnjxConfig config = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, Constant.CONFIG_TYPE_BOARDING_PRINT_CONFIG)
                .one();
        // 上航职登机牌，婴儿座位显示INF
        if (Constant.STR_ONE.equals(config.getAvailable())) {
            psgInf.setName(xnCname);
            psgInf.setSeat(Constant.INF);
        }
        // 温州龙湾登机牌，婴儿姓名后加INF标识
        else if (Constant.STR_TWO.equals(config.getAvailable())) {
            psgInf.setName(StrUtil.format("{} INF", xnCname));
        }
        psgInf.setTicketNo(xnTicketNo);
        return psgInf;
    }

    @Override
    public List<BcDto> parseCmd(String cmd) throws UnifiedResultException {
        String[] split = cmd.split(StrUtils.COLON);
        if (split.length != Constant.TWO) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<BcDto> bcDtoList = new ArrayList<>();
        String param = split[1];
        if (ReUtil.isMatch(ANGEL_PARAM_PATTERN, param)) {
            BcDto bcDto = new BcDto();
            bcDto.setType("1");
            // 3U1113/05MAR19FCTU,BN001,R;BN002,R
            List<String> allGroups = ReUtil.getAllGroups(ANGEL_PARAM_PATTERN, param);
            String headParam = allGroups.get(1);
            String flightNo;
            String flightDate;
            String sellCabin;
            String city;
            // 默认航班
            if ("*".equals(headParam)) {
                MemoryData memoryData = MemoryDataUtils.getMemoryData();
                MemoryDataFt memoryDataFt = memoryData.getMemoryDataFt();
                if (StrUtil.isEmpty(memoryDataFt.getFlightDate()) && StrUtil.isEmpty(memoryDataFt.getFlightNo())) {
                    throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
                }
                flightNo = memoryDataFt.getFlightNo();
                flightDate = memoryDataFt.getFlightDate();
                sellCabin = "*";
                city = memoryData.getMnjxOffice().getOfficeNo().substring(0, 3);
            } else {
                flightNo = allGroups.get(3);
                flightDate = allGroups.get(4);
                sellCabin = allGroups.get(5);
                city = allGroups.get(6);
            }
            boolean isRepeat = allGroups.get(8).contains(",R");
            String aboardNoArrStr = allGroups.get(8).replace(",R", "");
            bcDto.setFlightNo(flightNo);
            bcDto.setFlightDate(flightDate);
            bcDto.setSellCabin(sellCabin);
            bcDto.setOrg(city);
            bcDto.setAboardNoArrStr(aboardNoArrStr);
            bcDto.setRepeat(isRepeat);
            bcDtoList.add(bcDto);
        } else if (ReUtil.isMatch(PSG_NUMBER_PATTERN1, param)) {
            List<String> allGroups = ReUtil.getAllGroups(PSG_NUMBER_PATTERN1, param);
            int start = Integer.parseInt(allGroups.get(1));
            int end = StrUtil.isNotEmpty(allGroups.get(4)) ? Integer.parseInt(allGroups.get(4)) : start;
            // PD参数判断
            String startPd = allGroups.get(2);
            boolean fromPd = StrUtil.isNotEmpty(startPd);
            if (StrUtil.isNotEmpty(allGroups.get(4))) {
                String endPd = allGroups.get(5);
                // 开始有PD，结束必须有PD；开始没有PD，结束可以有PD
                if (StrUtil.isNotEmpty(startPd) && StrUtil.isEmpty(endPd)) {
                    throw new UnifiedResultException(Constant.FORMAT);
                }
                fromPd = StrUtil.isNotEmpty(endPd);
            }
            for (int i = start; i <= end; i++) {
                BcDto bcDto = new BcDto();
                this.constructParam(i, bcDto, fromPd);
                bcDto.setRepeat(true);
                bcDto.setType(Constant.STR_TWO);
                bcDtoList.add(bcDto);
            }
        } else if (ReUtil.isMatch(PSG_NUMBER_PATTERN2, param)) {
            // PD参数必须所有旅客都有或都没有
            int pdCount = StrUtil.count(param, "PD");
            int psgNum = param.split(";").length;
            if (pdCount != psgNum) {
                throw new UnifiedResultException(Constant.FORMAT);
            }
            boolean fromPd = pdCount > 0;
            String psgNumStr = param.replace("PD", "").replace(",R", "");
            String[] psgNumStrSplit = psgNumStr.split(";");
            for (String s : psgNumStrSplit) {
                BcDto bcDto = new BcDto();
                int psgIndex = Integer.parseInt(s);
                this.constructParam(psgIndex, bcDto, fromPd);
                bcDto.setRepeat(true);
                bcDto.setType(Constant.STR_TWO);
                bcDtoList.add(bcDto);
            }
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return bcDtoList;
    }

    @Override
    public String retrievePrintJsonData(PsgInfoDto psgInfoDto) {
        // 构建打印对象
        JSONObject printJsonObj = JSONUtil.createObj();
        // 中文姓名
        printJsonObj.set("nameZh", psgInfoDto.getName());
        // 航班号
        String fltNo = psgInfoDto.getFltNo();
        printJsonObj.set("flightNo", fltNo);
        // 出发城市3字码
        printJsonObj.set("from", psgInfoDto.getOffAirPort());
        // 出发城市中文
        printJsonObj.set("fromZh", psgInfoDto.getOffCityCname());
        // 到达城市3字码
        printJsonObj.set("to", psgInfoDto.getArrAirPort());
        // 到达城市中文
        printJsonObj.set("toZh", psgInfoDto.getArrCityCname());
        // 航班日期
        printJsonObj.set("flightDate", psgInfoDto.getFltDate());
        // 登机序号
        String bcNo = psgInfoDto.getBcNo();
        printJsonObj.set("boardingNo", bcNo);
        // 舱位
        printJsonObj.set("cabin", psgInfoDto.getCabin());
        // 常旅客卡号
        printJsonObj.set("frequentNo", "");
        // 登机口
        printJsonObj.set("gate", StrUtils.emptyIfNull(psgInfoDto.getGate()));
        //  登机时间
        printJsonObj.set("boardingTime", StrUtils.emptyIfNull(psgInfoDto.getBoardingTime()));
        //  座位号
        printJsonObj.set("seatNo", psgInfoDto.getSeat());
        // 票号
        printJsonObj.set("ticketNo", StrUtil.isNotEmpty(psgInfoDto.getTicketNo()) ? psgInfoDto.getTicketNo() : "");
        // 票前缀
        printJsonObj.set("ticketPre", StrUtil.isNotEmpty(psgInfoDto.getTicketNo()) ? "ETKT" : "");
        // 条形码
        printJsonObj.set("barcode", StrUtil.format("{}{}{}", bcNo, fltNo, psgInfoDto.getFltDate()));
        return printJsonObj.toString();
    }

    @Override
    public void printJsonData(String printJsonData) {
        JSONObject json = JSONUtil.parseObj(printJsonData);
        String flightNo = json.get("flightNo").toString();
        String seat = json.get("seatNo").toString();
        String flightDate = json.get("flightDate").toString();
        String boardingNo = StrUtil.format("BN{}", json.get("boardingNo").toString());
        String printKey = StrUtil.format("{}/{}/{}", flightNo, flightDate, boardingNo);
        if (Constant.INF.equals(seat)) {
            printKey = StrUtil.format("{}/{}", printKey, seat);
        }
        List<MnjxPrintData> printDataList = iMnjxPrintDataService.lambdaQuery()
                .eq(MnjxPrintData::getPrintKey, printKey)
                .eq(MnjxPrintData::getPrintType, Constant.STR_ZERO)
                .list();
        if (CollectionUtil.isEmpty(printDataList)) {
            //新增数据
            MnjxPrintData mnjxPrintData = new MnjxPrintData();
            mnjxPrintData.setPrintDataId(IdUtil.getSnowflake().nextIdStr());
            mnjxPrintData.setAllowPrint(Constant.STR_ONE);
            mnjxPrintData.setPrintKey(printKey);
            mnjxPrintData.setPrintValue(printJsonData);
            mnjxPrintData.setCreateTime(new Date());
            mnjxPrintData.setPrintType(Constant.STR_ZERO);
            iMnjxPrintDataService.save(mnjxPrintData);
        } else {
            //更新
            //判断json对象数据是否一致
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                JsonNode printData = objectMapper.readTree(printJsonData);
                for (MnjxPrintData mnjxPrintData : printDataList) {
                    JsonNode printDataDb = objectMapper.readTree(mnjxPrintData.getPrintValue());
                    if (printDataDb.equals(printData)) {
                        mnjxPrintData.setAllowPrint(Constant.STR_ONE);
                        iMnjxPrintDataService.updateById(mnjxPrintData);
                        return;
                    } else {
                        mnjxPrintData.setPrintValue(printJsonData);
                        mnjxPrintData.setAllowPrint(Constant.STR_ONE);
                        mnjxPrintData.setCreateTime(new Date());
                        iMnjxPrintDataService.updateById(mnjxPrintData);
                    }
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public String dealItBussiness(String cmd) throws UnifiedResultException {
        List<BcDto> bcDtoList = this.parseCmd(cmd);
        List<PsgInfoDto> psgInfoList = this.dealCmd(bcDtoList);
        //组装打印数据
        for (PsgInfoDto psgInfoDto : psgInfoList) {
            String printJsonData = this.retrievePrintJsonData(psgInfoDto);
            // 打印登机牌
            this.printJsonData(printJsonData);
        }
        return "BOARDING PASS ISSUED";
    }

    private void constructParam(int index, BcDto bcDto, boolean fromPd) throws UnifiedResultException {
        String flightNo;
        String flightDate;
        String offCity;
        String sellCabin;
        String ckiStatus;
        String pnrNmId;
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataHandlerResult memoryDataHandlerResult = memoryData.getMemoryDataHandlerResult();
        String lastAction = memoryDataHandlerResult.getLastAction();
        if (fromPd) {
            lastAction = "PD";
        }
        switch (lastAction) {
            case "PA":
                List<PaResultDto> cachePaResultDtoList = iCkiCacheService.getPaCache(memoryData);
                if (CollUtil.isEmpty(cachePaResultDtoList)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                if (index > cachePaResultDtoList.size()) {
                    throw new UnifiedResultException(Constant.NUMBER_ERROR);
                }
                PaResultDto paResultDto = cachePaResultDtoList.get(index - 1);
                flightNo = paResultDto.getFlightNo();
                flightDate = paResultDto.getFlightDate();
                offCity = paResultDto.getOrgAirport();
                sellCabin = paResultDto.getSellCabin();
                ckiStatus = paResultDto.getMnjxPsgCki().getCkiStatus();
                pnrNmId = paResultDto.getPnrNmId();
                break;
            case "RL":
            case "RN":
            case "PD":
                PdInfoDto pdInfoDto = iCkiCacheService.getPdDefaultInfo(memoryData);
                if (ObjectUtil.isEmpty(pdInfoDto)) {
                    if (fromPd) {
                        throw new UnifiedResultException(Constant.NO_DISPLAY);
                    } else {
                        throw new UnifiedResultException(Constant.ENTRY_NBR);
                    }
                }
                List<PdNmDto> nms = pdInfoDto.getNms();
                if (index > nms.size()) {
                    if (fromPd) {
                        throw new UnifiedResultException(Constant.ENTRY_NBR);
                    } else {
                        throw new UnifiedResultException(Constant.NUMBER_ERROR);
                    }
                }
                PdNmDto pdNmDto = nms.get(index - 1);
                flightNo = StrUtil.isEmpty(pdNmDto.getCarrierFlight()) ? pdInfoDto.getFlightNo() : pdNmDto.getCarrierFlight();
                flightDate = pdInfoDto.getDate();
                offCity = pdNmDto.getOrg();
                sellCabin = pdNmDto.getSellCabin();
                ckiStatus = pdNmDto.getCkiStatus();
                pnrNmId = pdNmDto.getPnrNmId();
                break;
            case "FSN":
            case "FB":
            case "PR":
                PdInfoDto prInfoDto = iCkiCacheService.getPrCache(memoryData);
                if (ObjectUtil.isEmpty(prInfoDto)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                List<PdNmDto> prNmDtos = prInfoDto.getNms();
                if (index > prNmDtos.size()) {
                    throw new UnifiedResultException(Constant.NUMBER_ERROR);
                }
                PdNmDto prNmDto = prNmDtos.get(index - 1);
                flightNo = StrUtil.isEmpty(prNmDto.getCarrierFlight()) ? prInfoDto.getFlightNo() : prNmDto.getCarrierFlight();
                flightDate = prInfoDto.getDate();
                offCity = prNmDto.getOrg();
                sellCabin = prNmDto.getSellCabin();
                ckiStatus = prNmDto.getCkiStatus();
                pnrNmId = prNmDto.getPnrNmId();
                break;
            case "PU":
                List<PaResultDto> cachePuResultDtoList = iCkiCacheService.getPuCache(memoryData);
                if (CollUtil.isEmpty(cachePuResultDtoList)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                if (index > cachePuResultDtoList.size()) {
                    throw new UnifiedResultException(Constant.NUMBER_ERROR);
                }
                PaResultDto cachePuResultDto = cachePuResultDtoList.get(index - 1);
                flightNo = cachePuResultDto.getFlightNo();
                flightDate = cachePuResultDto.getFlightDate();
                offCity = cachePuResultDto.getOrgAirport();
                sellCabin = cachePuResultDto.getSellCabin();
                ckiStatus = cachePuResultDto.getMnjxPsgCki().getCkiStatus();
                pnrNmId = cachePuResultDto.getPnrNmId();
                break;
            case "PW":
                List<PwResultDto> pwResultDtoList = iCkiCacheService.getPwCache(memoryData);
                if (CollUtil.isEmpty(pwResultDtoList)) {
                    throw new UnifiedResultException(Constant.ENTRY_NBR);
                }
                if (index > pwResultDtoList.size()) {
                    throw new UnifiedResultException(Constant.NUMBER_ERROR);
                }
                PwResultDto pwResultDto = pwResultDtoList.get(index - 1);
                flightNo = pwResultDto.getFltNo();
                flightDate = pwResultDto.getFltDate();
                offCity = pwResultDto.getOrg();
                sellCabin = pwResultDto.getSellCabin();
                ckiStatus = pwResultDto.getMnjxPsgCki().getCkiStatus();
                pnrNmId = pwResultDto.getPnrNmId();
                break;
            default:
                PaResultDto hbpuResultDto = null;
                List<PaResultDto> hbpuResultDtoList = iCkiCacheService.getPuCache(memoryData);
                if (CollUtil.isEmpty(hbpuResultDtoList)) {
                    hbpuResultDtoList = iCkiCacheService.getHbpuCache(memoryData);
                    if (CollUtil.isEmpty(hbpuResultDtoList)) {
                        List<PaResultDto> hbpaResultDtoList = iCkiCacheService.getHbpaCache(memoryData);
                        if (CollUtil.isNotEmpty(hbpaResultDtoList)) {
                            if (index > hbpaResultDtoList.size()) {
                                throw new UnifiedResultException(Constant.NUMBER_ERROR);
                            }
                            hbpuResultDto = hbpaResultDtoList.get(index - 1);
                        }
                    } else {
                        if (index > hbpuResultDtoList.size()) {
                            throw new UnifiedResultException(Constant.NUMBER_ERROR);
                        }
                        hbpuResultDto = hbpuResultDtoList.get(index - 1);
                    }
                } else {
                    if (index > hbpuResultDtoList.size()) {
                        throw new UnifiedResultException(Constant.NUMBER_ERROR);
                    }
                    hbpuResultDto = hbpuResultDtoList.get(index - 1);
                }
                if (ObjectUtil.isEmpty(hbpuResultDto)) {
                    List<PwResultDto> hbpwResultDtoList = iCkiCacheService.getHbpwCache(memoryData);
                    if (CollUtil.isEmpty(hbpwResultDtoList)) {
                        PdInfoDto hbprInfoDto = iCkiCacheService.getHbprCache(memoryData);
                        if (ObjectUtil.isNotEmpty(hbprInfoDto)) {
                            List<PdNmDto> hbprNmDtos = hbprInfoDto.getNms();
                            if (index > hbprNmDtos.size()) {
                                throw new UnifiedResultException(Constant.NUMBER_ERROR);
                            }
                            PdNmDto hbprNmDto = hbprNmDtos.get(index - 1);
                            flightNo = StrUtil.isEmpty(hbprNmDto.getCarrierFlight()) ? hbprInfoDto.getFlightNo() : hbprNmDto.getCarrierFlight();
                            flightDate = hbprInfoDto.getDate();
                            offCity = hbprNmDto.getOrg();
                            sellCabin = hbprNmDto.getSellCabin();
                            ckiStatus = hbprNmDto.getCkiStatus();
                            pnrNmId = hbprNmDto.getPnrNmId();
                        } else {
                            throw new UnifiedResultException(Constant.ENTRY_NBR);
                        }
                    } else {
                        if (index > hbpwResultDtoList.size()) {
                            throw new UnifiedResultException(Constant.NUMBER_ERROR);
                        }
                        PwResultDto hbpwResultDto = hbpwResultDtoList.get(index - 1);
                        flightNo = hbpwResultDto.getFltNo();
                        flightDate = hbpwResultDto.getFltDate();
                        offCity = hbpwResultDto.getOrg();
                        sellCabin = hbpwResultDto.getSellCabin();
                        ckiStatus = hbpwResultDto.getMnjxPsgCki().getCkiStatus();
                        pnrNmId = hbpwResultDto.getPnrNmId();
                    }
                } else {
                    flightNo = hbpuResultDto.getFlightNo();
                    flightDate = hbpuResultDto.getFlightDate();
                    offCity = hbpuResultDto.getOrgAirport();
                    sellCabin = hbpuResultDto.getSellCabin();
                    ckiStatus = hbpuResultDto.getMnjxPsgCki().getCkiStatus();
                    pnrNmId = hbpuResultDto.getPnrNmId();
                }
        }
        if (StrUtil.equalsAny(ckiStatus, Constant.NACC, Constant.SB)) {
            throw new UnifiedResultException(Constant.NO_PREVIOUS_BOARDING_PASS_ISSUED);
        } else if (Constant.DL.equals(ckiStatus)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        bcDto.setFlightNo(flightNo);
        bcDto.setFlightDate(flightDate);
        bcDto.setSellCabin(sellCabin);
        bcDto.setOrg(offCity);
        bcDto.setPnrNmId(pnrNmId);
    }

    /**
     * 根据序号，获取序号所对应的对象
     *
     * @param bcDtoList
     * @return 根据序号，获取序号所对应的对象
     */
    private List<PsgInfoDto> getPsgInfoDtosByOrders(List<BcDto> bcDtoList) throws UnifiedResultException {
        List<PsgInfoDto> psgInfoList = new ArrayList<>();
        for (BcDto bcDto : bcDtoList) {
            this.constructPsgInfoList(psgInfoList, bcDto, null);
        }
        return psgInfoList;
    }

    /**
     * Title: constructPsgInfoList
     * Description: 构建数据列表
     *
     * @param psgInfoList psgInfoList
     * @param bcDto       bcDto
     * @param aboardNo    aboardNo
     * <AUTHOR>
     * @date 2022/9/10 11:40
     */
    private void constructPsgInfoList(List<PsgInfoDto> psgInfoList, BcDto bcDto, String aboardNo) throws UnifiedResultException {
        String flightNo = bcDto.getFlightNo();
        String flightDate = bcDto.getFlightDate();
        String org = bcDto.getOrg();
        MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, org)
                .one();
        if (ObjectUtil.isEmpty(orgAirport)) {
            throw new UnifiedResultException(Constant.AIRPORT);
        }
        MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, flightNo)
                .one();
        if (ObjectUtil.isEmpty(flight)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        if (StrUtil.isNotEmpty(flight.getCarrierFlight())) {
            if (Constant.STR_ZERO.equals(flight.getShareState())) {
                throw new UnifiedResultException(Constant.FLT_NBR);
            }
            flight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, flight.getCarrierFlight())
                    .one();
        }
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, flight.getFlightId())
                .one();
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                .eq(MnjxPlanFlight::getFlightDate, flightDate)
                .one();
        if (ObjectUtil.isEmpty(planFlight)) {
            throw new UnifiedResultException(Constant.FLT_DATE);
        }
        MnjxPlanSection orgPlanSection = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .eq(MnjxPlanSection::getDepAptId, orgAirport.getAirportId())
                .one();
        if (ObjectUtil.isEmpty(orgPlanSection)) {
            throw new UnifiedResultException(Constant.AIRPORT);
        }

        String nmId = "";
        MnjxPsgCki psgCki = new MnjxPsgCki();
        MnjxPnrNm pnrNm = new MnjxPnrNm();
        MnjxPnrSeg pnrSeg = new MnjxPnrSeg();
        List<MnjxPsgCki> psgCkiList;
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getFlightNo, flightNo)
                .eq(MnjxPnrSeg::getFlightDate, flightDate)
                .eq(MnjxPnrSeg::getOrg, org)
                .list();
        // 通过登机号打印登机牌
        if (StrUtil.isNotEmpty(aboardNo)) {
            psgCkiList = iMnjxPsgCkiService.lambdaQuery()
                    .eq(MnjxPsgCki::getCkiStatus, Constant.CKI_STATUS_ACC)
                    .eq(MnjxPsgCki::getAboardNo, aboardNo.replace("BN", ""))
                    .list();
        } else {
            // 通过PD列表打印
            psgCkiList = iMnjxPsgCkiService.lambdaQuery()
                    .eq(MnjxPsgCki::getCkiStatus, Constant.CKI_STATUS_ACC)
                    .eq(MnjxPsgCki::getPnrNmId, bcDto.getPnrNmId())
                    .list();
        }
        if (CollUtil.isEmpty(psgCkiList)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        f1:
        for (MnjxPsgCki mnjxPsgCki : psgCkiList) {
            pnrNm = iMnjxPnrNmService.getById(mnjxPsgCki.getPnrNmId());
            for (MnjxPnrSeg mnjxPnrSeg : pnrSegList) {
                if (mnjxPnrSeg.getPnrId().equals(pnrNm.getPnrId()) && mnjxPnrSeg.getPnrSegNo() == Integer.parseInt(mnjxPsgCki.getPnrSegNo())) {
                    nmId = pnrNm.getPnrNmId();
                    psgCki = mnjxPsgCki;
                    pnrSeg = mnjxPnrSeg;
                    break f1;
                }
            }
        }
        if (StrUtil.isEmpty(nmId)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        MnjxPsgSeat psgSeat = iMnjxPsgSeatService.lambdaQuery()
                .eq(MnjxPsgSeat::getPsgCkiId, psgCki.getPsgCkiId())
                .one();
        MnjxPnrNmTn nmTn = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, nmId)
                .one();
        String pnrSegId = pnrSeg.getPnrSegId();
        MnjxPnrNmTicket nmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getPnrNmTnId, nmTn.getTnId())
                .and(nt -> nt.eq(MnjxPnrNmTicket::getS1Id, pnrSegId).or().eq(MnjxPnrNmTicket::getS2Id, pnrSegId))
                .one();

        String name = pnrNm.getName();
        String arrAirPort = pnrSeg.getDst();
        String gate = StrUtil.isNotEmpty(orgPlanSection.getGate()) ? orgPlanSection.getGate() : "25";
        String seatNo = psgSeat.getPsgSeat();
        String ticketNo = nmTicket.getTicketNo();
        String bcNo = psgCki.getAboardNo();
        MnjxCity orgCity = iMnjxCityService.getById(orgAirport.getCityId());
        MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, arrAirPort)
                .one();
        MnjxCity dstCity = iMnjxCityService.getById(dstAirport.getCityId());
        String offCityCname = orgCity.getCityCname();
        String arrCityCname = dstCity.getCityCname();
        String boardingTime = StrUtil.isEmpty(psgCki.getAboardTime()) ? orgPlanSection.getEstimateBoarding() : psgCki.getAboardTime();

        // 成人数据
        PsgInfoDto dto = new PsgInfoDto();
        dto.setName(name);
        dto.setFltNo(flight.getFlightNo());
        dto.setFltDate(DateUtils.ymd2Com(flightDate));
        dto.setCabin(psgCki.getSellCabin());
        dto.setCabinClass(psgCki.getCabinClass());
        dto.setOffAirPort(org);
        dto.setArrAirPort(arrAirPort);
        dto.setGate(gate);
        dto.setBoardingTime(boardingTime);
        dto.setSeat(seatNo);
        dto.setTicketNo(ticketNo);
        dto.setBcNo(bcNo);
        dto.setOffCityCname(offCityCname);
        dto.setArrCityCname(arrCityCname);
        psgInfoList.add(dto);

        MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, nmId)
                .one();
        // 说明成人带了婴儿
        if (ObjectUtil.isNotEmpty(nmXn)) {
            String xnCname = nmXn.getXnCname();
            MnjxPnrNmTn xnTn = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId())
                    .one();
            MnjxPnrNmTicket xnTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, xnTn.getTnId())
                    .and(nt -> nt.eq(MnjxPnrNmTicket::getS1Id, pnrSegId).or().eq(MnjxPnrNmTicket::getS2Id, pnrSegId))
                    .one();
            String xnTicketNo = xnTicket.getTicketNo();
            PsgInfoDto psgInf = this.generatePsgInf(dto, xnCname, xnTicketNo);
            psgInfoList.add(psgInf);
        }
    }
}

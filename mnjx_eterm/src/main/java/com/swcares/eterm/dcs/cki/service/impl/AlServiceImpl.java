package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.ReUtils;
import com.swcares.entity.MnjxSeat;
import com.swcares.eterm.dcs.cki.obj.dto.AlDto;
import com.swcares.eterm.dcs.cki.mapper.AlMapper;
import com.swcares.eterm.dcs.cki.service.IAlService;
import com.swcares.eterm.dcs.cki.service.ISeService;
import com.swcares.service.IMnjxSeatService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service
public class AlServiceImpl implements IAlService {

    @Resource
    private AlMapper alMapper;

    @Resource
    private IMnjxSeatService iMnjxSeatService;

    @Resource
    private ISeService iSeService;

    /***
     * AL:座位号
     */
    private static final String AL_PATTERN = "^AL:(-)?(\\d{1,3}[A-Z])$";

    /**
     * SE：航班号/日期/舱位/航段
     */
    private static final String SE_PATTERN = "SE:([0-9A-Za-z]{5,7})/([0-9]{1,2}[A-Z]{3}([0-9]{2})?|[+-.])/([A-Z|*])([A-Z]{6})?";

    /**
     * AL锁定不报错，也不产生任何作用的座位状态
     */
    private static final String STATUS_PATTERN = "[X|.|P|D|V|O]";

    @Override
    public AlDto pares(String cmd) throws UnifiedResultException {
        MemoryDataHandlerResult memoryDataHandlerResult = MemoryDataUtils.getMemoryData().getMemoryDataHandlerResult();
        // 判断上一个指令是否为SE
        String lastAction = memoryDataHandlerResult.getLastAction();
        if (!Constant.SE.equals(lastAction)) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        // 从SE指令获取航班号、日期和航段
        String seReCmd = memoryDataHandlerResult.getRecords().get(0);
        List<String> seGroups = ReUtil.getAllGroups(Pattern.compile(SE_PATTERN), seReCmd);
        AlDto alDto = new AlDto();
        // se指令
        alDto.setSeCmd(seGroups.get(0));
        // 航班号
        alDto.setFlightNo(seGroups.get(1));
        // 航班日期
        alDto.setFlightDate(DateUtils.com2ymd(seGroups.get(2)));
        // 航段
        alDto.setDep(seGroups.get(5).substring(0, 3));
        alDto.setArr(seGroups.get(5).substring(3, 6));
        // 指令格式验证
        if (!ReUtils.isMatch(AL_PATTERN, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<String> allGroups = ReUtils.getAllGroups(Pattern.compile(AL_PATTERN), cmd);
        // 释放标识
        alDto.setDash(allGroups.get(1));
        // 座位号
        alDto.setSeatNo(allGroups.get(2));
        return alDto;
    }

    @Override
    public String handler(AlDto alDto) throws UnifiedResultException {
        // 通过航班，日期，航段，座位号查询对应座位实体
        List<MnjxSeat> mnjxSeats = alMapper.retrieveSeatStatus(alDto);
        // 验证座位号是否存在
        if (CollUtil.isEmpty(mnjxSeats)){
            throw new UnifiedResultException(Constant.NO_ACTION_INDICATED);
        }
        for (MnjxSeat mnjxSeat:mnjxSeats){
            // 锁定座位
            if (StrUtil.isEmpty(alDto.getDash())){
                // AL锁定X/./P/D/V/O状态座位不报错，但不产生任何作用，座位符号不变。
                if (!ReUtils.isMatch(STATUS_PATTERN, mnjxSeat.getSeatStatus())){
                    // 保存锁定前的座位状态
                    mnjxSeat.setSeatStatusOld(mnjxSeat.getSeatStatus());
                    mnjxSeat.setSeatStatus(Constant.SEAT_STATUS_A);
                }
            }else if (StrUtil.DASHED.equals(alDto.getDash())){
                // 释放座位
                if (Constant.SEAT_STATUS_A.equals(mnjxSeat.getSeatStatus())){
                    // 将座位恢复为原座位
                    mnjxSeat.setSeatStatus(mnjxSeat.getSeatStatusOld());
                    mnjxSeat.setSeatStatusOld(Constant.SEAT_STATUS_A);
                }else {
                    throw new UnifiedResultException(Constant.UNABLE);
                }
            }
        }
        iMnjxSeatService.updateBatchById(mnjxSeats);
        return iSeService.handler(alDto.getSeCmd());
    }
}

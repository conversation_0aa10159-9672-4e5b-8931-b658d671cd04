package com.swcares.eterm.dcs.fdc.service;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.fdc.dto.CndDto;

import java.util.List;

/**
 * <AUTHOR>
 *  2022-04-27 15:52:43
 */
public interface ICndService {

    /**
     * 获取CND数据
     *
     * @param unifiedResult 参数
     * @return 机型数据
     * @throws UnifiedResultException 统一异常处理
     */
    List<CndDto> handle(UnifiedResult unifiedResult) throws UnifiedResultException;
}

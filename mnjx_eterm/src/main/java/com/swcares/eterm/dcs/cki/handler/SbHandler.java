package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.SbDto;
import com.swcares.eterm.dcs.cki.obj.dto.SbInfoDto;
import com.swcares.eterm.dcs.cki.service.ISbService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * SB指令处理:
 *
 * <AUTHOR>
 */
@OperateType(action = "SB", shorthand = true, template = "/dcs/cki/sb.jf")
public class SbHandler implements Handler {

    @Resource
    private ISbService iSbService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        //参数解析
        SbDto sbDto = iSbService.parse(cmd);
        List<SbInfoDto> resultList = new ArrayList<>();
        resultList.add(iSbService.handlerSb(MemoryDataUtils.getMemoryData(), sbDto));
        unifiedResult.setResults(resultList.toArray());
        return unifiedResult;
    }
}

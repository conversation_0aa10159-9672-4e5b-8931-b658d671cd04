package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.GsParamDto;
import com.swcares.eterm.dcs.cki.service.IGsService;

import javax.annotation.Resource;

/**
 * ID,GS,HL指令操作 ID:航班号 登机城市 舱位等级 PAD限额（+:增加,-:减少） GS:航班号 登机城市 舱位等级
 * GS限额数（+:增加,-:减少） ID:航班号 航段 舱位等级 人数（+:增加,-:减少）
 *
 * <AUTHOR>
 */
@OperateType(action = "GS")
public class GsHandler implements Handler {
    @Resource
    private IGsService iGsService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        GsParamDto gsParamDto= iGsService.parseGs(cmd);
        return iGsService.handle(gsParamDto);
    }
}

package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.mapper.PdMapper;
import com.swcares.eterm.dcs.cki.mapper.PrMapper;
import com.swcares.eterm.dcs.cki.obj.dto.*;
import com.swcares.eterm.dcs.cki.service.IAspectCkiService;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.eterm.dcs.cki.service.IPrService;
import com.swcares.service.*;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 查询指令航班的旅客详细信息
 *
 * <AUTHOR>
 */
@CacheConfig(cacheNames = CacheKeyConstant.CACHE_ETERM_KEY)
@Service
public class PrServiceImpl implements IPrService {

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private PrMapper prMapper;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;
    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxPsgOperateRecordService iMnjxPsgOperateRecordService;

    @Resource
    private IMnjxPnrCtService iMnjxPnrCtService;

    @Resource
    private IMnjxLuggageService iMnjxLuggageService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private IMnjxPsgCkiOptionService iMnjxPsgCkiOptionService;

    @Resource
    private IMnjxExLuggageService iMnjxExLuggageService;

    @Resource
    private PdMapper pdMapper;

    @Resource
    private IMnjxCndService iMnjxCndService;


    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;

    @Resource
    private IMnjxPlanSectionService mnjxPlanSectionService;

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Resource
    private IMnjxFrequenterService iMnjxFrequenterService;

    @Resource
    private IAspectCkiService iAspectCkiService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Resource
    private IMnjxPrintDataService iMnjxPrintDataService;

    /**
     * PR:旅客序号 PR旅客序号
     * PR:1 PR1 PR 1
     */
    private static final String REG_PR = "PR[:|\\s]?(\\d{1,3}(PD)?)";


    /**
     * PR:航班号/日期/舱位/始发站,旅客标识
     * PR:3U2001/10JUL22/Y/CTU,1ZHANG
     */
    private static final String REG_PR2 = "PR[:|\\s]\\s?([0-9A-Za-z]{5,7})/([+-.]|([0-9]{2}[A-Za-z]{3}([0-9]{2})?))/?(.)/?([A-Za-z]{3})?,([\\w/]+)(,HBNB)?";
    /**
     * PR：BT行李编号
     * PR:BT2302393223
     */
    private static final String REG_BT = "PR[:|\\s]BT(\\d+)";

    /**
     * PR:SB+No
     * PR:SB1029
     */
    private static final String REG_SB = "PR[:|\\s]SB(\\d+)";

    /**
     * PR:*,旅客标识
     * PR:*,1A
     */
    private static final String REG_PR3 = "PR[:|\\s][*],([\\w/]+)(,HBNB)?";

    /**
     * 可为任意单字母或 * 号
     */
    private static final String REG_CABIN = "([A-Za-z*])";

    /**
     * 1+旅客姓名
     */
    private static final Pattern PSG_NM = Pattern.compile("1([A-Za-z/]{1,20})");

    /**
     * BN+登机号
     */
    private static final Pattern PSG_BN = Pattern.compile("BN(\\d{1,3})");

    /**
     * SN+座位号
     */
    private static final Pattern PSG_SN = Pattern.compile("SN([A-Za-z0-9]{1,5})");

    /**
     * RL+订座记录编号
     */
    private static final Pattern PSG_RL = Pattern.compile("RL([A-Za-z0-9]{6})");

    /**
     * BT行李
     */
    private static final Pattern PSG_BT = Pattern.compile("BT(\\d+)");

    /**
     * SB+候补号
     */
    private static final Pattern PSG_SB = Pattern.compile("SB(\\d+)");

    /**
     * ET+票号
     */
    private static final Pattern PSG_ET = Pattern.compile("ET(\\d+)");

    /**
     * 数据处理
     */
    private static final Pattern REG_DATE = Pattern.compile("([+-.]|([0-9]{2}[A-Za-z]{3}([0-9]{2})?))");

    /**
     * 解析 证件信息
     * SSR FOID CA HK1 NI622323198604170516/P1
     * SSR FOID CA HK1 PP8604170516/P1
     * SSR FOID CA HK1 ID870516/P1
     */
    private static final Pattern REG_FOID = Pattern.compile("SSR\\sFOID\\s[A-Z0-9]{2}\\s\\w{3}\\s(\\w+)/\\w{2,}");

    /**
     * PSM
     */
    private static final Pattern PSM_PATTERN = Pattern.compile("^SSR\\sPSM\\s(.+)/P\\d+$");

    /**
     * SPML
     */
    private static final Pattern SPML_PATTERN = Pattern.compile("SSR\\s(\\w+)\\s\\w+\\s\\w+\\s\\w+\\s\\d+\\s\\w+(\\s(\\w+))?(/\\w+)?");

    @Override
    public PrResultDto handle(MemoryData memoryData, String cmd) throws UnifiedResultException {
        // 指令格式验证
        if (ReUtil.isMatch(REG_PR, cmd) || ReUtil.isMatch(REG_PR2, cmd) || ReUtil.isMatch(REG_PR3, cmd)) {
            if (ReUtil.isMatch(REG_PR, cmd)) {
                // 获取上一次指令
                MemoryDataHandlerResult handlerResult = MemoryDataUtils.getMemoryData().getMemoryDataHandlerResult();
                if (ObjectUtil.isEmpty(handlerResult)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                // 上一次操作的指令
                String lastAction = handlerResult.getLastAction().toUpperCase();
                // 旅客序号
                // 旅客序号
                String param = ReUtil.get(Pattern.compile(REG_PR), cmd, 1);
                if (param.contains("PD")) {
                    param = param.replace("PD", "");
                    lastAction = "PD";
                }
                int psgNo = Integer.parseInt(param) - 1;
                // pd参数
                String pdParam = handlerResult.getPdParamSby();
                // 有效指令
                List<String> validCmdList = Arrays.asList("PD", "RN", "RL", "SB", "PR", "PA", "PU", "PW", "FB", "FSN", "JC");
                // 前一指令(必须是准确的前一有效指令，即使PD后PR查了一次，再PR一次，前一指令也成PR而会导致报错，但是要注意：翻页指令PN,PB,PF,PL,PG不能算有效指令）是否是PD，RN，RL，SB，PR
                if (StrUtil.isEmpty(lastAction) || !validCmdList.contains(lastAction)) {
                    throw new UnifiedResultException(Constant.NO_DISPLAY);
                }
                PdInfoDto pdInfoDto = new PdInfoDto();
                // 组装缓存的key
                List<String> cmdList = Arrays.asList("PD", "RL", "RN");
                if (cmdList.contains(lastAction.toUpperCase())) {
                    if (StrUtil.isNotEmpty(pdParam)) {
                        pdInfoDto = iCkiCacheService.getPdSbyInfo(memoryData);
                    } else {
                        pdInfoDto = iCkiCacheService.getPdDefaultInfo(memoryData);
                    }
                } else if (Constant.CMD_FB.equalsIgnoreCase(lastAction) || Constant.CMD_FSN.equals(lastAction) || Constant.CMD_PR.equals(lastAction)) {
                    pdInfoDto = iCkiCacheService.getPrCache(memoryData);
                }
                List<String> auwList = Arrays.asList(Constant.CMD_PA, Constant.CMD_PU, Constant.CMD_PW, Constant.CMD_JC);
                if (Constant.SB.equalsIgnoreCase(lastAction)) {
                    return this.getAlternateaPssenger(memoryData, param, psgNo);
                } else if (auwList.contains(lastAction)) {
                    return this.handleByAuw(memoryData, param, psgNo, lastAction);
                } else {
                    return this.handleByPr(memoryData, param, psgNo, pdInfoDto);
                }
            } else {
                return this.dealPr(memoryData, cmd);
            }
        } else if (ReUtil.isMatch(REG_BT, cmd)) {
            return this.dealPrBt(memoryData, cmd);
        } else if (ReUtil.isMatch(REG_SB, cmd)) {
            return this.dealPrSb(memoryData, cmd);
        }
        throw new UnifiedResultException(Constant.FORMAT);
    }

    /**
     * pr处理
     *
     * @param memoryData memoryData
     * @param param      param
     * @param psgNo      psgNo
     * @param pdInfoDto  pdInfoDto
     * @return pr处理
     * @throws UnifiedResultException 统一异常
     */
    private PrResultDto handleByPr(MemoryData memoryData, String param, int psgNo, PdInfoDto pdInfoDto) throws UnifiedResultException {
        PrResultDto prResult = new PrResultDto();
        if (ObjectUtil.isEmpty(pdInfoDto) || CollUtil.isEmpty(pdInfoDto.getNms())) {
            throw new UnifiedResultException(Constant.NO_DISPLAY);
        }
        List<PdNmDto> nms = pdInfoDto.getNms();
        if (psgNo < 0 || psgNo >= nms.size()) {
            throw new UnifiedResultException("ENTRY NBR");
        }
        // 航班号
        String flightNo = pdInfoDto.getFlightNo();
        // 航班日期
        String flightDate = pdInfoDto.getDate();
        if (!flightDate.contains(StrUtils.DASHED)) {
            flightDate = DateUtils.com2ymd(flightDate);
        }
        PdNmDto pdNmDto = nms.get(psgNo);
        String pnrNmId = pdNmDto.getPnrNmId();
        if (!flightDate.contains(StrUtils.DASHED)) {
            flightDate = DateUtils.com2ymd(flightDate);
        }
        // 出发城市
        String org = pdNmDto.getOrg();
        // 销售舱位
        String sellCabin = pdNmDto.getSellCabin();
        // 出发时间
        String estimateOff = pdInfoDto.getEstimateOff();
        List<PrDto> prs = prMapper.retrieveByPnrNmId(pnrNmId, flightNo, flightDate, sellCabin, estimateOff, org);
        if (CollUtil.isEmpty(prs)) {
            prs = prMapper.retrieveByPnrNmIdAndCarrierFlight(pnrNmId, flightNo, flightDate, sellCabin, estimateOff, org);
        }
        if (CollUtil.isEmpty(prs)) {
            iCkiCacheService.clearPaCache(memoryData);
            iCkiCacheService.clearPuCache(memoryData);
            iCkiCacheService.clearPwCache(memoryData);
            PdInfoDto defaultInfo = iCkiCacheService.getPdDefaultInfo(memoryData);
            if (ObjectUtil.isNotEmpty(defaultInfo)) {
                iCkiCacheService.clearPdSbyInfo(memoryData);
            }
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        assemblePsgNum(prs);
        PdInfoDto p = new PdInfoDto();
        BeanUtil.copyProperties(pdInfoDto, p);
        p.getNms().clear();
        p.getNms().add(pdNmDto);
        prResult.setPdInfoDto(p);
        prResult.setParam(param);
        prResult.getPrDtos().addAll(prs);
        return prResult;
    }

    /**
     * 处理PA、PU、PW
     *
     * @param memoryData memoryData
     * @param param      param
     * @param psgNo      psgNo
     * @param lastAction lastAction
     * @return 处理PA、PU、PW
     * @throws UnifiedResultException 统一异常
     */
    private PrResultDto handleByAuw(MemoryData memoryData, String param, int psgNo, String lastAction) throws UnifiedResultException {
        String pnrNmId = null;
        // 航班号
        String flightNo = null;
        // 航班日期
        String flightDate = null;
        // 出发城市
        String org = null;
        // 销售舱位
        String sellCabin = null;
        // 出发时间
        String estimateOff = null;
        if (StrUtil.equalsAny(lastAction, Constant.CMD_JC, Constant.CMD_PA)) {
            List<PaResultDto> paResultDtoList = iCkiCacheService.getPaCache(memoryData);
            if (CollUtil.isEmpty(paResultDtoList)) {
                throw new UnifiedResultException(Constant.NO_DISPLAY);
            }
            if (psgNo < 0 || psgNo >= paResultDtoList.size()) {
                throw new UnifiedResultException("ENTRY NBR");
            }
            PaResultDto paResultDto = paResultDtoList.get(psgNo);
            pnrNmId = paResultDto.getPnrNmId();
            flightNo = paResultDto.getFlightNo();
            flightDate = paResultDto.getFlightDate();
            org = paResultDto.getOrgAirport();
            // 销售舱位
            sellCabin = paResultDto.getSellCabin();
            // 出发时间
            estimateOff = paResultDto.getSd();
        } else if (Constant.CMD_PU.equalsIgnoreCase(lastAction)) {
            List<PaResultDto> puResultDtoList = iCkiCacheService.getPuCache(memoryData);
            if (CollUtil.isEmpty(puResultDtoList)) {
                throw new UnifiedResultException(Constant.NO_DISPLAY);
            }
            if (psgNo < 0 || psgNo >= puResultDtoList.size()) {
                throw new UnifiedResultException("ENTRY NBR");
            }
            PaResultDto cachePaResultDto = puResultDtoList.get(psgNo);
            pnrNmId = cachePaResultDto.getPnrNmId();
            // 航班号
            flightNo = cachePaResultDto.getFlightNo();
            flightDate = cachePaResultDto.getFlightDate();
            // 出发城市
            org = cachePaResultDto.getOrgAirport();
            // 销售舱位
            sellCabin = cachePaResultDto.getSellCabin();
            // 出发时间
            estimateOff = cachePaResultDto.getSd();
        } else if (Constant.CMD_PW.equalsIgnoreCase(lastAction)) {
            List<PwResultDto> pwResultDtoList = iCkiCacheService.getPwCache(memoryData);
            if (CollUtil.isEmpty(pwResultDtoList)) {
                throw new UnifiedResultException(Constant.NO_DISPLAY);
            }
            if (psgNo < 0 || psgNo >= pwResultDtoList.size()) {
                throw new UnifiedResultException("ENTRY NBR");
            }
            PwResultDto pwResultDto = pwResultDtoList.get(psgNo);
            pnrNmId = pwResultDto.getPnrNmId();
            // 航班号
            flightNo = pwResultDto.getFltNo();
            flightDate = pwResultDto.getFltDate();
            // 出发城市
            org = pwResultDto.getOrg();
            // 销售舱位
            sellCabin = pwResultDto.getSellCabin();
            // 出发时间
            estimateOff = pwResultDto.getEstimateOff();
        }
        if (!flightDate.contains(StrUtils.DASHED)) {
            flightDate = DateUtils.com2ymd(flightDate);
        }
        return getAuwBusiness(memoryData, param, pnrNmId, flightNo, flightDate, org, sellCabin, estimateOff);
    }

    /**
     * 业务处理
     *
     * @param param       param
     * @param pnrNmId     pnrNmId
     * @param flightNo    flightNo
     * @param flightDate  flightDate
     * @param org         org
     * @param sellCabin   sellCabin
     * @param estimateOff estimateOff
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    private PrResultDto getAuwBusiness(MemoryData memoryData, String param, String pnrNmId, String flightNo, String flightDate, String org, String sellCabin, String estimateOff) throws UnifiedResultException {
        PrResultDto prResultDto = new PrResultDto();
        List<PrDto> prs = prMapper.retrieveByPnrNmId(pnrNmId, flightNo, flightDate, sellCabin, estimateOff, org);
        if (CollUtil.isEmpty(prs)) {
            prs = prMapper.retrieveByPnrNmIdAndCarrierFlight(pnrNmId, flightNo, flightDate, sellCabin, estimateOff, org);
        }
        assemblePsgNum(prs);
        if (CollUtil.isEmpty(prs)) {
            // 执行PR后应该把其他的P指令缓存清除，保证每一次提取旅客没有上一个旅客的缓存影响
            iCkiCacheService.clearPaCache(memoryData);
            iCkiCacheService.clearPwCache(memoryData);
            iCkiCacheService.clearPuCache(memoryData);
            PdInfoDto pdDefaultInfo = iCkiCacheService.getPdDefaultInfo(memoryData);
            // 如果不为空清除sby的结果集
            if (ObjectUtil.isNotEmpty(pdDefaultInfo)) {
                iCkiCacheService.clearPdSbyInfo(memoryData);
            }
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        prResultDto.setParam(param);
        prResultDto.getPrDtos().addAll(prs);
        return prResultDto;
    }

    /**
     * 处理SB旅客
     *
     * @param memoryData memoryData
     * @param param      param
     * @param psgNo      psgNo
     * @return 处理SB旅客
     * @throws UnifiedResultException 统一异常
     */
    private PrResultDto getAlternateaPssenger(MemoryData memoryData, String param, int psgNo) throws UnifiedResultException {
        PrResultDto prResultDto = new PrResultDto();
        SbInfoDto sbInfoDto = iCkiCacheService.getSbCache(memoryData);
        List<SbHbPnrNmDto> nms = sbInfoDto.getNms();
        if (psgNo < 0 || psgNo >= nms.size()) {
            throw new UnifiedResultException("ENTRY NBR");
        }
        SbHbPnrNmDto sbHb = nms.get(psgNo);
        String flightNo = sbHb.getFlightNo();
        String flightDate = sbHb.getFlightDate();
        String pnrNmId = sbHb.getPnrNmId();

        if (!flightDate.contains(StrUtils.DASHED)) {
            flightDate = DateUtils.com2ymd(flightDate);
        }
        List<PrDto> prs = prMapper.retrieveByPnrNmId(pnrNmId, flightNo, flightDate, null, null, null);
        if (CollUtil.isEmpty(prs)) {
            prs = prMapper.retrieveByPnrNmIdAndCarrierFlight(pnrNmId, flightNo, flightDate, null, null, null);
        }
        if (CollUtil.isEmpty(prs)) {
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        assemblePsgNum(prs);
        prResultDto.setParam(param);
        prResultDto.getPrDtos().addAll(prs);
        return prResultDto;
    }

    @Override
    public void assemblePsgNum(List<PrDto> prDtos) {
        List<String> pnrSegList = prDtos.stream().map(PrDto::getPnrSegId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(pnrSegList)) {
            List<MnjxPnrNmTicket> mnjxPnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getS1Id, pnrSegList).list();
            mnjxPnrNmTickets.addAll(iMnjxPnrNmTicketService.lambdaQuery().eq(MnjxPnrNmTicket::getS2Id, pnrSegList).list());
            if (CollUtil.isNotEmpty(mnjxPnrNmTickets)) {
                // hbnb1
                Map<String, MnjxPnrNmTicket> s1Map = mnjxPnrNmTickets.stream()
                        .collect(Collectors.toMap(k -> StrUtil.format("{}{}", k.getS1Id(), k.getPnrNmTnId()), k -> k));
                prDtos.forEach(k -> {
                    String key = StrUtil.format("{}{}", k.getPnrSegId(), k.getTnId());
                    if (s1Map.containsKey(key)) {
                        MnjxPnrNmTicket mnjxPnrNmTicket = s1Map.get(key);
                        k.setPsgNum(mnjxPnrNmTicket.getHbnb1());
                    }
                });
                // hbnb2
                mnjxPnrNmTickets = mnjxPnrNmTickets.stream().filter(k -> StrUtil.isNotEmpty(k.getS2Id())).collect(Collectors.toList());
                Map<String, MnjxPnrNmTicket> s2Map = mnjxPnrNmTickets.stream()
                        .collect(Collectors.toMap(k -> StrUtil.format("{}{}", k.getS2Id(), k.getPnrNmTnId()), k -> k));
                prDtos.forEach(k -> {
                    String key = StrUtil.format("{}{}", k.getPnrSegId(), k.getTnId());
                    if (s2Map.containsKey(key)) {
                        MnjxPnrNmTicket mnjxPnrNmTicket = s2Map.get(key);
                        k.setPsgNum(mnjxPnrNmTicket.getHbnb2());
                    }
                });
            }
        }
    }

    /**
     * 指令业务处理
     *
     * @param cmd cmd
     * @return 指令业务处理
     * @throws UnifiedResultException 统一异常
     */
    private PrResultDto dealPr(MemoryData memoryData, String cmd) throws UnifiedResultException {
        // 航班号
        String flightNo;
        // 航班日期
        String flightDate;
        // 舱位
        String cabin;
        // 航站
        String org;
        // 参数
        String param;
        // hbnb
        String hbnb;
        // 解析指令数据、缺省航班的数据
        if (ReUtil.isMatch(REG_PR2, cmd)) {
            List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_PR2), cmd);
            flightNo = groups.get(1);
            flightDate = groups.get(2);
            cabin = groups.get(5);
            org = groups.get(6);
            param = groups.get(7);
            hbnb = groups.get(8);
        } else {
            // 缺省航班
            MemoryDataFt ft = memoryData.getMemoryDataFt();
            if (ObjectUtil.isEmpty(ft) || StrUtil.isEmpty(ft.getFlightNo()) || StrUtil.isEmpty(ft.getFlightDate())) {
                throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
            }
            flightNo = ft.getFlightNo();
            flightDate = ft.getFlightDate();
            cabin = "*";
            List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_PR3), cmd);
            param = groups.get(1);
            // 取出航站的三字码
            org = memoryData.getMnjxOffice().getOfficeNo().substring(0, 3);
            hbnb = groups.get(2);
        }

        // 航站
        if (StrUtil.isEmpty(org)) {
            // 设置航站
            org = memoryData.getMnjxOffice().getOfficeNo().substring(0, 3);
        }

        try {
            if (ReUtil.isMatch(REG_DATE, flightDate)) {
                flightDate = DateUtils.com2ymd(flightDate);
            }
        } catch (Exception e) {
            throw new UnifiedResultException(e.getMessage());
        }

        List<String> rangeDate = rangeDate();
        // 如果查询日期不在当前日期前后3天内（即只有当天及前后2天属于合法查询日期）
        if (CollUtil.isEmpty(rangeDate) || !rangeDate.contains(flightDate)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        // 航班在查询日期不存在
        MnjxPlanFlight planFlight = prMapper.retrievePlanFlightInitial(flightNo, flightDate);
        if (ObjectUtil.isEmpty(planFlight)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        // 航班是否初始化
        if (StrUtil.isEmpty(planFlight.getIsFlightInitial()) || !"Y".equals(planFlight.getIsFlightInitial())) {
            throw new UnifiedResultException(Constant.NEED_INITIALIZE);
        }

        // 是否为三字机场码
        MnjxAirport airport = iMnjxAirportService.lambdaQuery().eq(MnjxAirport::getAirportCode, org).one();
        if (ObjectUtil.isEmpty(airport)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        // 如果航站与当前航班的各航站比较，如果不属于航班所经历的航站，或者是航班的最后一个到达航站
        MnjxTcardSection tcardSection = prMapper.retrieveDepartureCity(flightNo, airport.getAirportId());
        if (ObjectUtil.isEmpty(tcardSection)) {
            throw new UnifiedResultException(Constant.CITY);
        }
        // 可为任意单字母或 * 号
        if (!cabin.matches(REG_CABIN)) {
            throw new UnifiedResultException(Constant.CABIN);
        }
        if (!"*".equals(cabin)) {
            // 该航班无该舱位
            MnjxOpenCabin openCabin = prMapper.retrieveSellCabin(flightNo, flightDate, cabin, airport.getAirportId());
            if (ObjectUtil.isEmpty(openCabin)) {
                throw new UnifiedResultException(Constant.CABIN);
            }
        }

        // 业务处理
        return this.prHandle(memoryData, flightNo, flightDate, cabin, org, param, hbnb, cmd);
    }

    /**
     * 行李
     *
     * @param cmd cmd
     * @return 行李
     */
    private PrResultDto dealPrBt(MemoryData memoryData, String cmd) throws UnifiedResultException {
        PrResultDto prResultDto = new PrResultDto();
        // 放入缓存中 方便后续使用
        // 清除pd缓存
        iCkiCacheService.clearPdDefaultInfo(memoryData);
        List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_BT), cmd);
        String luggageNo = groups.get(1);
        // 支持全部10位行李号或后6位行李号
        if (luggageNo.length() != Constant.TEN && luggageNo.length() != Constant.SIX) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        luggageNo = StrUtil.fill(luggageNo, '0', 10, true);
        List<String> rangeDate = retrievalRange();
        List<MnjxLuggage> mnjxLuggages = prMapper.retrieveByBtLuggageNo(luggageNo, rangeDate);
        if (CollUtil.isEmpty(mnjxLuggages)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        String pnrNmId = mnjxLuggages.get(0).getPnrNmId();
        List<PrDto> prs = prMapper.retrieveByPnrNmId(pnrNmId, null, null, null, null, null);
        if (CollUtil.isEmpty(prs)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        String param = "BT" + luggageNo;
        prResultDto.setParam(param);
        prResultDto.getPrDtos().addAll(prs);
        return prResultDto;
    }

    /**
     * 候补旅客
     *
     * @param cmd cmd
     * @return 候补旅客
     */
    private PrResultDto dealPrSb(MemoryData memoryData, String cmd) throws UnifiedResultException {
        PrResultDto prResultDto = new PrResultDto();
        // 清除pd缓存
        iCkiCacheService.clearPdDefaultInfo(memoryData);
        List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_SB), cmd);
        // 前面自动补0为4位
        String hbNo = StrUtil.fill(groups.get(1), '0', 4, true);
        List<String> rangeDate = retrievalRange();
        List<PrDto> prDtos = prMapper.retrieveByPnrHbNo(hbNo, rangeDate);
        if (CollUtil.isEmpty(prDtos)) {
            throw new UnifiedResultException(Constant.PSGR_ID);
        }
        String cabin = "*";
        prResultDto.setCabin(cabin);
        prResultDto.setParam("SB" + hbNo);
        prResultDto.getPrDtos().addAll(prDtos);
        return prResultDto;
    }

    /**
     * 获取今明两天
     *
     * @return 获取今明两天
     */
    private static List<String> retrievalRange() {
        Date now = new Date();
        DateTime back = DateUtil.offsetDay(now, 1);
        // 今明两天
        return DateUtil.rangeToList(now, back, DateField.DAY_OF_YEAR).stream()
                .map(DateTime::toDateStr)
                .collect(Collectors.toList());
    }

    /**
     * 获取前后2天
     *
     * @return 获取前后2天
     */
    private List<String> rangeDate() {
        Date now = new Date();
        DateTime front = DateUtil.offsetDay(now, -2);
        DateTime back = DateUtil.offsetDay(now, 2);
        // 把时间处理成yyyy-MM-dd格式
        return DateUtil.rangeToList(front, back, DateField.DAY_OF_YEAR)
                .stream()
                .map(DateTime::toDateStr)
                .collect(Collectors.toList());
    }

    /**
     * 业务处理
     *
     * @param param param
     * @throws UnifiedResultException 统一异常
     */
    private PrResultDto prHandle(MemoryData memoryData, String flightNo, String flightDate, String cabin, String org, String param, String hbnb, String cmd) throws UnifiedResultException {
        // 格式验证
        if (!ReUtil.isMatch(PSG_NM, param) && !ReUtil.isMatch(PSG_BN, param) && !ReUtil.isMatch(PSG_SN, param) &&
                !ReUtil.isMatch(PSG_RL, param) && !ReUtil.isMatch(PSG_BT, param) && !ReUtil.isMatch(PSG_ET, param) &&
                !ReUtil.isMatch(PSG_SB, param)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 放入缓存中 方便后续使用
        List<PrDto> prDtos = null;
        // 输入姓名
        if (ReUtil.isMatch(PSG_NM, param)) {
            String queryName = ReUtil.get(PSG_NM, param, 1);
            prDtos = prMapper.retrieveByQueryName(flightNo, flightDate, cabin, org, queryName);
            if (CollUtil.isEmpty(prDtos)) {
                prDtos = prMapper.retrieveByQueryNameAndCarrierFlight(flightNo, flightDate, cabin, org, queryName);
            }
        } else if (ReUtil.isMatch(PSG_BN, param)) {
            // 如果上一次指令不是HBPA清除PD结果
            String lastAction = MemoryDataUtils.getMemoryData().getMemoryDataHandlerResult().getLastAction();
            if (!Constant.CMD_HBPA.equals(lastAction) && !Constant.CMD_PA.equals(lastAction) && !(cmd.startsWith("PR:") && StrUtil.isNotEmpty(hbnb) && cmd.endsWith(hbnb))) {
                // 清除PD缓存
                iCkiCacheService.clearPdDefaultInfo(MemoryDataUtils.getMemoryData());
            }
            // 输入登机口
            String aboardNo = ReUtil.get(PSG_BN, param, 1);
            // 不够3位时自动补全
            aboardNo = StrUtil.fill(aboardNo, '0', 3, true);
            prDtos = prMapper.retrieveByAboardNo(flightNo, flightDate, cabin, org, aboardNo);
            if (CollUtil.isEmpty(prDtos)) {
                prDtos = prMapper.retrieveByAboardNoAndCarrierFlight(flightNo, flightDate, cabin, org, aboardNo);
            }
            if (CollUtil.isEmpty(prDtos)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
        } else if (ReUtil.isMatch(PSG_SN, param)) {
            // 输入座位号
            // 取座位
            String seatNo = ReUtil.get(PSG_SN, param, 1);
            MnjxAirport airport = iMnjxAirportService.lambdaQuery().eq(MnjxAirport::getAirportCode, org).one();
            if (ObjectUtil.isEmpty(airport)) {
                throw new UnifiedResultException("对应的机场不存在！");
            }
            // 当前输入座位号与当前航班的开舱座位号进行比较
            MnjxSeat seat = prMapper.retrieveBySeatNo(flightNo, flightDate, seatNo, airport.getAirportId());
            if (ObjectUtil.isEmpty(seat)) {
                throw new UnifiedResultException(Constant.SEATS);
            }
            prDtos = prMapper.retrieveBySeatNoPr(flightNo, flightDate, cabin, org, seatNo);
            if (CollUtil.isEmpty(prDtos)) {
                prDtos = prMapper.retrieveBySeatNoPrAndCarrierFlight(flightNo, flightDate, cabin, org, seatNo);
            }
        } else if (ReUtil.isMatch(PSG_RL, param)) {
            // pnr
            String pnr = ReUtil.get(PSG_RL, param, 1);
            prDtos = prMapper.retrieveByPnr(flightNo, flightDate, cabin, org, pnr);
            if (CollUtil.isEmpty(prDtos)) {
                prDtos = prMapper.retrieveByPnrAndCarrierFlight(flightNo, flightDate, cabin, org, pnr);
            }
        } else if (ReUtil.isMatch(PSG_BT, param)) {
            // 清除PD缓存
            iCkiCacheService.clearPdDefaultInfo(MemoryDataUtils.getMemoryData());
            // 行李号
            List<String> groups = ReUtil.getAllGroups(PSG_BT, param);
            String luggageNo = groups.get(1);
            prDtos = prMapper.retrieveByLuggageNo(flightNo, flightDate, cabin, org, luggageNo);
            if (CollUtil.isEmpty(prDtos)) {
                prDtos = prMapper.retrieveByLuggageNoAndCarrierFlight(flightNo, flightDate, cabin, org, luggageNo);
            }
        } else if (ReUtil.isMatch(PSG_ET, param)) {
            // 电子客票
            List<String> groups = ReUtil.getAllGroups(PSG_ET, param);
            String ticketNo = groups.get(1);
            prDtos = prMapper.retrieveByTicketNo(flightNo, flightDate, cabin, org, ticketNo);
            if (CollUtil.isEmpty(prDtos)) {
                prDtos = prMapper.retrieveByTicketNoAndCarrierFlight(flightNo, flightDate, cabin, org, ticketNo);
            }
        } else if (ReUtil.isMatch(PSG_SB, param)) {
            // 候补
            List<String> groups = ReUtil.getAllGroups(PSG_SB, param);
            String hbNo = groups.get(1);
            List<String> rangeDate = rangeDate();
            prDtos = prMapper.retrieveByPnrHbNo(hbNo, rangeDate);
        }
        return handleByParamsType(memoryData, param, hbnb, prDtos, cabin);
    }

    /**
     * 按参数类型
     *
     * @param memoryData memoryData
     * @param param      param
     * @param hbnb       hbnb
     * @param prDtos     prDtos
     * @return 按参数类型
     * @throws UnifiedResultException 统一异常
     */
    private PrResultDto handleByParamsType(MemoryData memoryData, String param, String hbnb, List<PrDto> prDtos, String cabin) throws UnifiedResultException {
        PrResultDto prResultDto = new PrResultDto();
        // 处理回显
        if (CollUtil.isEmpty(prDtos)) {
            throw new UnifiedResultException(Constant.NO_RECORD);
        }
        assemblePsgNum(prDtos);
        if (StrUtil.isNotEmpty(hbnb)) {
            param = StrUtil.format("{}{}", param, hbnb);

            // 执行PR后应该把其他的P指令缓存清除，保证每一次提取旅客没有上一个旅客的缓存影响
            iCkiCacheService.clearPaCache(memoryData);
            iCkiCacheService.clearPuCache(memoryData);
            iCkiCacheService.clearPwCache(memoryData);
            PdInfoDto pdDefaultInfo = iCkiCacheService.getPdDefaultInfo(memoryData);
            // 如果不为空清除sby的结果集
            if (ObjectUtil.isNotEmpty(pdDefaultInfo)) {
                iCkiCacheService.clearPdSbyInfo(memoryData);
            }
        }
        prResultDto.setCabin(cabin);
        if (CollUtil.isNotEmpty(prDtos) && prDtos.size() > 1) {
            prResultDto.setMultiple(true);
        }
        prResultDto.setParam(param);
        prResultDto.getPrDtos().addAll(prDtos);
        return prResultDto;
    }

    /**
     * 构建回显结果
     *
     * @param pr pr
     * @return 构建回显结果
     */
    @Override
    public UnifiedResult buildResult(PrDto pr, String param, String cabin) {
        String pnrNmId = pr.getPnrNmId();
        String flightNo = pr.getFlightNo();
        if (StrUtil.isNotEmpty(pr.getCarrierFlight())) {
            flightNo = pr.getCarrierFlight();
        }
        String sellCabin = pr.getSellCabin();
        if (StrUtil.isEmpty(cabin)) {
            cabin = sellCabin;
        }
        String org = pr.getOrg();
        String dst = pr.getDst();
        String flightDate = DateUtils.ymd2Com(pr.getFlightDate());
        String pnrCrs = pr.getPnrCrs();
        String pnrIcs = pr.getPnrIcs();
        // 旅客状态
        String ckiStatus = pr.getCkiStatus();
        param = StrUtil.fill(StrUtil.emptyToDefault(param, ""), ' ', 12, false);
        if (param.matches("BT\\d+")) {
            param = StrUtil.fill(param, ' ', 13, false);
        } else if (param.startsWith("ET") && param.endsWith(",HBNB")) {
            pnrCrs = StrUtil.format("        CRS RL  {}", pr.getPnrCrs());
        }

        // SSR
        List<MnjxNmSsr> pnrSsrList = getCurrentOrThoroughfareSsr(new ArrayList<>(), pnrNmId, pr.getPnrSegNo());

        // 团队名
        String gn = "";
        if (ObjectUtil.isNotEmpty(pr.getDefaultGroupName())) {
            gn = pr.getDefaultGroupName();
        }
        gn = StrUtil.fill(gn, ' ', 2, false);
        String queryName = StrUtil.fill(pr.getQueryName().toUpperCase(), ' ', 17, false);
        // 登机牌号
        if (StrUtil.isNotEmpty(pr.getAboardNo())) {
            pr.setAboardNo("BN" + pr.getAboardNo());
        }
        String aboardNo = StrUtil.fill(StrUtil.emptyToDefault(pr.getAboardNo(), ""), ' ', 5, false);
        // 座位号
        String seatNo = StrUtil.fill(StrUtil.emptyToDefault(pr.getSeat(), ""), ' ', 4, false);
        if (StrUtil.isNotEmpty(ckiStatus) && Constant.CKI_STATUS_GT.equals(ckiStatus)) {
            seatNo = StrUtil.fill("*" + pr.getSeat(), ' ', 4, false);
        }
        List<String> specialList = Arrays.asList("STCR", "JMP");
        List<MnjxNmSsr> stcrSsr = pnrSsrList.stream().filter(k -> specialList.contains(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(stcrSsr)) {
            MnjxNmSsr nmSsr = stcrSsr.get(0);
            seatNo = StrUtil.fill(nmSsr.getSsrType(), ' ', 4, false);
        }
        String ticketNo = null;
        // 票号
        List<MnjxPnrNmTicket> pnrNmTicket = prMapper.retrieveBySegTicketNo(pnrNmId, flightNo, pr.getFlightDate());
        if (CollUtil.isNotEmpty(pnrNmTicket) && ObjectUtil.isNotEmpty(pnrNmTicket.get(0))) {
            ticketNo = pnrNmTicket.get(0).getTicketNo();
        }
        if (StrUtil.isNotEmpty(ticketNo)) {
            pr.setTicketNo(ticketNo);
        }

        List<String> result = new ArrayList<>();

        // 回显内容
        StringBuilder sb = new StringBuilder();
        this.extractedPsg(pr, param, sellCabin, dst, ckiStatus, gn, queryName, aboardNo, seatNo, sb);
        this.extractedPrContent(pr, pnrNmId, flightNo, org, dst, pnrSsrList, result, sb);

        return buildHbpr(param, cabin, flightNo, org, flightDate, pnrCrs, pnrIcs, result);
    }

    private void extractedPrContent(PrDto pr, String pnrNmId, String flightNo, String org, String dst, List<MnjxNmSsr> pnrSsrList, List<String> result, StringBuilder sb) {
        // PnrId
        String pnrId = pr.getPnrId();
        // psgCikId
        String psgCkiId = pr.getPsgCkiId();
        // 舱位等级
        String cabinClass = pr.getCabinClass();
        // 性别
        String sex = pr.getSex();
        // 证件号
        String idNumber = this.getIdNumber(pnrSsrList);
        // 行李
        List<MnjxLuggage> luggages = iMnjxLuggageService.lambdaQuery()
                .eq(MnjxLuggage::getPnrNmId, pnrNmId)
//                .isNull(MnjxLuggage::getIsDel)
                .list();
        // 候补旅客
        String hbType = null;
        if (StrUtil.isNotEmpty(pr.getNrec())) {
            hbType = "NREC";
        } else if (StrUtil.isNotEmpty(pr.getUres())) {
            hbType = "URES";
        }
        if (StrUtil.isNotEmpty(hbType)) {
            sb.append(" ").append(hbType);
        }
        // 婴儿摇篮、客舱宠物
        List<MnjxPsgCkiOption> psgCkiOptions = iMnjxPsgCkiOptionService.lambdaQuery().eq(MnjxPsgCkiOption::getPsgCkiId, psgCkiId).list();
        // 额外占座
        List<String> exstList = Arrays.asList("EXST", "CBBG", "COUR", "DIPL");
        List<MnjxPsgCkiOption> exstPsgCikList = psgCkiOptions.stream().filter(k -> exstList.contains(k.getOptionType())).collect(Collectors.toList());
        // 婴儿摇篮、客舱宠物
        this.cradleCabin(sb, exstPsgCikList, psgCkiOptions, exstList, hbType);
        // 性别
        if (StrUtil.isNotEmpty(sex)) {
            sb.append(" ").append(sex).append("1/0");
        }
        String ssrType = this.getSsrType(pnrSsrList, pnrNmId);
        if (StrUtil.isNotEmpty(ssrType)) {
            sb.append(" ").append(ssrType);
        }
        // 升降舱
        String liftCabin = this.liftCabin(pnrSsrList);
        if (StrUtil.isNotEmpty(liftCabin)) {
            sb.append(" ").append(liftCabin);
            if (StrUtil.isNotEmpty(pr.getSeat())) {
                sb.append(" R").append(pr.getSeat());
            }
        }
        List<MnjxNmSsr> ssrTypeInf = this.getSsrTypeInf(pnrSsrList, pnrNmId, pr);
        if (CollUtil.isNotEmpty(ssrTypeInf)) {
            sb.append(" INF1/0");
        }
        // 儿童
        String typeChd = getSsrTypeChd(pnrSsrList);
        if (StrUtil.isNotEmpty(typeChd)) {
            sb.append(" ").append(typeChd);
        }
        String cndCabinWeight = this.getCndCabinWeight(flightNo, pr.getFlightDate(), cabinClass);
        // 常客信息
        String ffInfo = "";
        if (StrUtil.isNotEmpty(idNumber) && pnrSsrList.stream().anyMatch(s -> Constant.SSR_TYPE_FQTV.equals(s.getSsrType()))) {
            MnjxNmSsr fqtvSsr = pnrSsrList.stream()
                    .filter(s -> Constant.SSR_TYPE_FQTV.equals(s.getSsrType()))
                    .collect(Collectors.toList())
                    .get(0);
            String airlineCode = fqtvSsr.getAirlineCode();
            if (StrUtil.isNotEmpty(pr.getCarrierFlight())) {
                airlineCode = pr.getCarrierFlight().substring(0, 2);
            }
            MnjxFrequenter frequenter = iMnjxFrequenterService.lambdaQuery()
                    .eq(MnjxFrequenter::getAirlineCode, airlineCode)
                    .eq(MnjxFrequenter::getFrequenterCertificateType, idNumber.substring(0, 2))
                    .eq(MnjxFrequenter::getFrequenterCertificateNo, idNumber.substring(2))
                    .one();
            if (ObjectUtil.isNotEmpty(frequenter)) {
                ffInfo = StrUtil.format("                                        FF/{} {}/{} {}", airlineCode, frequenter.getFrequenterCard(), frequenter.getFrequenterLevel(), cndCabinWeight);
            }
        }
        // 行李
        this.getLuggageWeight(luggages, pr.getPnrSegNo(), sb);
        // 免费行李重量，当有常客信息时，免费行李重量显示在FF行
        if (StrUtil.isEmpty(ffInfo) && StrUtil.isNotEmpty(cndCabinWeight)) {
            sb.append(" ").append(cndCabinWeight);
        }
        if (CollUtil.isNotEmpty(ssrTypeInf)) {
            sb.append(" I").append(cndCabinWeight);
        }
        this.autoWrap(sb, result);
        if (StrUtil.isNotEmpty(ffInfo)) {
            result.add(ffInfo);
        }
        // 票号
        if (StrUtil.isNotEmpty(pr.getTicketNo())) {
            result.add(StrUtil.format("                                        ET TKNE/{}/1", pr.getTicketNo()));
        }
        this.getSsrInfTicket(pnrSsrList, pnrNmId, result, false);
        if (StrUtil.isNotEmpty(idNumber)) {
            result.add(StrUtil.format("                                        FOID/{}", idNumber));
        }
        if (StrUtil.isNotEmpty(pr.getIsCnin())) {
            result.add(StrUtil.format("                                        {}/{}", pr.getIsCnin(), pr.getName()));
        }
        // 额外占座
        this.extraSeating(pr, result, exstPsgCikList);

        // 婴儿
        this.getSsrInf(pnrSsrList, pnrNmId, result);

        // PSM
        this.getSsrTypePsm(pnrSsrList, result);

        // MEDA特服
        this.getSsrTypeMeda(pnrSsrList, result, flightNo, cabinClass);

        // 共享航班信息
        this.getCarrierFlight(pr, result);

        // 构建CTC
        // 设置CTC信息
        this.getLuggageOrMealsOrInterline(pnrNmId, pnrId, org, dst, pr, result, psgCkiId, flightNo, pnrSsrList, luggages);

        // 组装操作记录
        this.getPsgOperateRecords(psgCkiId, result, pnrNmId, dst);
    }

    private UnifiedResult buildHbpr(String param, String cabin, String flightNo, String org, String flightDate, String pnrCrs, String pnrIcs, List<String> result) {
        HbprDto hbprDto = new HbprDto();
        hbprDto.setFlightNo(flightNo);
        hbprDto.setFlightDate(flightDate);
        hbprDto.setCabin(cabin);
        hbprDto.setOrg(org);
        hbprDto.setParam(param);
        hbprDto.setPnrCrs(pnrCrs);
        hbprDto.setPnrIcs(pnrIcs);
        hbprDto.setResult(result);
        UnifiedResult unifiedResult = new UnifiedResult();
        unifiedResult.setResults(Collections.singletonList(hbprDto).toArray());
        return unifiedResult;
    }

    /**
     * 旅客回显
     *
     * @param pr        pr
     * @param param     param
     * @param sellCabin sellCabin
     * @param dst       dst
     * @param ckiStatus ckiStatus
     * @param gn        gn
     * @param queryName queryName
     * @param aboardNo  aboardNo
     * @param seatNo    seatNo
     * @param sb        sb
     */
    private void extractedPsg(PrDto pr, String param, String sellCabin, String dst, String ckiStatus, String gn, String queryName, String aboardNo, String seatNo, StringBuilder sb) {
        // 是否是候补
        String isHb = pr.getIsHb();
        if (Constant.CKI_STATUS_DL.equals(ckiStatus)) {
            sb.append(StrUtil.format("  1. {} {} {}       {} {}", queryName, gn, "DELETED", sellCabin, dst));
        } else if (StrUtil.isNotEmpty(isHb) && StrUtil.toString(Constant.ONE).equals(isHb) && Constant.SB.equals(ckiStatus)) {
            String hbNo = StrUtil.format("SB{}", pr.getHbNo());
            if (StrUtil.isNotEmpty(pr.getCap()) && pr.getCap().equals(Constant.STR_ONE)) {
                sb.append(StrUtil.format("  1. {}    {} CAP    {} {}", queryName, hbNo, sellCabin, dst));
            } else {
                sb.append(StrUtil.format("  1. {}    {} URES   {} {}", queryName, hbNo, sellCabin, dst));
            }
        } else {
            sb.append(StrUtil.format("  1. {} {} {}  {}   {} {}", queryName, gn, aboardNo, seatNo, sellCabin, dst));
        }
        if (param.trim().endsWith(",HBNB") && StrUtil.isNotEmpty(pr.getPsgNum())) {
            sb.append(StrUtil.format(" [{}]", pr.getPsgNum()));
        }
        // 显示销售的航班信息
        if (StrUtil.isNotEmpty(pr.getCarrierFlight())) {
            sb.append(StrUtil.format(" SIP{}", pr.getFlightNo().substring(0, 2)));
        }
    }

    /**
     * 婴儿摇篮、客舱宠物
     *
     * @param sb            sb
     * @param psgCkiOptions psgCkiOptions
     * @param exstList      exstList
     */
    private void cradleCabin(StringBuilder sb, List<MnjxPsgCkiOption> exstPsgCikList, List<MnjxPsgCkiOption> psgCkiOptions, List<String> exstList, String hbType) {
        if (CollUtil.isNotEmpty(exstPsgCikList)) {
            // 如果有额外占座需要显示候补标识
            if (StrUtil.isEmpty(hbType)) {
                sb.append(" URES");
            }
            // NREC可能同时存在URES
            if (Constant.HB_TYPE_NREC.equals(hbType)) {
                sb.append(" URES");
            }
            sb.append(" ").append(exstPsgCikList.get(0).getOptionType());
            if (StrUtil.isNotEmpty(psgCkiOptions.get(0).getOptionValue())) {
                sb.append(psgCkiOptions.get(0).getOptionValue());
            }
        }
        List<MnjxPsgCkiOption> cradlePetList = psgCkiOptions.stream().filter(k -> !exstList.contains(k.getOptionType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(cradlePetList)) {
            for (MnjxPsgCkiOption cradlePet : cradlePetList) {
                sb.append(" ").append(cradlePet.getOptionType());
                if (StrUtil.isNotEmpty(cradlePet.getOptionValue())) {
                    sb.append(cradlePet.getOptionValue());
                }
            }
        }
    }

    /**
     * 额外占座
     *
     * @param pr             pr
     * @param result         result
     * @param exstPsgCikList exstPsgCikList
     */
    private void extraSeating(PrDto pr, List<String> result, List<MnjxPsgCkiOption> exstPsgCikList) {
        if (StrUtil.isNotEmpty(pr.getSeatExst()) && CollUtil.isNotEmpty(exstPsgCikList)) {
            // 额外占座
            String seatExst = StrUtil.fill(StrUtil.emptyToDefault(pr.getSeatExst(), ""), ' ', 4, false);
            MnjxPsgCkiOption ckiOption = exstPsgCikList.get(0);
            if (StrUtil.isNotEmpty(ckiOption.getOptionValue())) {
                result.add(StrUtil.format("                                 {}   {}{}", seatExst, ckiOption.getOptionType(), ckiOption.getOptionValue()));
            } else {
                result.add(StrUtil.format("                                 {}   {}", seatExst, ckiOption.getOptionType()));
            }
        }
    }

    @Override
    public String buildResultList(List<PrDto> prDtos, String flightNo, String flightDate, String org, String param, String cabin) {
        StringBuilder result = new StringBuilder();
        flightDate = DateUtils.ymd2Com(flightDate);
        result.append("\u0010");
        result.append(StrUtil.format("PR: {}/{}{}{},{}           ", flightNo, flightDate, cabin, org, param));
        result.append(StrUtil.CRLF);
        List<String> pnrNmIds = prDtos.stream().map(PrDto::getPnrNmId).collect(Collectors.toList());
        // 行李
        List<MnjxLuggage> mnjxLuggages = iMnjxLuggageService.lambdaQuery()
                .in(MnjxLuggage::getPnrNmId, pnrNmIds)
                .list();
        Map<String, List<MnjxLuggage>> luggageMap = null;
        if (CollUtil.isNotEmpty(mnjxLuggages)) {
            luggageMap = mnjxLuggages.stream()
                    .collect(Collectors.groupingBy(MnjxLuggage::getPnrNmId));
        }
        // SSR项
        List<MnjxNmSsr> mnjxNmSsrs = iMnjxNmSsrService.lambdaQuery().in(MnjxNmSsr::getPnrNmId, pnrNmIds).list();
        Map<String, List<MnjxNmSsr>> ssrMap = null;
        if (CollUtil.isNotEmpty(mnjxNmSsrs)) {
            ssrMap = mnjxNmSsrs.stream().collect(Collectors.groupingBy(MnjxNmSsr::getPnrNmId));
        }
        int index = 1;
        for (PrDto pr : prDtos) {
            String dst = pr.getDst();
            String pnrNmId = pr.getPnrNmId();
            String sellCabin = pr.getSellCabin();
            String ckiStatus = pr.getCkiStatus();
            String isHb = pr.getIsHb();
            // 团队
            String gn = "";
            if (StrUtil.isNotEmpty(pr.getDefaultGroupName())) {
                gn = pr.getDefaultGroupName();
            }
            gn = StrUtil.fill(gn, ' ', 3, false);
            // 拼音
            String queryName = StrUtil.fill(pr.getQueryName().toUpperCase(), ' ', 18, false);
            // 登机牌号
            if (StrUtil.isNotEmpty(pr.getAboardNo())) {
                pr.setAboardNo("BN" + pr.getAboardNo());
            }
            String aboardNo = StrUtil.fill(StrUtil.emptyToDefault(pr.getAboardNo(), "*" + pr.getPnrIcs()), ' ', 5, false);
            // 座位号
            String seatNo = StrUtil.fill(StrUtil.emptyToDefault(pr.getSeat(), ""), ' ', 4, false);
            if (StrUtil.isNotEmpty(ckiStatus) && "GT".equals(ckiStatus)) {
                seatNo = StrUtil.fill("*" + pr.getSeat(), ' ', 4, false);
            }
            extractedMultiple(result, luggageMap, ssrMap, index, pr, dst, pnrNmId, sellCabin, ckiStatus, isHb, gn, queryName, aboardNo, seatNo);
            index++;
        }
        // 到达城市
        String dst = prDtos.get(0).getDst();
        MnjxCity mnjxCity = prMapper.retrieveByCity(dst);
        result.append(StrUtil.format("  *{}* **********WELCOME TO {} AIRPORT**********", dst, mnjxCity.getCityEname()));
        return result.toString();
    }

    /**
     * 多人
     *
     * @param result     result
     * @param luggageMap luggageMap
     * @param ssrMap     ssrMap
     * @param index      index
     * @param pr         pr
     * @param dst        dst
     * @param pnrNmId    pnrNmId
     * @param sellCabin  sellCabin
     * @param ckiStatus  ckiStatus
     * @param isHb       isHb
     * @param gn         gn
     * @param queryName  queryName
     * @param aboardNo   aboardNo
     * @param seatNo     seatNo
     */
    private void extractedMultiple(StringBuilder result, Map<String, List<MnjxLuggage>> luggageMap, Map<String, List<MnjxNmSsr>> ssrMap, int index, PrDto pr, String dst, String pnrNmId, String sellCabin, String ckiStatus, String isHb, String gn, String queryName, String aboardNo, String seatNo) {
        if (Constant.CKI_STATUS_DL.equals(ckiStatus)) {
            result.append(StrUtil.format("  {}. 1{}{} {}         {} {}", index, queryName, gn, "DELETED ", sellCabin, dst));
        } else if (StrUtil.isNotEmpty(isHb) && StrUtil.toString(Constant.ONE).equals(isHb) && Constant.SB.equals(ckiStatus)) {
            String hbNo = "      ";
            if (StrUtil.isNotEmpty(pr.getHbNo())) {
                hbNo = StrUtil.format("SB{}", pr.getHbNo());
            }
            result.append(StrUtil.format("  {}. 1{}{}    URES {} {}", index, queryName, hbNo, sellCabin, dst));
        } else {
            result.append(StrUtil.format("  {}. 1{}{} {}  {}      {} {}", index, queryName, gn, aboardNo, seatNo, sellCabin, dst));
        }
        // 候补类型
        String hbType = null;
        if (StrUtil.isNotEmpty(pr.getNrec())) {
            hbType = "NREC";
        } else if (StrUtil.isNotEmpty(pr.getUres())) {
            hbType = "URES";
        }
        if (StrUtil.isNotEmpty(hbType)) {
            result.append(" ").append(hbType);
        }
        // 性别
        if (StrUtil.isNotEmpty(pr.getSex())) {
            result.append(StrUtil.format(" {}1", pr.getSex()));
        }
        // PSM 特殊旅客
        List<String> psmList = null;
        // SSR
        if (CollUtil.isNotEmpty(ssrMap) && ssrMap.containsKey(pnrNmId)) {
            List<MnjxNmSsr> nmSsrs = ssrMap.get(pnrNmId);
            nmSsrs = getCurrentOrThoroughfareSsr(nmSsrs, pnrNmId, pr.getPnrSegNo());
            String ssrType = nmSsrs.stream()
                    .filter(k -> !"FOID".equals(k.getSsrType()) && !k.getSsrType().contains("INF"))
                    .map(MnjxNmSsr::getSsrType)
                    .collect(Collectors.joining(" "));
            if (StrUtil.isNotEmpty(ssrType)) {
                result.append(" ").append(ssrType);
            }
            List<MnjxNmSsr> ssrTypeInf = getSsrTypeInf(nmSsrs, pnrNmId, pr);
            if (CollUtil.isNotEmpty(ssrTypeInf)) {
                result.append(" INF");
            }
            // Constant.PSM_TYPES
            List<String> psgTypes = Arrays.asList(Constant.PSM_TYPES);
            psmList = ssrMap.get(pnrNmId).stream().map(MnjxNmSsr::getSsrType).filter(psgTypes::contains).collect(Collectors.toList());
        }
        // 联程
        if (StrUtil.isNotEmpty(pr.getInterlink()) && Constant.STR_ONE.equals(pr.getInterlink())) {
            result.append(" O");
        }
        // 行李
        StringBuilder luggageSb = new StringBuilder();
        if (CollUtil.isNotEmpty(luggageMap) && luggageMap.containsKey(pnrNmId)) {
            this.getLuggageWeight(luggageMap.get(pnrNmId), pr.getPnrSegNo(), luggageSb);
        }
        if (StrUtil.isNotEmpty(luggageSb)) {
            result.append(StrUtil.format(" {}", luggageSb.toString().trim()));
        } else {
            result.append(" BAG0/0/0");
        }
        result.append(" ET");
        // PSM 特殊旅客
        if (CollUtil.isNotEmpty(psmList)) {
            result.append(" PSM");
        }
        result.append(StrUtil.CRLF);
    }

    @Override
    public String getCndCabinWeight(String flightNo, String flightDate, String cabinClass) {
        MnjxCnd mnjxCnd = prMapper.retrieveByCnd(flightNo, flightDate);
        if (ObjectUtil.isEmpty(mnjxCnd)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("FBA/");
        if (StrUtil.isNotEmpty(mnjxCnd.getFirstCabinClass()) && mnjxCnd.getFirstCabinClass().equals(cabinClass)) {
            sb.append(mnjxCnd.getFirstWeight());
        } else if (StrUtil.isNotEmpty(mnjxCnd.getSecondCabinClass()) && mnjxCnd.getSecondCabinClass().equals(cabinClass)) {
            sb.append(mnjxCnd.getSecondWeight());
        } else if (StrUtil.isNotEmpty(mnjxCnd.getThirdCabinClass()) && mnjxCnd.getThirdCabinClass().equals(cabinClass)) {
            sb.append(mnjxCnd.getThirdWeight());
        } else if (StrUtil.isNotEmpty(mnjxCnd.getFourthCabinClass()) && mnjxCnd.getFourthCabinClass().equals(cabinClass)) {
            sb.append(mnjxCnd.getFourthWeight());
        } else if (StrUtil.isNotEmpty(mnjxCnd.getFifthCabinClass()) && mnjxCnd.getFifthCabinClass().equals(cabinClass)) {
            sb.append(mnjxCnd.getFifthWeight());
        }
        sb.append("KG");
        return sb.toString();
    }

    @Override
    public void getInterline(String pnrId, String newSegNo, String pnrNmId, String org, String dst, List<String> result, String cmd) {
        interlineSeg(pnrId, newSegNo, result, cmd, pnrNmId);
    }

    /**
     * 联程
     *
     * @param pnrId    pnrId
     * @param nowSegNo nowSegNo
     * @param result   result
     * @param cmd      cmd
     */
    private void interlineSeg(String pnrId, String nowSegNo, List<String> result, String cmd, String pnrNmId) {
        // 查询是否存在联程航班
        List<PrDto> segList = prMapper.retrieveByInterline(pnrId, pnrNmId);
        Map<Integer, String> resultMap = new HashMap<>(1024);
        if (CollUtil.isNotEmpty(segList) && segList.size() > 1) {
            // 取出出发到达航站信息
            List<String> orgDst = new ArrayList<>();
            segList = segList.stream()
                    .filter(s -> StrUtil.isNotEmpty(s.getFlightDate()))
                    .collect(Collectors.toList());
            segList.forEach(k -> {
                orgDst.add(k.getOrg());
                orgDst.add(k.getDst());
            });
            // 查询机场对应的航站
            List<MnjxAirport> airports = iMnjxAirportService.lambdaQuery().in(MnjxAirport::getAirportCode, orgDst).list();
            // 航站转成Map
            Map<String, String> airportMap = airports.stream().collect(Collectors.toMap(MnjxAirport::getAirportCode, MnjxAirport::getAirportId));
            // 航班信息
            List<FlightSectionDto> flightSectionDtos = prMapper.retrieveByFlightSection(segList.stream().map(PrDto::getFlightNo).collect(Collectors.toList()), segList.stream().map(PrDto::getFlightDate).distinct().collect(Collectors.toList()));
            Map<String, List<FlightSectionDto>> flightSectionMap = flightSectionDtos.stream().collect(Collectors.groupingBy(FlightSectionDto::getFlightNo));
            // 当前航段序号
            int currentSegNo = Integer.parseInt(nowSegNo);
            PrDto currentSeg = segList.stream().filter(k -> Integer.parseInt(k.getPnrSegNo()) == currentSegNo).collect(Collectors.toList()).get(0);
            // 之前的航段
            List<PrDto> frontSegList = segList.stream().filter(k -> StrUtil.isNotEmpty(k.getFlightDate()) && Integer.parseInt(k.getPnrSegNo()) < currentSegNo).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(frontSegList)) {
                String segDate = StrUtil.format("{} {}", currentSeg.getFlightDate(), currentSeg.getEstimateOff());
                frontSegList = frontSegList.stream().sorted(Comparator.comparing(PrDto::getPnrSegNo).reversed()).collect(Collectors.toList());
                for (int i = 0; i < frontSegList.size(); i++) {
                    PrDto frontSeg = frontSegList.get(i);
                    String upperSeg = StrUtil.format("{} {}", frontSeg.getFlightDate(), frontSeg.getEstimateArr());
                    if (Integer.parseInt(frontSeg.getEstimateOff()) > Integer.parseInt(frontSeg.getEstimateArr())) {
                        // 航段信息
                        List<FlightSectionDto> sections = getFlightSection(flightSectionMap, airportMap, frontSeg);
                        if (CollUtil.isNotEmpty(sections)) {
                            FlightSectionDto flightSectionDto = sections.get(0);
                            if (ObjectUtil.isNotEmpty(flightSectionDto) && StrUtil.isNotEmpty(flightSectionDto.getEstimateArrChange())) {
                                // 是否跨天
                                int estimateArrChange = Integer.parseInt(flightSectionDto.getEstimateArrChange().substring(1));
                                DateTime flightDate = DateUtil.parse(frontSeg.getFlightDate(), "yyyy-MM-dd");
                                DateTime dateTime = DateUtils.offsetDay(flightDate, estimateArrChange);
                                upperSeg = StrUtil.format("{} {}", dateTime.toDateStr(), frontSeg.getEstimateArr());
                            }
                        }
                    }
                    if (i != 0) {
                        PrDto nextSeg = frontSegList.get(i - 1);
                        segDate = StrUtil.format("{} {}", nextSeg.getFlightDate(), nextSeg.getEstimateOff());
                    }
                    long hour = DateUtil.between(DateUtil.parse(upperSeg, "yyyy-MM-dd HHmm"), DateUtil.parse(segDate, "yyyy-MM-dd HHmm"), DateUnit.HOUR);
                    if (hour > 12) {
                        break;
                    }
                    // 联程航段
                    StringBuilder interlineSeg = new StringBuilder();
                    interlineSeg.append(extractedInterlineSeg(false, cmd, frontSeg));
                    // 额外占座
                    String interlineAdditionalSeats = this.getInterlineAdditionalSeats(frontSeg);
                    if (StrUtil.isNotEmpty(interlineAdditionalSeats)) {
                        interlineSeg.append(StrUtil.CRLF).append(interlineAdditionalSeats);
                    }
                    resultMap.put(Integer.parseInt(frontSeg.getPnrSegNo()), interlineSeg.toString());
                }
            }
            // 后面的航段
            List<PrDto> behindSegList = segList.stream().filter(k -> Integer.parseInt(k.getPnrSegNo()) > currentSegNo).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(behindSegList)) {
                String segDate = StrUtil.format("{} {}", currentSeg.getFlightDate(), currentSeg.getEstimateArr());
                if (Integer.parseInt(currentSeg.getEstimateOff()) > Integer.parseInt(currentSeg.getEstimateArr())) {
                    // 航段信息
                    List<FlightSectionDto> sections = getFlightSection(flightSectionMap, airportMap, currentSeg);
                    if (CollUtil.isNotEmpty(sections)) {
                        FlightSectionDto flightSectionDto = sections.get(0);
                        if (ObjectUtil.isNotEmpty(flightSectionDto) && StrUtil.isNotEmpty(flightSectionDto.getEstimateArrChange())) {
                            // 是否跨天
                            int estimateArrChange = Integer.parseInt(flightSectionDto.getEstimateArrChange().substring(1));
                            DateTime flightDate = DateUtil.parse(currentSeg.getFlightDate(), "yyyy-MM-dd");
                            DateTime dateTime = DateUtils.offsetDay(flightDate, estimateArrChange);
                            segDate = StrUtil.format("{} {}", dateTime.toDateStr(), currentSeg.getEstimateArr());
                        }
                    }
                }
                behindSegList = behindSegList.stream().sorted(Comparator.comparing(PrDto::getPnrSegNo)).collect(Collectors.toList());
                for (int i = 0; i < behindSegList.size(); i++) {
                    PrDto nextSeg = behindSegList.get(i);
                    String nextSegDate = StrUtil.format("{} {}", nextSeg.getFlightDate(), nextSeg.getEstimateOff());
                    if (i != 0) {
                        PrDto frontSeg = behindSegList.get(i - 1);
                        segDate = StrUtil.format("{} {}", frontSeg.getFlightDate(), frontSeg.getEstimateArr());
                    }
                    long hour = DateUtil.between(DateUtil.parse(segDate, "yyyy-MM-dd HHmm"), DateUtil.parse(nextSegDate, "yyyy-MM-dd HHmm"), DateUnit.HOUR);
                    if (hour > 12) {
                        break;
                    }
                    StringBuilder interlineSeg = new StringBuilder();
                    interlineSeg.append(extractedInterlineSeg(true, cmd, nextSeg));
                    // 额外占座
                    String interlineAdditionalSeats = this.getInterlineAdditionalSeats(nextSeg);
                    if (StrUtil.isNotEmpty(interlineAdditionalSeats)) {
                        interlineSeg.append(StrUtil.CRLF).append(interlineAdditionalSeats);
                    }
                    resultMap.put(Integer.parseInt(nextSeg.getPnrSegNo()), interlineSeg.toString());
                }
            }
        }
        if (CollUtil.isNotEmpty(resultMap)) {
            for (Integer key : resultMap.keySet()) {
                result.add(resultMap.get(key));
            }
        }
    }

    private List<FlightSectionDto> getFlightSection(Map<String, List<FlightSectionDto>> flightSectionMap, Map<String, String> airportMap, PrDto seg) {
        // 航段信息
        List<FlightSectionDto> sections = flightSectionMap.get(seg.getFlightNo());
        String orgId = airportMap.get(seg.getOrg());
        String dstId = airportMap.get(seg.getDst());
        List<FlightSectionDto> sectionDtoList = sections.stream()
                .filter(k -> k.getDepAptId().equals(orgId) && k.getArrAptId().equals(dstId))
                .filter(k -> k.getFlightDate().equals(seg.getFlightDate()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(sectionDtoList)) {
            // 按时间筛选
            sections = sections.stream().filter(k -> k.getFlightDate().equals(seg.getFlightDate())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(sections)) {
                boolean findFirstAirport = false;
                sectionDtoList = new ArrayList<>();
                for (FlightSectionDto section : sections) {
                    if (section.getDepAptId().equals(orgId)) {
                        findFirstAirport = true;
                        sectionDtoList.add(section);
                    } else if (findFirstAirport) {
                        sectionDtoList.clear();
                        sectionDtoList.add(section);
                        if (section.getArrAptId().equals(dstId)) {
                            findFirstAirport = false;
                        }
                    }
                }
            }
        }
        return sectionDtoList;
    }

    private String extractedInterlineSeg(boolean org, String cmd, PrDto seg) {
        String result;
        String sb = StrUtil.fill(" ", ' ', 11, false);
        String ckiStatus = seg.getCkiStatus();
        if (StrUtil.isNotEmpty(ckiStatus)) {
            // ACC\GT
            if (ckiStatus.equals(Constant.ACC) || ckiStatus.equalsIgnoreCase(Constant.GT)) {
                sb = StrUtil.fill(StrUtil.format("BN{}  {}", seg.getAboardNo(), seg.getSeat()), ' ', 11, false);
            } else if (ckiStatus.equals(Constant.DL)) {
                sb = StrUtil.fill("DELETED", ' ', 11, false);
            }
            // 处理是否是空填充
            if (StrUtil.isEmpty(sb.trim()) && StrUtil.isEmpty(cmd)) {
                if (StrUtil.isNotEmpty(seg.getIsHb()) && StrUtil.toString(Constant.ONE).equals(seg.getIsHb()) && Constant.SB.equals(ckiStatus)) {
                    sb = StrUtil.fill("STANDBY", ' ', 11, false);
                } else if (ckiStatus.equals(Constant.NACC)) {
                    sb = StrUtil.fill("UN-CHECKED", ' ', 11, false);
                }
            } else {
                if (StrUtil.isNotEmpty(seg.getIsHb()) && StrUtil.toString(Constant.ONE).equals(seg.getIsHb()) && Constant.SB.equals(ckiStatus)) {
                    sb = StrUtil.fill(StrUtil.format("SB{} CAP", seg.getHbNo()), ' ', 11, false);
                }
            }
        }
        // 航班号
        String flightNo = seg.getFlightNo();
        // 航班日期
        String flightDateCom = DateUtils.ymd2Com(seg.getFlightDate());
        // 航班日期
        String flightDate = flightDateCom.substring(0, flightDateCom.length() - 2);
        String sellCabin = seg.getSellCabin();
        if (org) {
            result = StrUtil.format("      O/{}/{}      {}   {} {}", flightNo, flightDate, sb, sellCabin, seg.getDst());
        } else {
            result = StrUtil.format("      I/{}/{}      {}   {} {}", flightNo, flightDate, sb, sellCabin, seg.getOrg());
        }
        return result;
    }

    @Override
    public String getIdNumber(List<MnjxNmSsr> nmSsrs) {
        String idNumber = null;
        if (CollUtil.isNotEmpty(nmSsrs)) {
            nmSsrs = nmSsrs.stream()
                    .filter(k -> Constant.SSR_TYPE_FOID.equals(k.getSsrType()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmSsrs)) {
                MnjxNmSsr pnrSsr = nmSsrs.get(0);
                // 身份证、护照、ID号码
                if (ReUtil.isMatch(REG_FOID, pnrSsr.getSsrInfo())) {
                    idNumber = ReUtil.get(REG_FOID, pnrSsr.getSsrInfo(), 1);
                }
            }
        }
        return idNumber;
    }

    @Override
    public String getSsrType(List<MnjxNmSsr> nmSsrs, String pnrNmId) {
        StringBuilder sb = new StringBuilder();
        // 身份、儿童、婴儿、担架、婴儿摇篮、客舱宠物、额外占座、邻近座位
        List<String> filterSsr = Arrays.asList("FOID", "CHLD", "INFT", "STCR", "PSM", "MSG", "PIL", "UMNR", "TKNE",
                "PETC", "BSCT", "EXST", "CBBG", "COUR", "DIPL", "ADSR", "FQTV", "WCBD", "WCBW", "WCMP", "WCOB", "MEDA");
        // SSR 项信息、去除特殊餐食
        List<MnjxNmSsr> ssrList = nmSsrs.stream()
                .filter(k -> !filterSsr.contains(k.getSsrType()))
                .filter(k -> !k.getSsrType().contains("UPG"))
                .filter(k -> !k.getSsrType().contains("DNG"))
                .filter(k -> !k.getSsrInfo().contains("ML"))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ssrList)) {
            sb.append(ssrList.stream().map(MnjxNmSsr::getSsrType).distinct().collect(Collectors.joining(" ")));
        }
        // 无陪儿童
        List<MnjxNmSsr> umnrSsr = nmSsrs.stream()
                .filter(k -> Constant.SSR_TYPE_UMNR.equals(k.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(umnrSsr)) {
            MnjxPnrNmUm nmUm = iMnjxPnrNmUmService.lambdaQuery().eq(MnjxPnrNmUm::getPnrNmId, pnrNmId).one();
            if (ObjectUtil.isNotEmpty(nmUm)) {
                sb.append(StrUtil.format(" UM{}", nmUm.getUmAge()));
            }
        }
        return sb.toString();
    }

    @Override
    public List<MnjxNmSsr> getSsrTypeInf(List<MnjxNmSsr> nmSsrs, String pnrNmId, PrDto prDto) {
        return nmSsrs.stream().filter(k -> Constant.SSR_TYPE_INFT.equals(k.getSsrType())).collect(Collectors.toList());
    }

    @Override
    public String getSsrTypeChd(List<MnjxNmSsr> nmSsrs) {
        List<MnjxNmSsr> ssrChlds = nmSsrs.stream().filter(k -> Constant.SSR_TYPE_CHLD.equals(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(ssrChlds)) {
            return "CHD1/0";
        }
        return null;
    }

    @Override
    public void getSsrInf(List<MnjxNmSsr> nmSsrs, String pnrNmId, List<String> result) {
        List<MnjxNmSsr> inftNmSsrs = nmSsrs.stream().filter(k -> Constant.SSR_TYPE_INFT.equals(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(inftNmSsrs)) {
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery().eq(MnjxNmXn::getPnrNmId, pnrNmId).one();
            if (ObjectUtil.isNotEmpty(nmXn)) {
                result.add(StrUtil.format("INF-{}", nmXn.getXnCname()));
            }
        }
    }

    @Override
    public void getSsrInfTicket(List<MnjxNmSsr> nmSsrs, String pnrNmId, List<String> result, boolean space) {
        List<MnjxNmSsr> inftNmSsrs = nmSsrs.stream().filter(k -> Constant.SSR_TYPE_INFT.equals(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(inftNmSsrs)) {
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery().eq(MnjxNmXn::getPnrNmId, pnrNmId).one();
            if (ObjectUtil.isNotEmpty(nmXn)) {
                MnjxPnrNmTn nmTn = iMnjxPnrNmTnService.lambdaQuery().eq(MnjxPnrNmTn::getNmXnId, nmXn.getNmXnId()).one();
                if (ObjectUtil.isNotEmpty(nmTn)) {
                    MnjxPnrNmTicket nmTicket = iMnjxPnrNmTicketService.lambdaQuery().eq(MnjxPnrNmTicket::getPnrNmTnId, nmTn.getTnId()).one();
                    if (ObjectUtil.isNotEmpty(nmTicket)) {
                        if (space) {
                            result.add(StrUtil.format("                                        ET TKNE/INF{}/1", nmTicket.getTicketNo()));
                        } else {
                            result.add(StrUtil.format("                                        ET TKNE/INF{}/1", nmTicket.getTicketNo()));
                        }
                    }
                }
            }
        }
    }

    @Override
    public void getSsrTypePsm(List<MnjxNmSsr> nmSsrs, List<String> result) {
        // MSG
        List<MnjxNmSsr> psgList = nmSsrs.stream().filter(k -> "MSG".equals(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(psgList)) {
            MnjxNmSsr mnjxNmSsr = psgList.get(0);
            String info = mnjxNmSsr.getSsrInfo().split(" ")[2];
            result.add(StrUtil.format(" MSG-{}", info.split("/")[0]));
        }
        StringBuilder psm = new StringBuilder();
        // PSM
        List<MnjxNmSsr> psmSsr = nmSsrs.stream().filter(k -> "PSM".equals(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(psmSsr)) {
            MnjxNmSsr nmSsr = psmSsr.get(0);
            String ssrInfo = ReUtil.get(PSM_PATTERN, nmSsr.getSsrInfo(), 1);
            // 过滤VIP
            if (ssrInfo.contains("/VIP")) {
                ssrInfo = ssrInfo.substring(0, ssrInfo.indexOf("/VIP"));
                if (StrUtil.isNotEmpty(ssrInfo)) {
                    psm.append(StrUtil.format("PSM-{}", ssrInfo));
                }
            } else {
                psm.append(StrUtil.format("PSM-{}", ssrInfo));
            }
        }
        // 儿童
        List<MnjxNmSsr> chld = nmSsrs.stream().filter(k -> Constant.SSR_TYPE_CHLD.equals(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(chld)) {
            MnjxNmSsr nmSsr = chld.get(0);
            String chdlReg = "SSR\\sCHLD\\s[A-Z0-9]{2}\\s([A-Z0-9]{3})\\s(\\w{5,7})/P[0-9]+";
            if (ReUtil.isMatch(chdlReg, nmSsr.getSsrInfo())) {
                List<String> groups = ReUtil.getAllGroups(Pattern.compile(chdlReg), nmSsr.getSsrInfo());
                String actionCode = groups.get(1);
                String birthday = groups.get(2);
                if (StrUtil.isEmpty(psm)) {
                    psm.append(StrUtil.format("PSM-//CHLD {} {}", actionCode, birthday));
                } else {
                    psm.append(StrUtil.format(" -//CHLD {} {}", actionCode, birthday));
                }
            }
        }
        // PSM
        if (CollUtil.isNotEmpty(nmSsrs)) {
            MnjxNmSsr mnjxNmSsr = nmSsrs.get(0);
            String pnrNmId = mnjxNmSsr.getPnrNmId();
            List<MnjxNmOsi> mnjxNmOsis = iMnjxNmOsiService.lambdaQuery().eq(MnjxNmOsi::getPnrNmId, pnrNmId).eq(MnjxNmOsi::getPnrOsiType, "VIP").list();
            if (CollUtil.isNotEmpty(mnjxNmOsis)) {
                if (StrUtil.isEmpty(psm)) {
                    psm.append("PSM-");
                }
                psm.append(" /VIP");
            }
        }
        if (StrUtil.isNotEmpty(psm)) {
            result.add(psm.toString());
        }
        // PIL
        List<MnjxNmSsr> pilList = nmSsrs.stream().filter(k -> "PIL".equals(k.getSsrType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(pilList)) {
            MnjxNmSsr mnjxNmSsr = pilList.get(0);
            String info = mnjxNmSsr.getSsrInfo().split(" ")[2];
            result.add(StrUtil.format("PIL-{}", info.split("/")[0]));
        }
    }

    @Override
    public void getSsrTypeMeda(List<MnjxNmSsr> pnrSsrList, List<String> result, String flightNo, String cabinClass) {
        List<MnjxNmSsr> medaSsrList = pnrSsrList.stream()
                .filter(k -> Constant.SSR_TYPE_MEDA.equals(k.getSsrType()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(medaSsrList)) {
            MnjxNmSsr mnjxNmSsr = medaSsrList.get(0);
            result.add(StrUtil.format("MEDA- NN1 {}{}{}{}", mnjxNmSsr.getOrgDst(), flightNo.substring(2), cabinClass, DateUtils.ymd2Com(mnjxNmSsr.getFltDate()).substring(0, 5)));
        }
    }

    @Override
    public void getLuggageOrMealsOrInterline(String pnrNmId, String pnrId, String org, String dst, PrDto pr, List<String> result, String psgCkiId, String flightNo, List<MnjxNmSsr> pnrSsrList, List<MnjxLuggage> luggageList) {
        PrCtcDto prCtc = prMapper.retrieveCtcByNmId(pnrNmId);
        StringBuilder res = new StringBuilder();
        this.ctc(pnrId, result, prCtc, res);
        // 逾重行李
        this.getExLuggage(result, psgCkiId);
        // 手动行李
        this.getLuggageBagTag(result, flightNo, pr.getPnrSegNo(), luggageList, pr.getCkiStatus());
        // 特殊餐食
        this.getSpecialMeals(result, pnrSsrList);
        // WCBD WCBW WCMP WCOB回显需要显示在左下角
        this.getOtherWc(result, pnrSsrList);
        // 联程
        this.getInterline(pnrId, pr.getPnrSegNo(), pr.getPnrNmId(), org, dst, result, "PR");
        // RES AUTO
        if (StrUtil.isNotEmpty(res)) {
            result.add(res.toString());
        }
    }

    /**
     * 联系组
     *
     * @param pnrId  pnrId
     * @param result result
     * @param prCtc  prCtc
     * @param res    res
     */
    private void ctc(String pnrId, List<String> result, PrCtcDto prCtc, StringBuilder res) {
        StringBuilder ctcSb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(prCtc) && StrUtil.isNotEmpty(prCtc.getIssuedTime())) {
            ctcSb.append("CTC-");
            String issuedTime = prCtc.getIssuedTime();
            String dateCom = DateUtils.ymd2Com(DateUtils.ymdhms2YmdStr(issuedTime));
            String mm = DateUtils.ymdhms2hm(issuedTime);
            ctcSb.append(StrUtil.format("{}-{}-{}-T ", prCtc.getOfficeNo(), dateCom, mm));
            res.append(StrUtil.format("     RES AUTO {}{}", dateCom, mm));
        }
        // 所属PNR中的PNR-CT项，如果有多项PNR-CT则任意取一条
        List<MnjxPnrCt> ctList = iMnjxPnrCtService.lambdaQuery().eq(MnjxPnrCt::getPnrId, pnrId).list();
        if (CollUtil.isNotEmpty(ctList)) {
            if (StrUtil.isEmpty(ctcSb)) {
                ctcSb.append("CTC-");
            }
            MnjxPnrCt pnrCt = ctList.get(0);
            ctcSb.append(pnrCt.getInputValue());
        }
        if (StrUtil.isNotEmpty(ctcSb)) {
            result.add(ctcSb.toString());
        }
    }

    /**
     * 特殊餐食
     *
     * @param result     result
     * @param pnrSsrList pnrSsrList
     */
    private void getSpecialMeals(List<String> result, List<MnjxNmSsr> pnrSsrList) {
        List<MnjxNmSsr> mlSsr = pnrSsrList.stream().filter(k -> k.getSsrType().contains("ML")).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(mlSsr)) {
            mlSsr.stream().filter(k -> ReUtil.isMatch(SPML_PATTERN, k.getSsrInfo())).map(k -> {
                List<String> allGroups = ReUtil.getAllGroups(SPML_PATTERN, k.getSsrInfo());
                if (Constant.SSR_TYPE_SPML.equals(allGroups.get(1))) {
                    return allGroups.get(2).trim();
                } else {
                    return allGroups.get(1);
                }
            }).forEach(k -> result.add(StrUtil.format("SPML-{}", k)));
        }
    }

    /**
     * Title: getOtherWc
     * Description: WCBD WCBW WCMP WCOB回显需要显示在左下角
     *
     * @param result
     * @param pnrSsrList
     * @return
     * <AUTHOR>
     * @date 2023/5/5 10:40
     */
    private void getOtherWc(List<String> result, List<MnjxNmSsr> pnrSsrList) {
        List<MnjxNmSsr> otherWcSsr = pnrSsrList.stream()
                .filter(k -> StrUtil.equalsAny(k.getSsrType(), "WCBD", "WCBW", "WCMP", "WCOB"))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(otherWcSsr)) {
            result.add(StrUtil.format("{}-", otherWcSsr.get(0).getSsrType()));
        }
    }

    /**
     * 逾重行李
     *
     * @param result   result
     * @param psgCkiId psgCkiId
     */
    private void getExLuggage(List<String> result, String psgCkiId) {
        List<MnjxExLuggage> exLuggages = iMnjxExLuggageService.lambdaQuery().eq(MnjxExLuggage::getPsgCkiId, psgCkiId).list();
        if (CollUtil.isNotEmpty(exLuggages)) {
            // expc
            List<MnjxExLuggage> expcList = exLuggages.stream().filter(k -> Constant.EX_EXPC.equals(k.getExType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(expcList)) {
                StringBuilder expcSb = new StringBuilder();
                for (MnjxExLuggage ex : expcList) {
                    expcSb.append(StrUtil.format("/{}KG-{}LCM", ex.getWeight(), ex.getLcm()));
                }
                result.add(StrUtil.format("{}- {}{}", Constant.EX_EXPC, expcList.size(), expcSb.toString()));
            }
            // exbg
            List<MnjxExLuggage> exbgList = exLuggages.stream().filter(k -> Constant.EX_EXBG.equals(k.getExType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(exbgList)) {
                for (MnjxExLuggage ex : exbgList) {
                    result.add(StrUtil.format("{}-{}KG/CASH", ex.getExType(), ex.getWeight()));
                }
            }
        }
    }

    /**
     * 手动行李
     *
     * @param result   result
     * @param flightNo flightNo
     * @param nowSegNo nowSegNo
     * @param luggages luggages
     */
    private void getLuggageBagTag(List<String> result, String flightNo, String nowSegNo, List<MnjxLuggage> luggages, String ckiStatus) {
        if (CollUtil.isEmpty(luggages)) {
            return;
        }
        // 选出当前航段的行李信息
        luggages = luggages.stream()
                .filter(k -> StrUtil.isNotEmpty(k.getBagSegNo()))
                .filter(k -> k.getBagSegNo().equals(nowSegNo))
                .collect(Collectors.toList());
        StringBuilder luggageSb = new StringBuilder();
        if (CollUtil.isNotEmpty(luggages)) {
            List<MnjxLuggage> newLuggages = new ArrayList<>();
            List<MnjxLuggage> avihLuggageList = luggages.stream()
                    .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                    .collect(Collectors.toList());
            List<MnjxLuggage> otherLuggageList = luggages.stream()
                    .filter(l -> !StrUtil.equalsAny(l.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(otherLuggageList)) {
                for (MnjxLuggage luggage : otherLuggageList) {
                    // 去除被删除的手工行李
                    if (StrUtil.isNotEmpty(luggage.getIsDel()) && Constant.STR_TWO.equals(luggage.getLuggageType())) {
                        continue;
                    }
                    newLuggages.add(luggage);
                }
                if (CollUtil.isNotEmpty(newLuggages)) {
                    for (int i = 1; i <= newLuggages.size(); i++) {
                        MnjxLuggage luggage = newLuggages.get(i - 1);
                        // 系统行李回显
                        if (Constant.STR_ONE.equals(luggage.getLuggageType())) {
                            luggageSb.append(StrUtil.format("/{}/{}", luggage.getLuggageNo(), luggage.getDst()));
                        }
                        // 手工行李回显
                        else if (Constant.STR_TWO.equals(luggage.getLuggageType())) {
                            String airline = flightNo.substring(0, 2);
                            String luggageNo = luggage.getLuggageNo().substring(4);
                            luggageSb.append(StrUtil.format("/ {} {}/{}", airline, luggageNo, luggage.getDst()));
                        }
                        if (StrUtil.isNotEmpty(luggage.getIsDel())) {
                            luggageSb.append(luggage.getIsDel());
                        }
                        // 换行
                        if (i % 4 == 0) {
                            luggageSb.append(StrUtil.CRLF).append("            ");
                        } else {
                            if (i != luggages.size()) {
                                luggageSb.append(" ");
                            }
                        }
                    }
                }
                if (ObjectUtil.isNotEmpty(luggageSb)) {
                    result.add("     BAGTAG" + luggageSb);
                }
            }
            if (CollUtil.isNotEmpty(avihLuggageList) && !StrUtil.equalsAny(ckiStatus, Constant.CKI_STATUS_NACC, Constant.CKI_STATUS_DL)) {
                newLuggages.clear();
                for (MnjxLuggage luggage : avihLuggageList) {
                    // 去除被删除的AVIH手工行李
                    if (StrUtil.isNotEmpty(luggage.getIsDel()) && Constant.STR_FOUR.equals(luggage.getLuggageType())) {
                        continue;
                    }
                    newLuggages.add(luggage);
                }
                if (CollUtil.isNotEmpty(newLuggages)) {
                    luggageSb = new StringBuilder();
                    for (int i = 1; i <= newLuggages.size(); i++) {
                        MnjxLuggage luggage = newLuggages.get(i - 1);
                        // AVIH系统行李回显
                        if (Constant.STR_THREE.equals(luggage.getLuggageType())) {
                            luggageSb.append(StrUtil.format("/{}/{}", luggage.getLuggageNo(), luggage.getDst()));
                        }
                        // AVIH手工行李回显
                        else if (Constant.STR_FOUR.equals(luggage.getLuggageType())) {
                            String airline = flightNo.substring(0, 2);
                            String luggageNo = luggage.getLuggageNo().substring(4);
                            luggageSb.append(StrUtil.format("/ {} {}/{}", airline, luggageNo, luggage.getDst()));
                        }
                        if (StrUtil.isNotEmpty(luggage.getIsDel())) {
                            luggageSb.append(luggage.getIsDel());
                        }
                        // 换行
                        if (i % 4 == 0) {
                            luggageSb.append(StrUtil.CRLF).append("            ");
                        } else {
                            if (i != luggages.size()) {
                                luggageSb.append(" ");
                            }
                        }
                    }
                }
                if (ObjectUtil.isNotEmpty(luggageSb)) {
                    result.add("     AVIH  " + luggageSb);
                }
            }
        }
    }

    @Override
    public void getPsgOperateRecords(String psgCkiId, List<String> result, String pnrNmId, String dst) {
        // 组装操作记录
        List<MnjxPsgOperateRecord> psgOperateRecords = iMnjxPsgOperateRecordService.lambdaQuery().eq(MnjxPsgOperateRecord::getPsgCkiId, psgCkiId).list();
        if (CollUtil.isEmpty(psgOperateRecords)) {
            return;
        }
        psgOperateRecords.forEach(r -> result.add(StrUtil.format("     {} {}", r.getOperateType(), r.getContent())));
    }

    @Override
    public void autoWrap(StringBuilder sb, List<String> result) {
        int time = 1;
        while (true) {
            if (sb.length() > (70 * time)) {
                int spaceIndex = sb.lastIndexOf(" ", 69 * time);
                sb.insert(spaceIndex, StrUtil.CRLF + "                                       ");
                time += 1;
            } else {
                break;
            }
        }
        result.add(sb.toString());
    }

    @Override
    public String liftCabin(List<MnjxNmSsr> nmSsrs) {
        // 获取升降舱
        List<MnjxNmSsr> liftCabinSsr = nmSsrs.stream()
                .filter(k -> k.getSsrType().contains("UPG") || k.getSsrType().contains("DNG"))
                .collect(Collectors.toList());
        return liftCabinSsr.stream().map(MnjxNmSsr::getSsrType).collect(Collectors.joining(" "));

    }


    @Override
    public void getLuggageWeight(List<MnjxLuggage> luggage, String nowSegNo, StringBuilder sb) {
        List<MnjxLuggage> existLuggageList = luggage.stream()
                .filter(l -> StrUtil.isEmpty(l.getIsDel()))
                .filter(k -> k.getBagSegNo().equals(nowSegNo))
                .collect(Collectors.toList());
        List<MnjxLuggage> weightPoolLuggageList = existLuggageList.stream()
                .filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()))
                .collect(Collectors.toList());
        existLuggageList = existLuggageList.stream()
                .filter(l -> ObjectUtil.isEmpty(l.getWeightPool()))
                .collect(Collectors.toList());
        List<MnjxLuggage> luggageList = new ArrayList<>();
        if (CollUtil.isNotEmpty(existLuggageList)) {
            // 取出所有包含当前航段的、行李组分
            luggageList = existLuggageList.stream()
                    .filter(k -> StrUtil.isEmpty(k.getLuggageGroupId()))
                    .collect(Collectors.toList());
            // 取出同组行李，去除重复，只计算一次重量，数量要单独计算
            List<MnjxLuggage> luggageGroup = existLuggageList.stream()
                    .filter(k -> StrUtil.isNotEmpty(k.getLuggageGroupId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(luggageGroup)) {
                luggageList.addAll(luggageGroup.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MnjxLuggage::getLuggageGroupId))), ArrayList::new)
                ));
            }
        }
        int luggageWeight = 0;
        int luggageSize = 0;
        int avihWeight = 0;
        int avihSize = 0;
        // 计算一般行李的重量和件数
        if (CollUtil.isNotEmpty(luggageList)) {
            // 行李重量
            luggageWeight += luggageList.stream()
                    .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                    .mapToInt(MnjxLuggage::getLuggageWeight)
                    .sum();
            avihWeight += luggageList.stream()
                    .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                    .mapToInt(MnjxLuggage::getLuggageWeight)
                    .sum();
            // 行李数量
            luggageSize += existLuggageList.stream()
                    .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                    .filter(k -> k.getBagSegNo().equals(nowSegNo))
                    .count();
            avihSize += existLuggageList.stream()
                    .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                    .filter(k -> k.getBagSegNo().equals(nowSegNo))
                    .count();
        }
        // 计算转换为重量池的行李重量和件数
        if (CollUtil.isNotEmpty(weightPoolLuggageList)) {
            if (weightPoolLuggageList.stream().anyMatch(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))) {
                luggageWeight += weightPoolLuggageList.stream()
                        .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                        .collect(Collectors.toList())
                        .get(0)
                        .getWeightPool();
                luggageSize += weightPoolLuggageList.stream()
                        .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_ONE, Constant.STR_TWO))
                        .count();
            }
            if (weightPoolLuggageList.stream().anyMatch(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))) {
                avihWeight += weightPoolLuggageList.stream()
                        .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                        .collect(Collectors.toList())
                        .get(0)
                        .getWeightPool();
                avihSize += weightPoolLuggageList.stream()
                        .filter(l -> StrUtil.equalsAny(l.getLuggageType(), Constant.STR_THREE, Constant.STR_FOUR))
                        .count();
            }
        }
        if (StrUtil.isNotEmpty(sb)) {
            sb.append(StrUtil.format(" BAG{}/{}/0", luggageSize, luggageWeight));
        } else {
            sb.append(StrUtil.format("                                        BAG{}/{}/0", luggageSize, luggageWeight));
        }
        if (avihSize > 0) {
            sb.append(StrUtil.format(" AVIH{}/{}", avihSize, avihWeight));
        }
    }

    @Override
    @CachePut(key = "'prResult' + #memoryData.memoryDataId")
    public PdInfoDto putPrCache(MemoryData memoryData, PrResultDto prResultDto) throws UnifiedResultException {
        return ObjectUtil.isNotEmpty(prResultDto.getPdInfoDto()) ? prResultDto.getPdInfoDto() : this.buildPdInfoDto(prResultDto.getPrDtos().get(0), prResultDto.getPrDtos());
    }

    @Override
    public PdInfoDto buildPdInfoDto(PrDto prDto, List<PrDto> prDtos) throws UnifiedResultException {
        PdInfoDto pdInfoDto = new PdInfoDto();
        List<MnjxPlanFlight> planFlights = pdMapper.retrieveFlightByDate(prDto.getFlightNo(), prDto.getFlightDate());
        if (CollUtil.isEmpty(planFlights)) {
            planFlights = pdMapper.retrieveFlightByDate(prDto.getCarrierFlight(), prDto.getFlightDate());
        }
        if (CollUtil.isEmpty(planFlights)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        MnjxCnd cnd = iMnjxCndService.lambdaQuery().eq(MnjxCnd::getCndNo, planFlights.get(0).getCndNo()).one();

        MnjxPlaneModel planeModel = iMnjxPlaneModelService.lambdaQuery()
                .eq(MnjxPlaneModel::getPlaneModelId, cnd.getPlaneModelId()).one();
        if (ObjectUtil.isNotNull(planeModel)) {
            pdInfoDto.setPlaneType(planeModel.getPlaneModelType() + "/" + planeModel.getPlaneModelVersion());
        }
        List<MnjxPlanSection> planSections = mnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlights.get(0).getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getPlanFlightId).orderByAsc(MnjxPlanSection::getIsLastSection)
                .orderByAsc(MnjxPlanSection::getEstimateOffChange).orderByAsc(MnjxPlanSection::getEstimateOff).list();
        if (CollUtil.isNotEmpty(planSections)) {
            MnjxPlanSection mnjxPlanSection = planSections.get(0);
            pdInfoDto.setGtd("GTD/" + mnjxPlanSection.getGate());
        } else {
            pdInfoDto.setGtd("GTD/????");
        }
        BeanUtil.copyProperties(prDto, pdInfoDto);
        pdInfoDto.setDate(prDto.getFlightDate());
        pdInfoDto.setCity(prDto.getOrg());
        if (CollUtil.isNotEmpty(prDtos)) {
            List<PdNmDto> nms = prDtos.stream().map(k -> {
                PdNmDto pdNmDto = new PdNmDto();
                pdNmDto.setShareFlight(prDto.getFlightNo());
                BeanUtil.copyProperties(k, pdNmDto);
                return pdNmDto;
            }).collect(Collectors.toList());
            pdInfoDto.setNms(nms);
        }
        return pdInfoDto;
    }

    /**
     * 处理联程额外占座
     *
     * @param pr pr
     * @return 处理联程额外占座
     */
    public String getInterlineAdditionalSeats(PrDto pr) {
        String psgCkiId = pr.getPsgCkiId();
        // 婴儿摇篮、客舱宠物
        List<MnjxPsgCkiOption> psgCkiOptions = iMnjxPsgCkiOptionService.lambdaQuery().eq(MnjxPsgCkiOption::getPsgCkiId, psgCkiId).list();
        // 额外占座
        List<String> exstList = Arrays.asList("EXST", "CBBG", "COUR", "DIPL");
        List<MnjxPsgCkiOption> exstPsgCikList = psgCkiOptions.stream().filter(k -> exstList.contains(k.getOptionType())).collect(Collectors.toList());
        if (StrUtil.isNotEmpty(pr.getSeatExst()) && CollUtil.isNotEmpty(exstPsgCikList)) {
            // 额外占座
            String seatExst = StrUtil.fill(StrUtil.emptyToDefault(pr.getSeatExst(), ""), ' ', 4, false);
            MnjxPsgCkiOption ckiOption = exstPsgCikList.get(0);
            if (StrUtil.isNotEmpty(ckiOption.getOptionValue())) {
                return StrUtil.format("                                 {}   {}{}", seatExst, ckiOption.getOptionType(), ckiOption.getOptionValue());
            } else {
                return StrUtil.format("                                 {}   {}", seatExst, ckiOption.getOptionType());
            }
        }
        return null;
    }

    @Override
    public List<MnjxNmSsr> getCurrentOrThoroughfareSsr(List<MnjxNmSsr> pnrSsrList, String pnrNmId, String segNo) {
        if (CollUtil.isEmpty(pnrSsrList)) {
            pnrSsrList = iMnjxNmSsrService.lambdaQuery().eq(MnjxNmSsr::getPnrNmId, pnrNmId).list();
            if (CollUtil.isEmpty(pnrSsrList)) {
                return pnrSsrList;
            }
        }
        List<MnjxNmSsr> resultSsr = new ArrayList<>();
        // 通程SSR
        List<MnjxNmSsr> thoroughfareSsr = pnrSsrList.stream().filter(k -> ObjectUtil.isEmpty(k.getPnrSegNo())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(thoroughfareSsr)) {
            resultSsr.addAll(thoroughfareSsr);
        }
        // 当前航段SSR
        Integer pnrSegNo = Integer.parseInt(segNo);
        List<MnjxNmSsr> segNoSsr = pnrSsrList.stream().filter(k -> ObjectUtil.isNotEmpty(k.getPnrSegNo()) && k.getPnrSegNo().equals(pnrSegNo)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(segNoSsr)) {
            resultSsr.addAll(segNoSsr);
        }
        return resultSsr;
    }

    @Override
    public void getCarrierFlight(PrDto pr, List<String> result) {
        if (StrUtil.isNotEmpty(pr.getCarrierFlight())) {
            result.add(StrUtil.format("MARKETING FLIGHT:{} FARE CLASS:{}", pr.getFlightNo(), pr.getSellCabin()));
        }
    }

    @Override
    public void restoreInit(String pnrNmId, String seatNo, String flightNo, String flightDate) {
        MnjxConfig config = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "RESTORE")
                .one();
        if (ObjectUtil.isNotEmpty(config) && Constant.STR_ONE.equals(config.getAvailable())) {
            HbprRestoreDto hbprRestoreDto = new HbprRestoreDto();
            hbprRestoreDto.setPnrNmId(pnrNmId);
            List<MnjxPlanSection> planSectionList = prMapper.retrievePlanSection(flightNo, flightDate);
            hbprRestoreDto.setPlanSectionIdList(planSectionList.stream().map(MnjxPlanSection::getPlanSectionId).collect(Collectors.toList()));
            hbprRestoreDto.setSeatNo(StrUtil.isEmpty(seatNo) ? "" : seatNo.trim());
            iAspectCkiService.restoreHbprInitialData(hbprRestoreDto);
        }
    }

    @Override
    public void savePrForPrint(List<PrDto> prs, String cmd) {
        PrDto prDto = prs.get(0);
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < prs.size(); i++) {
            PrDto pr = prs.get(i);
            if (StrUtil.equalsAny(pr.getCkiStatus(), Constant.CKI_STATUS_ACC, Constant.CKI_STATUS_GT)) {
                map.put(StrUtil.toString(i + 1), "BN" + pr.getAboardNo());
            }
        }
        if (MapUtil.isNotEmpty(map)) {
            String printKey = StrUtil.format("{}/{}/{}", prDto.getFlightNo(), DateUtils.ymd2Com(prDto.getFlightDate()), cmd);
            MnjxPrintData dbPrintData = iMnjxPrintDataService.lambdaQuery()
                    .eq(MnjxPrintData::getPrintKey, printKey)
                    .one();
            if (ObjectUtil.isNotNull(dbPrintData)) {
                dbPrintData.setPrintValue(JSONUtil.toJsonStr(map));
                iMnjxPrintDataService.updateById(dbPrintData);
            } else {
                MnjxPrintData mnjxPrintData = new MnjxPrintData();
                mnjxPrintData.setPrintKey(printKey);
                mnjxPrintData.setPrintValue(JSONUtil.toJsonStr(map));
                iMnjxPrintDataService.save(mnjxPrintData);
            }
        }
    }
}

package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.eterm.dcs.cki.mapper.SeiMapper;
import com.swcares.eterm.dcs.cki.obj.dto.SemDto;
import com.swcares.eterm.dcs.cki.service.ISeiService;
import com.swcares.eterm.dcs.fdc.dto.ExDto;
import com.swcares.eterm.dcs.fdc.dto.ExParamDto;
import com.swcares.eterm.dcs.fdc.service.IExService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SeiServiceImpl implements ISeiService {

    @Resource
    private SeiMapper seiMapper;

    @Resource
    private IExService iExService;

    /**
     * SEI:1 SEI:123
     */
    private static final String SEI_REG = "SEI:[A-Za-z0-9]+";

    /**
     * SEM:CA/303/123 SEM:3U/100/123
     */
    private static final String SEM_REG = "SEM:[0-9A-Za-z]{2}/[0-9A-Za-z]{3,}/[0-9A-Za-z]{3,}";

    private SemDto dealSei(String obj) throws UnifiedResultException {
        SemDto dto = new SemDto();
        if (obj.matches(SEI_REG)) {
            String[] cmdStrs = obj.split(":");
            // 如果输入非数字或者不存在的序号报错ENTRY NBR
            int exNum;
            try {
                exNum = Integer.parseInt(cmdStrs[1]);
            } catch (Exception e) {
                throw new UnifiedResultException(Constant.ENTRY_NBR);
            }
            if (exNum == 0) {
                throw new UnifiedResultException(Constant.ENTRY_NBR);
            }
            // 获取缓存数据
            MemoryData memoryData = MemoryDataUtils.getMemoryData();
            // 获取EX指令缓存
            ExParamDto exParamDto = iExService.parseEx(memoryData);
            // 如果前面没有有效的AV指令及查询结果，报错：**NO DISPLAY**
            if (ObjectUtil.isEmpty(exParamDto)) {
                throw new UnifiedResultException(Constant.NO_DISPLAY_SYMBOL);
            }
            String airlineCode = exParamDto.getAirlineCode();
            List<ExDto> exDtoList = iExService.handle(memoryData);
            ExDto exDto = exDtoList.get(exNum - 1);
            dto.setAirlineCode(airlineCode);
            dto.setModel(exDto.getPlaneModelType().trim());
            dto.setVersion(exDto.getPlaneModelVersion().trim());
            dto.setLayout(exDto.getPreLayout().trim());
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return dto;
    }

    private SemDto dealSem(String cmd) throws UnifiedResultException {
        SemDto dto = new SemDto();
        if (cmd.matches(SEM_REG)) {
            String[] params = cmd.trim().split(":")[1].split("/");
            dto.setAirlineCode(params[0]);
            dto.setModel(params[1]);
            dto.setVersion(params[2]);
        } else {
            String semRegErr1 = "SEM:[0-9A-Za-z]{2}";
            String semRegErr2 = "SEM:[0-9A-Za-z]{2}/[0-9A-Za-z]{3,}";
            if (cmd.matches(semRegErr1) || cmd.matches(semRegErr2)) {
                throw new UnifiedResultException(Constant.AIRCRAFT);
            } else {
                throw new UnifiedResultException(Constant.FORMAT);
            }
        }
        return dto;
    }

    /**
     * 获取飞机号和版本信息
     *
     * @param dto dto
     * @return 获取飞机号和版本信息
     */
    private List<Map<String, Object>> getPlaneType(SemDto dto) {
        List<Map<String, Object>> result = null;
        try {
            String airCode = dto.getAirlineCode();
            String type = dto.getModel();
            String version = dto.getVersion();
            String layout = dto.getLayout();
            result = seiMapper.retrievePlaneType(airCode, type, version, layout);
        } catch (Exception e) {
            log.error("执行SQL错误：{}", e);
        }
        return result;
    }

    @Override
    public String dealCmd(String cmd) throws UnifiedResultException {
        SemDto semDto;
        if (cmd.trim().toUpperCase().startsWith(Constant.SEI_CODE)) {
            semDto = dealSei(cmd);
        } else if (cmd.trim().toUpperCase().startsWith(Constant.SEM_CODE)) {
            semDto = dealSem(cmd);
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        List<Map<String, Object>> list = getPlaneType(semDto);
        if (CollectionUtil.isNotEmpty(list)) {
            // 获取第一个数据
            Map<String, Object> map = list.get(0);
            // 将2020-02-01 转换为 01FEB20
            String purchaseDate = DateUtils
                    .ymd2Com(StrUtils.toString(DateUtil.format((Date) map.get("PURCHASE_DATE"), "yyyy-MM-dd")));
            map.put("PURCHASE_DATE", purchaseDate);
            // 验证是否找到对应的模板
            StringBuilder resultBuffer = new StringBuilder();
            resultBuffer.append("SEM:").append(map.get("AIR_CODE")).append("/").append(map.get("FLT_TYPE")).append("/").append(map.get("FLT_VERSION")).append("/").append(purchaseDate).append("-OPEN");
            resultBuffer.append("\n\r");
            resultBuffer.append(map.get("PLANE_LAYOUT").toString());
            return resultBuffer.toString();
        } else {
            return Constant.AIRCRAFT;
        }
    }
}

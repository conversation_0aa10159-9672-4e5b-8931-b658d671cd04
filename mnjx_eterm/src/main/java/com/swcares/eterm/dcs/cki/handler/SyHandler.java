package com.swcares.eterm.dcs.cki.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.obj.dto.SyDto;
import com.swcares.eterm.dcs.cki.obj.dto.SyResultDto;
import com.swcares.eterm.dcs.cki.service.ISyService;

import javax.annotation.Resource;

/**
 * SY指令处理
 *
 * <AUTHOR>
 */
@OperateType(action = "SY", shorthand = true, template = "dcs/fdc/sy.jf", fullScreen = true)
public class SyHandler implements Handler {

    @Resource
    private ISyService iSyService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        SyDto syDto = iSyService.parseSy(cmd);
        SyResultDto syResultDto = iSyService.handle(syDto, unifiedResult);
        unifiedResult.setResults(new Object[]{syDto.getOption(), syResultDto});
        return unifiedResult;
    }
}

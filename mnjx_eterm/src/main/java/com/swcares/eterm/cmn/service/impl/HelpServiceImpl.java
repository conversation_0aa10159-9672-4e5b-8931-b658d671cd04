package com.swcares.eterm.cmn.service.impl;

import com.swcares.entity.MnjxOrder;
import com.swcares.eterm.cmn.service.IHelpService;
import com.swcares.service.IMnjxOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HELP处理 Service 功能帮助
 *
 * <AUTHOR>
 */
@Service
public class HelpServiceImpl implements IHelpService {

    @Resource
    private IMnjxOrderService iMnjxOrderService;

    @Override
    public Map<String, Object> dealCmd(String[] tmp) {
        Map<String, Object> map = new HashMap<>(1024);
        String orderName = tmp[1];

        List<MnjxOrder> mnjxOrders = iMnjxOrderService.lambdaQuery().eq(MnjxOrder::getOrderName, orderName).list();
        if (mnjxOrders.isEmpty()) {
            map.put("key", "请检查输入");
        }
        return map;
    }
}

package com.swcares.eterm.cmn.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxPnr;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ISoService {

    /**
     * retrievePnr
     *
     * @param pnrId pnrId
     * @return retrievePnr
     */
    List<MnjxPnr> retrievePnr(String pnrId);

    /**
     * 用户退出
     *
     * @return
     * @throws UnifiedResultException
     */
    Map<String, Object> handle() throws UnifiedResultException;
}

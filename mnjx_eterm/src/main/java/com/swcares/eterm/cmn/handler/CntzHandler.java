package com.swcares.eterm.cmn.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.cmn.dto.CntzResultDto;
import com.swcares.eterm.cmn.dto.CntzRetrieveDto;
import com.swcares.eterm.cmn.service.ICntzService;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 16:35
 */
@OperateType(action = "CNTZ", template = "cmn/cntz.jf")
public class CntzHandler implements Handler {

    @Resource
    private ICntzService iCntzService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException, IOException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        CntzRetrieveDto cntzRetrieveDto = iCntzService.parseCmd(cmd);
        List<CntzResultDto> resultList = iCntzService.handle(cntzRetrieveDto, unifiedResult);
        unifiedResult.setResults(resultList.toArray());
        return unifiedResult;
    }
}

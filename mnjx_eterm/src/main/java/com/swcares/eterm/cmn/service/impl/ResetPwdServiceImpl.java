package com.swcares.eterm.cmn.service.impl;

import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxConfig;
import com.swcares.entity.MnjxOffice;
import com.swcares.entity.MnjxSi;
import com.swcares.eterm.cmn.service.IResetPwdService;
import com.swcares.service.IMnjxConfigService;
import com.swcares.service.IMnjxSiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/4/18 15:48
 */
@Slf4j
@Service
public class ResetPwdServiceImpl implements IResetPwdService {

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxConfigService iMnjxConfigService;

    @Override
    public boolean resetPwd() throws UnifiedResultException {
        MnjxConfig config = iMnjxConfigService.lambdaQuery()
                .eq(MnjxConfig::getType, "ENABLE_RESET_PASSWORD")
                .one();
        if (Constant.STR_ONE.equals(config.getAvailable())) {
            MnjxOffice mnjxOffice = MemoryDataUtils.getMemoryData().getMnjxOffice();
            log.info("重置Office {} 下工作号的密码", mnjxOffice.getOfficeNo());
            return iMnjxSiService.lambdaUpdate()
                    .eq(MnjxSi::getOfficeId, mnjxOffice.getOfficeId())
                    .set(MnjxSi::getSiPassword, "f379eaf3c831b04de153469d1bec345e")
                    .update();
        } else {
            throw new UnifiedResultException(Constant.AUTHORITY);
        }
    }
}

package com.swcares.eterm.base.service.impl;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.util.Constant;
import com.swcares.core.util.StrUtils;
import com.swcares.eterm.base.service.IDataUpdateService;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/5/23
 */
@Service
public class DataUpdateServiceImpl implements IDataUpdateService {

    /**
     * 修改姓名  2/1ZHANG/SAN  2/1张叁
     */
    private static final Pattern REG_NM_UPDATE = Pattern.compile("^(\\d+)/1(([\\u4e00-\\u9fa5]+)|([A-Z]+/[A-Z]+))[\\s\\w()]*$");

    @Override
    public String updateNm(String cmd) {
        // 修改姓名  eg. 2/1ZHANG/SAN 将2号旅客姓名改为ZHANG/SAN； 3/1张叁 将3号旅客姓名修改为张叁
        return ReUtil.isMatch(REG_NM_UPDATE, cmd) ? StrUtil.format("NM:{}", cmd) : StrUtils.format("{}({})\r\n", Constant.CMD_NOT_SUPPORT, cmd);
    }
}

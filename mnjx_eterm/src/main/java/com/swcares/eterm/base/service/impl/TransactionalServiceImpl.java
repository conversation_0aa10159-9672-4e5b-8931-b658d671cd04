package com.swcares.eterm.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataCabinet;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.eterm.base.service.ITransactionalOperateService;
import com.swcares.eterm.dcs.cki.obj.dto.PwResultDto;
import com.swcares.eterm.dcs.cki.service.IAspectCkiService;
import com.swcares.eterm.dcs.cki.service.IPwService;
import com.swcares.service.IMnjxNmOsiService;
import com.swcares.service.IMnjxNmSsrService;
import com.swcares.service.IMnjxNmXnService;
import com.swcares.service.IMnjxPnrNmUmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/21 15:07
 */
@Slf4j
@Service
public class TransactionalServiceImpl implements ITransactionalOperateService {

    /**
     * 所有选项
     */
    private static final Pattern REG9 = Pattern.compile("BAG|PSM|BLND|BAG|CHD\\d{1}?|DEAF|O(.+)?|M1|F1|INF1|INF|UM(-?\\d{1,4})?|SPML|STCR|CNIN|WCHC|WCHR"
            + "|WCHS|WCBD|WCBW|WCMP|WCOB|BSCT|PETC"
            + "|((AVIH)?\\d{1,3}/\\d+)|(BT((/\\d{6})+))|(T/\\w{2}(/\\d{6})+)"
            + "|EXBG|EXPC|INAD|ADSR|EXST|CBBG|COUR|DIPL|FF|AVIH|MEDA");

    /**
     * 所有ssr
     */
    private static final Pattern REG10 = Pattern.compile("^DEAF|PSM|STCR|JMP|BLND|WCHC|WCHR|WCHS|WCBD|WCBW|WCMP|WCOB|INAD|BSCT|PETC|ADSR|EXST|CBBG|COUR|DIPL|MEDA$");

    /**
     * -1/-12
     */
    private static final Pattern REG12 = Pattern.compile("^-?\\d{1,3}/-?\\d+$");
    private static final Pattern AVIH_REG12 = Pattern.compile("^AVIH-?\\d{1,3}/-?\\d+$");

    /**
     * bt/123456
     */
    private static final Pattern REG13 = Pattern.compile("^BT(/\\d{6})+$");

    /**
     * t/3u/123456
     */
    private static final Pattern REG14 = Pattern.compile("^T/\\w{2}(/\\d{6})+$");

    /**
     * t/ca/100230
     */
    private static final Pattern REG15 = Pattern.compile("^T/(\\w{2})(/\\d{6})+$");

    /**
     * 0/123
     */
    private static final Pattern REG16 = Pattern.compile("^0/\\d+$");
    private static final Pattern AVIH_REG16 = Pattern.compile("^AVIH0/\\d+$");

    /**
     * EXST（额外占座）,CBBG(乐器占座),COUR（信袋占座）,DIPL（外交信袋占座）
     */
    private static final Pattern REG17 = Pattern.compile("^ADSR|EXST|CBBG|COUR|DIPL$");

    @Resource
    private IAspectCkiService iAspectCkiService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private IMnjxNmOsiService iMnjxNmOsiService;

    @Resource
    private IPwService iPwService;

    @Transactional(rollbackFor = UnifiedResultException.class)
    @Override
    public void executePw(List<PwResultDto> pwResultDtoList) throws UnifiedResultException {
        // 指令检查
        this.checkPwOption(pwResultDtoList);
        // 指令检查
        this.checkPwOptionDb(pwResultDtoList);
        for (int i = 0; i < pwResultDtoList.size(); i++) {
            PwResultDto pwResultDto = pwResultDtoList.get(i);
            List<String> optionList = pwResultDto.getOptionList();
            if (CollUtil.isNotEmpty(optionList)) {
                // 处理选项数
                this.handlePwOption(pwResultDto, i);
            } else {
                iPwService.handlePw(pwResultDto);
            }
            // 返回信息
            iPwService.getReturnData(pwResultDto);
        }
        iPwService.handleLcReData(pwResultDtoList);
    }

    private void checkPwOption(List<PwResultDto> pwResultDtoList) throws UnifiedResultException {
        for (PwResultDto pwResultDto : pwResultDtoList) {
            // 检查选择项
            List<String> optionList = pwResultDto.getOptionList();
            if (CollUtil.isEmpty(optionList)) {
                return;
            }
            if (pwResultDto.isExecPw()) {
                return;
            }
            boolean isWchc = false;
            boolean isWchs = false;
            boolean isWchr = false;
            for (String option : optionList) {
                // 如果输入的选择有O标识，提示“OUTBOUND DELETE UNABLE”
                if ("O".equals(option)) {
                    throw new UnifiedResultException(Constant.OUTBOUND_DELETE_UNABLE);
                } else if (!ReUtil.isMatch(REG9, option)) {
                    // 如果选择项不正确，如“AAAA”,选择项不正确，提示“ITEM”
                    log.error("选项不正确");
                    throw new UnifiedResultException(Constant.ITEM);
                } else if ("WCHC".equals(option)) {
                    isWchc = true;
                } else if ("WCHS".equals(option)) {
                    isWchs = true;
                } else if ("WCHR".equals(option)) {
                    isWchr = true;
                } else if (option.startsWith("AVIH")) {
                    pwResultDto.setAvih(true);
                }
            }
            // 如果输入的选择项同时存在WCHC,WCHS,WCHR，提示“DUP IS FOUND ”
            if (isWchc && isWchs && isWchr) {
                log.error("WCHC,WCHS,WCHR同时存在");
                throw new UnifiedResultException(Constant.DUP_IS_FOUND);
            }
        }
    }

    /**
     * Title：checkChiStatus <br>
     * description：检查选项 <br>
     *
     * @param pwResultDtoList
     * @throws UnifiedResultException <br>
     * <AUTHOR> <br>
     * @date 2022/08/08 <br>
     */
    private void checkPwOptionDb(List<PwResultDto> pwResultDtoList) throws UnifiedResultException {
        pwResultDtoList = pwResultDtoList.stream()
                .sorted(Comparator.comparing(PwResultDto::getPnrSegNo))
                .collect(Collectors.toList());
        for (int k = 0; k < pwResultDtoList.size(); k++) {
            PwResultDto pwResultDto = pwResultDtoList.get(k);
            if (pwResultDto.isExecPw()) {
                return;
            }
            String pnrNmId = pwResultDto.getPnrNmId();
            MnjxPsgCki mnjxPsgCki = pwResultDto.getMnjxPsgCki();
            // 值机状态
            String ckiStatus = mnjxPsgCki.getCkiStatus();
            // 未值机时的旅客操作，没有所谓的拉下操作，只能删除属性
            if (Constant.GT.equalsIgnoreCase(ckiStatus)) {
                throw new UnifiedResultException(Constant.PAX_ALREADY_BOARDED);
            }
            // 检查选择项
            List<String> optionList = pwResultDto.getOptionList();
            if (CollUtil.isEmpty(optionList)) {
                continue;
            }
            List<String> ssrList = new ArrayList<>();
            // 是否检查ssr
            boolean isSsr = true;
            for (int i = 0; i < optionList.size(); i++) {
                String option = optionList.get(i);
                boolean sexBoolean = "M1".equals(option) || "F1".equals(option);
                if (ReUtil.isMatch(REG10, option)) {
                    ssrList.add(option);
                }
                // inf选项是否存在
                if (option.startsWith("INF")) {
                    MnjxNmXn mnjxNmXn = iMnjxNmXnService.lambdaQuery()
                            .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                            .one();
                    if (mnjxNmXn == null) {
                        log.error("不存在INF项");
                        throw new UnifiedResultException(Constant.ITEM_NOT_FOUND);
                    }
                    // um选项是否存在
                } else if (option.startsWith("UM")) {
                    MnjxPnrNmUm mnjxPnrNmUm = iMnjxPnrNmUmService.lambdaQuery()
                            .eq(MnjxPnrNmUm::getPnrNmId, pnrNmId)
                            .one();
                    if (mnjxPnrNmUm == null) {
                        log.error("不存在UM项");
                        throw new UnifiedResultException(Constant.ITEM_NOT_FOUND);
                    }
                    // 如果旅客值机状态为“ACC”，删除性别时，提示“GENDER DELETION REQUIRES PAX DELETION ”。
                } else if (sexBoolean && Constant.ACC.equals(ckiStatus)) {
                    throw new UnifiedResultException(Constant.GENDER_DELETION_REQUIRES_PAX_DELETION);
                    // 行李处理
                } else if (ReUtil.isMatch(REG16, option) || ReUtil.isMatch(AVIH_REG16, option)) {
                    // 0/NN不允许再输入BT/指定行李号
                    if (optionList.stream().anyMatch(o -> o.startsWith("BT/"))) {
                        throw new UnifiedResultException(Constant.BAG_TAG_MISMATCH);
                    }
                    boolean isAvih = option.startsWith("AVIH");
                    if (isAvih) {
                        option = option.substring(4);
                    }
                    // cki中行李总重量
                    Integer totalWeight = isAvih ? pwResultDto.getAvihWeight() : pwResultDto.getBagWeight();
                    String[] bags = option.split("/");
                    // 重量
                    Integer iWeight = Integer.parseInt(bags[1]);
                    // 当前操作航段有多个行李并且目的地不一样 ，不带行李号
                    iPwService.multiSegBagCheck(pwResultDto, null, isAvih);
                    // 如果输入的行李重量等于当前旅客所属行李总重量，提示“CHECK BAG WEIGHT ”。
                    if (iWeight.equals(totalWeight)) {
                        log.error("行李重量超过总重量");
                        throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                    }
                    //如果输入的行李重量大于当前旅客的属性行李的重量，提示“CONFLICT”。
                    if (iWeight > totalWeight) {
                        log.error("行李重量超过总重量");
                        throw new UnifiedResultException(Constant.CONFLICT);
                    }
                } else if (ReUtil.isMatch(REG12, option) || ReUtil.isMatch(AVIH_REG12, option)) {
                    boolean isAvih = option.startsWith("AVIH");
                    if (isAvih) {
                        option = option.substring(4);
                    }
                    String[] bags = option.split("/");
                    // 件数
                    Integer bagNum = Integer.parseInt(bags[0]);
                    // 重量
                    Integer bagWeight = Integer.parseInt(bags[1]);
                    // 行李号
                    String luggageNo = null;
                    if (i + 1 < optionList.size()) {
                        luggageNo = optionList.get(i + 1);
                    }
                    // 当前操作航段有多个行李并且目的地不一样 ，不带行李号
                    iPwService.multiSegBagCheck(pwResultDto, luggageNo, isAvih);
                    // 检查
                    this.checkBag(bagNum, bagWeight, luggageNo, pwResultDto, k, isAvih);
                } else if (StrUtil.equalsAny(option, "BAG", "AVIH")) {
                    boolean isAvih = "AVIH".equals(option);
                    // 当前操作航段有多个行李并且目的地不一样 ，不带行李号
                    iPwService.multiSegBagCheck(pwResultDto, null, isAvih);
                    // 是否存在行李
                    if ((CollUtil.isEmpty(pwResultDto.getCurrentSegLuggageList()) && !isAvih) || (CollUtil.isEmpty(pwResultDto.getCurrentSegAvihLuggageList()) && isAvih)) {
                        log.error("旅客没有行李");
                        throw new UnifiedResultException(Constant.ITEM_NOT_FOUND);
                    }
                } else if (option.startsWith("SPML")) {
                    // spml
                    isSsr = false;
                } else if (Constant.EX_EXBG.equals(option) || Constant.EX_EXPC.equals(option)) {
                    iPwService.checkExbgExpc(option, mnjxPsgCki.getPsgCkiId());
                }
            }
            if (isSsr) {
                // 检查ssr
                boolean ssrCheckRes = this.checkSsr(ssrList, pnrNmId);
                if (ssrCheckRes) {
                    throw new UnifiedResultException(Constant.ITEM_NOT_FOUND);
                }
            }
        }
    }

    private void handlePwOption(PwResultDto pwResultDto, int pwResultDtoNo) throws UnifiedResultException {
        // 操作记录
        List<MnjxPsgOperateRecord> mporList = new ArrayList<>();
        String pnrNmId = pwResultDto.getPnrNmId();
        String psgCkiId = pwResultDto.getMnjxPsgCki().getPsgCkiId();
        List<String> ssrList = new ArrayList<>();
        List<String> optionList = pwResultDto.getOptionList();
        if (pwResultDto.isExecPw()) {
            return;
        }
        if (CollUtil.isEmpty(optionList)) {
            log.error("选项集合为空");
            throw new UnifiedResultException(Constant.NO_ACTION_INDICATED);
        }
        for (int i = 0; i < optionList.size(); i++) {
            String option = optionList.get(i);
            if (ReUtil.isMatch(REG10, option)) {
                ssrList.add(option);
            }
            if ("FF".equals(option)) {
                ssrList.add("FQTV");
            } else if ("INF".equals(option)) {
                // 删除SSR类型为INFT的SSR数据
                ssrList.add("INFT");
                // 删除当前旅客携带的婴儿数据
                MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                        .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                        .one();
                iAspectCkiService.deleteNmXn(nmXn, pnrNmId);
            } else if ("UM".equals(option)) {
                // 删除SSR类型为UMNR的SSR数据。
                ssrList.add("UMNR");
                // 删除当前旅客所对应的关联关系即根据旅客ID删除表mnjx_pnr_nm_um中的数据。
                MnjxPnrNmUm nmUm = iMnjxPnrNmUmService.lambdaQuery()
                        .eq(MnjxPnrNmUm::getPnrNmId, pnrNmId)
                        .one();
                iAspectCkiService.deleteNmUm(nmUm, pnrNmId);
            } else if ("BSCT".equals(option)) {
                // 删除BSCT
                iPwService.delCkiOption(psgCkiId, option, pnrNmId);
            } else if ("PETC".equals(option)) {
                // 删除PETC
                iPwService.delCkiOption(psgCkiId, option, pnrNmId);
            } else if (StrUtil.equalsAny(option, "BAG", "AVIH")) {
                // 只执行了BAG,删除所有非AVIH的行李；只执行AVIH，删除所有非BAG的行李
                MnjxPsgOperateRecord mporBag = iPwService.delBag(pwResultDto, pnrNmId, psgCkiId, true, "AVIH".equals(option));
                mporList.add(mporBag);
            } else {
                boolean isAvih = option.startsWith("AVIH");
                if (ReUtil.isMatch(REG16, option) || ReUtil.isMatch(AVIH_REG16, option)) {
                    if (isAvih) {
                        option = option.substring(4);
                    }
                    String[] strLuggage = option.split("/");
                    int iWeight = Integer.parseInt(strLuggage[1]);
                    if (iWeight > 0) {
                        // 删除行李
                        iPwService.delBag(iWeight, isAvih ? pwResultDto.getCurrentSegAvihLuggageList() : pwResultDto.getCurrentSegLuggageList());
                        MnjxPsgOperateRecord mporBag = this.createOperateRecord(psgCkiId, null);
                        mporBag.setContent(mporBag.getContent() + "/T");
                        mporList.add(mporBag);
                    }
                } else if (ReUtil.isMatch(REG12, option) || ReUtil.isMatch(AVIH_REG12, option)) {
                    if (isAvih) {
                        option = option.substring(4);
                    }
                    String[] strLuggage = option.split("/");
                    Integer iWeight = Integer.parseInt(strLuggage[1]);
                    if (iWeight <= 0) {
                        throw new UnifiedResultException(Constant.BAG);
                    }
                    String iLuggageNo = null;
                    if (i + 1 < optionList.size()) {
                        // T/3u/123456
                        if (ReUtil.isMatch(REG14, optionList.get(i + 1))) {
                            iLuggageNo = optionList.get(i + 1).substring(5);
                        }
                        // BT/759241
                        if (ReUtil.isMatch(REG13, optionList.get(i + 1))) {
                            iLuggageNo = optionList.get(i + 1).substring(3);
                        }
                    }
                    // 删除行李，返回操作记录对象
                    MnjxPsgOperateRecord mporBag;
                    if (StrUtil.isNotEmpty(iLuggageNo)) {
                        mporBag = iPwService.delBag(pwResultDto, iLuggageNo, iWeight, true, pwResultDtoNo, isAvih);
                    } else {
                        //删除全部行李
                        mporBag = iPwService.delBag(pwResultDto, pnrNmId, psgCkiId, true, isAvih);
                    }
                    mporList.add(mporBag);
                } else if (option.startsWith("SPML")) {
                    ssrList.remove("SPML");
                    // 查询出旅客下面所有的特餐
                    List<MnjxNmSsr> mnjxNmSsrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                            .list();
                    if (CollUtil.isNotEmpty(mnjxNmSsrList)) {
                        for (MnjxNmSsr mnjxNmSsr : mnjxNmSsrList) {
                            if (Constant.SPML_MAP.containsKey(mnjxNmSsr.getSsrType())) {
                                ssrList.add(mnjxNmSsr.getSsrType());
                            }
                        }
                    }
                } else if (option.startsWith("PSM")) {
                    // 删除osi中内容
                    List<MnjxNmOsi> nmOsiList = iMnjxNmOsiService.lambdaQuery()
                            .eq(MnjxNmOsi::getPnrNmId, pnrNmId)
                            .eq(MnjxNmOsi::getPnrOsiType, "VIP")
                            .list();
                    iAspectCkiService.deleteNmOsiList(nmOsiList, pnrNmId);
                } else if (Constant.EX_EXBG.equals(option) || Constant.EX_EXPC.equals(option)) {
                    iPwService.delExbgExpc(option, psgCkiId, pnrNmId);
                } else if (ReUtil.isMatch(REG17, option)) {
                    iPwService.handleCkiOption(psgCkiId, option, pwResultDto);
                }
            }
        }
        // 如果输入选择项为SPML,DEAF,BLND,WCHC,WCHR,WCHS，表示删除当前旅客SSR类型
        if (CollUtil.isNotEmpty(ssrList)) {
            iPwService.delSsr(pnrNmId, ssrList);
            // 处理特殊的ssr
            for (String ssr : ssrList) {
                if ("INFT".equals(ssr)) {
                    ssr = "INF";
                } else if ("UMNR".equals(ssr)) {
                    ssr = "UM";
                }
                if (!"ADSR".equals(ssr)) {
                    mporList.add(this.createOperateRecord(psgCkiId, ssr));
                }
            }
        }
        // 新增操作记录
        this.addOperateRecord(mporList, null, pnrNmId);
    }

    /**
     * Title：createOperateRecord <br>
     * description：创建操作记录对象 <br>
     *
     * @param psgCkiId mnjx_psg_cki表主键
     * @param content  内容
     * @return <br>
     * <AUTHOR> <br>
     * @date 2022/08/13 <br>
     */
    private MnjxPsgOperateRecord createOperateRecord(String psgCkiId, String content) {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataCabinet activeMemoryDataCabinet = memoryData.getMemoryDataContainer().getActiveMemoryDataCabinet();
        MnjxSi mnjxSi = activeMemoryDataCabinet.getMnjxSi();
        MnjxOffice mnjxOffice = memoryData.getMnjxOffice();
        // 主要是记录操作终端、操作工作号和操作时间。格式为：当前office的城市3字码+PID号 AGT+当前操作的工作号/当前操作日期 /【登机号】 【选择项】
        String officeCity = mnjxOffice.getOfficeNo().substring(0, 3);
        String pid = mnjxSi.getSiPid();
        String siNo = mnjxSi.getSiNo();
        String date = DateUtils.ymd2Com(DateUtil.today()).substring(0, 5);
        String time = DateUtils.ymdhms2hm(DateUtil.now());
        String temp = officeCity + pid + " AGT" + siNo + "/" + date + time;
        if (StrUtil.isNotEmpty(content)) {
            temp = temp + "/" + content;
        }

        MnjxPsgOperateRecord mpor = new MnjxPsgOperateRecord();
        mpor.setPsgCkiId(psgCkiId);
        mpor.setOperateType("MOD");
        mpor.setOperator(mnjxOffice.getOfficeNo());
        mpor.setContent(temp);
        mpor.setOperateTime(new Date());
        return mpor;
    }

    /**
     * Title：addOperateRecord <br>
     * description：新增操作记录<br>
     *
     * @param list
     * @param mpor <br>
     * <AUTHOR> <br>
     * @date 2022/08/13 <br>
     */
    private void addOperateRecord(List<MnjxPsgOperateRecord> list, MnjxPsgOperateRecord mpor, String pnrNmId) {
        if (CollUtil.isNotEmpty(list)) {
            iAspectCkiService.insertPsgOperateRecordList(list, pnrNmId);
        }
        if (mpor != null) {
            iAspectCkiService.insertPsgOperateRecordList(Collections.singletonList(mpor), pnrNmId);
        }
    }

    /**
     * Title：checkBag <br>
     * description：行李检查 <br>
     *
     * @param bagNum    件数
     * @param bagWeight 重量
     * @param luggageNo 行李号
     * @throws UnifiedResultException <br>
     * <AUTHOR> <br>
     * @date 2022/08/12 <br>
     */
    private void checkBag(Integer bagNum, Integer bagWeight, String luggageNo, PwResultDto pwResultDto, int k, boolean isAvih) throws UnifiedResultException {
        // 如果行李件数,为0，提示“CHECK BAG WEIGHT”
        if (bagNum == 0) {
            log.error("行李件数为0");
            throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
        }
        // 如果行李件数不为正整数,提示“BAG”
        // 如果输入的行李重量为非正整数，提示“BAG”
        if (bagNum < 0 || bagWeight < 0) {
            log.error("行李件数或者行李重量为负数");
            throw new UnifiedResultException(Constant.BAG);
        }
        if (StrUtil.isNotEmpty(luggageNo)) {
            // 如果输入1,1/10,BT/,提示“BAGTAG”。表示”需要行李牌“。
            // 如果输入1,1/10,BT/75924，提示“BAGTAG”。
            String[] tstrluggageNo = luggageNo.split("/");
            List<String> luggageNoList;
            if (ReUtil.isMatch(REG13, luggageNo)) {
                luggageNoList = Arrays.asList(ArrayUtil.sub(tstrluggageNo, 1, tstrluggageNo.length));
            } else if (ReUtil.isMatch(REG14, luggageNo)) {
                luggageNoList = Arrays.asList(ArrayUtil.sub(tstrluggageNo, 2, tstrluggageNo.length));
            } else if (ReUtil.isMatch(REG15, luggageNo)) {
                luggageNoList = Arrays.asList(ArrayUtil.sub(tstrluggageNo, 2, tstrluggageNo.length));
            } else {
                throw new UnifiedResultException(Constant.BAGTAG);
            }
            // 输入的行李件数和行李号数量不匹配
            if (luggageNoList.size() != bagNum) {
                throw new UnifiedResultException(Constant.CONFLICT);
            }
            // 当前段没被删掉的所有行李
            List<MnjxLuggage> currentSegLuggageList = isAvih ? pwResultDto.getCurrentSegAvihLuggageList() : pwResultDto.getCurrentSegLuggageList();
            // 筛选输入行李号涉及的行李
            List<MnjxLuggage> mnjxLuggageList = currentSegLuggageList.stream()
                    .filter(l -> luggageNoList.contains(l.getLuggageNo().substring(l.getLuggageNo().length() - 6)))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(mnjxLuggageList)) {
                // 输入的行李号在其他航段组都没有的时候跳过
                // 在第一个航段时报错
                if (k == 0) {
                    throw new UnifiedResultException(Constant.ITEM_NOT_FOUND);
                }
            }
            // 输入的行李号包含了被删掉的行李号且在第一个航段时，报错
            else if (k == 0 && mnjxLuggageList.size() != luggageNoList.size()) {
                throw new UnifiedResultException(Constant.CONFLICT);
            }
            // 标识输入的行李编号是不是包含要删除的组合行李中所有的行李
            boolean isContainsAllLuggages = false;
            // 被删除的行李是否属于重量池
            boolean isWeightPool = mnjxLuggageList.stream().anyMatch(l -> ObjectUtil.isNotEmpty(l.getWeightPool()));
            // 只输入了一个行李号
            if (luggageNoList.size() == 1) {
                if (isWeightPool) {
                    MnjxLuggage mnjxLuggage = mnjxLuggageList.get(0);
                    // 如果删除重量大于重量池，报错
                    if (bagWeight > mnjxLuggage.getWeightPool()) {
                        throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                    }
                    // 如果重量池内还有其他行李号，删除重量等于重量池，报错
                    if (bagWeight.intValue() == mnjxLuggage.getWeightPool()
                            && currentSegLuggageList.stream().filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()) && mnjxLuggage.getWeightPool().intValue() == l.getWeightPool()).count() > 1) {
                        throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                    }
                    // 如果是重量池中最后一件，且重量不等于重量池，报错
                    if (bagWeight.intValue() != mnjxLuggage.getWeightPool()
                            && currentSegLuggageList.stream().filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()) && mnjxLuggage.getWeightPool().intValue() == l.getWeightPool()).count() == 1) {
                        throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                    }
                } else {
                    MnjxLuggage mnjxLuggage = mnjxLuggageList.get(0);
                    // 删除的是单件行李
                    if (StrUtil.isEmpty(mnjxLuggage.getLuggageGroupId())) {
                        // 判断重量是否匹配，不匹配报错：THE ACTUAL WEIGHT IS 11KG
                        if (!bagWeight.equals(mnjxLuggage.getLuggageWeight())) {
                            throw new UnifiedResultException(Constant.THE_ACTUAL_WEIGHT_IS + " " + mnjxLuggage.getLuggageWeight() + " KG");
                        }
                    }
                    // 删除的是多件行李
                    else {
                        if (bagWeight > mnjxLuggage.getLuggageWeight()) {
                            throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                        }
                        // 组内还有其他行李，删除重量等于组重量，报错
                        if (bagWeight.intValue() == mnjxLuggage.getLuggageWeight() && currentSegLuggageList.stream().filter(l -> mnjxLuggage.getLuggageGroupId().equals(l.getLuggageGroupId())).count() > 1) {
                            throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                        }
                    }
                }
            }
            // 输入多个行李号
            else {
                // 包含重量池时，只需要检查总件数和总重量
                if (isWeightPool) {
                    // 输入的行李号不全是重量池的行李时，检查的重量和件数要全部加起来
                    List<MnjxLuggage> notPoolLuggageList = mnjxLuggageList.stream()
                            .filter(l -> ObjectUtil.isEmpty(l.getWeightPool()))
                            .collect(Collectors.toList());
                    List<MnjxLuggage> poolLuggageList = mnjxLuggageList.stream()
                            .filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()))
                            .collect(Collectors.toList());
                    // 当前航段重量池所有行李件数
                    int dbBagNum = (int) currentSegLuggageList.stream()
                            .filter(l -> ObjectUtil.isNotEmpty(l.getWeightPool()) && poolLuggageList.get(0).getWeightPool().equals(l.getWeightPool()))
                            .count();
                    // 当前航段重量池的重量
                    int dbBagWeight = poolLuggageList.get(0).getWeightPool();
                    // 不全是重量池，要加上非重量池行李号的件数和总重量
                    if (CollUtil.isNotEmpty(notPoolLuggageList)) {
                        dbBagNum += notPoolLuggageList.size();
                        // 非重量池的行李重量分为单件和组合，分开加算
                        List<MnjxLuggage> notPoolSingleLuggageList = notPoolLuggageList.stream()
                                .filter(l -> StrUtil.isEmpty(l.getLuggageGroupId()))
                                .collect(Collectors.toList());
                        List<MnjxLuggage> notPoolGroupLuggageList = notPoolLuggageList.stream()
                                .filter(l -> StrUtil.isNotEmpty(l.getLuggageGroupId()))
                                .collect(Collectors.toList());
                        // 单件行李的部分直接加重量
                        if (CollUtil.isNotEmpty(notPoolSingleLuggageList)) {
                            dbBagWeight += notPoolSingleLuggageList.stream()
                                    .mapToInt(MnjxLuggage::getLuggageWeight)
                                    .sum();
                        }
                        // 组合行李如果有多个组合要分开加重量
                        if (CollUtil.isNotEmpty(notPoolGroupLuggageList)) {
                            Map<String, List<MnjxLuggage>> groupIdLuggageList = notPoolGroupLuggageList.stream()
                                    .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId));
                            for (Map.Entry<String, List<MnjxLuggage>> entry : groupIdLuggageList.entrySet()) {
                                dbBagWeight += entry.getValue().get(0).getLuggageWeight();
                            }
                        }
                    }
                    // 输入的件数超过总行李件数
                    if (bagNum > dbBagNum) {
                        throw new UnifiedResultException(Constant.CONFLICT);
                    }
                    // 输入的重量查过总行李重量
                    if (bagWeight > dbBagWeight) {
                        throw new UnifiedResultException(Constant.THE_ACTUAL_WEIGHT_IS + " " + dbBagWeight + " KG");
                    }
                    // 输入的重量等于总行李重量，但是件数小于总行李件数
                    else if (bagWeight == dbBagWeight && bagNum < dbBagNum) {
                        throw new UnifiedResultException(Constant.CONFLICT);
                    }
                    // 件数相等但重量不等，报错
                    else if (bagWeight != dbBagWeight && bagNum == dbBagNum) {
                        throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                    }
                } else {
                    // 每个行李号都是删除的单件行李
                    if (mnjxLuggageList.stream().noneMatch(l -> StrUtil.isNotEmpty(l.getLuggageGroupId()))) {
                        int sumWeight = mnjxLuggageList.stream()
                                .mapToInt(MnjxLuggage::getLuggageWeight)
                                .sum();
                        if (bagWeight != sumWeight) {
                            throw new UnifiedResultException(Constant.THE_ACTUAL_WEIGHT_IS + " " + sumWeight + " KG");
                        }
                    }
                    // 每个行李号都是删除的组合行李
                    else if (mnjxLuggageList.stream().noneMatch(l -> StrUtil.isEmpty(l.getLuggageGroupId()))) {
                        Map<String, List<MnjxLuggage>> map = mnjxLuggageList.stream()
                                .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId));
                        Set<Map.Entry<String, List<MnjxLuggage>>> entries = map.entrySet();
                        // 行李号列表涉及多个组合行李，将多个组合行李重组到一起，视为重量池，判断总的件数和重量
                        if (entries.size() > 1) {
                            List<String> groupIdList = mnjxLuggageList.stream()
                                    .map(MnjxLuggage::getLuggageGroupId)
                                    .distinct()
                                    .collect(Collectors.toList());
                            int groupCount = (int) currentSegLuggageList.stream()
                                    .filter(l -> groupIdList.contains(l.getLuggageGroupId()))
                                    .count();
                            // 是否删除的多个组合加起来所有的件数
                            if (mnjxLuggageList.size() == groupCount) {
                                isContainsAllLuggages = true;
                            }
                            AtomicInteger sumWeight = new AtomicInteger();
                            entries.forEach(e -> sumWeight.addAndGet(e.getValue().get(0).getLuggageWeight()));
                            // 删除重量大于总重量，报错
                            if (bagWeight > sumWeight.get()) {
                                throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                            }
                            // 重量相等但件数不等，报错
                            if ((!isContainsAllLuggages && bagWeight == sumWeight.get())) {
                                throw new UnifiedResultException(Constant.CONFLICT);
                            }
                            // 件数相等但重量不等，报错
                            else if (isContainsAllLuggages && bagWeight != sumWeight.get()) {
                                throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                            }
                        }
                        // 行李号列表都属于同一个组合行李
                        else {
                            int luggageWeight = mnjxLuggageList.get(0).getLuggageWeight();
                            int groupCount = (int) currentSegLuggageList.stream()
                                    .filter(l -> mnjxLuggageList.get(0).getLuggageGroupId().equals(l.getLuggageGroupId()))
                                    .count();
                            if (mnjxLuggageList.size() == groupCount) {
                                isContainsAllLuggages = true;
                            }
                            // 删除重量大于总重量，报错
                            if (bagWeight > luggageWeight) {
                                throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                            }
                            // 重量相等但件数不等，报错
                            if (!isContainsAllLuggages && bagWeight == luggageWeight) {
                                throw new UnifiedResultException(Constant.CONFLICT);
                            }
                            // 件数相等但重量不等，报错
                            else if (isContainsAllLuggages && bagWeight != luggageWeight) {
                                throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                            }
                        }
                    }
                    // 行李号列表同时有单件和组合行李，将单件和多个组合行李重组到一起，视为重量池，判断总的件数和重量
                    else {
                        List<String> groupIdList = mnjxLuggageList.stream()
                                .map(MnjxLuggage::getLuggageGroupId)
                                .filter(StrUtil::isNotEmpty)
                                .distinct()
                                .collect(Collectors.toList());
                        int groupCount = (int) currentSegLuggageList.stream()
                                .filter(l -> groupIdList.contains(l.getLuggageGroupId()))
                                .count();
                        groupCount += mnjxLuggageList.stream()
                                .filter(l -> StrUtil.isEmpty(l.getLuggageGroupId()))
                                .count();
                        if (mnjxLuggageList.size() == groupCount) {
                            isContainsAllLuggages = true;
                        }
                        // 单件行李的重量总和
                        int sumWeight = mnjxLuggageList.stream()
                                .filter(l -> StrUtil.isEmpty(l.getLuggageGroupId()))
                                .mapToInt(MnjxLuggage::getLuggageWeight)
                                .sum();
                        Map<String, List<MnjxLuggage>> map = mnjxLuggageList.stream()
                                .filter(l -> StrUtil.isNotEmpty(l.getLuggageGroupId()))
                                .collect(Collectors.groupingBy(MnjxLuggage::getLuggageGroupId));
                        Set<Map.Entry<String, List<MnjxLuggage>>> entries = map.entrySet();
                        // 加上组合行李的重量
                        AtomicInteger atomicInteger = new AtomicInteger();
                        entries.forEach(e -> atomicInteger.addAndGet(e.getValue().get(0).getLuggageWeight()));
                        sumWeight += atomicInteger.get();

                        // 删除重量大于总重量，报错
                        if (bagWeight > sumWeight) {
                            throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                        }
                        // 重量相等但件数不等，报错
                        if (!isContainsAllLuggages && bagWeight == sumWeight) {
                            throw new UnifiedResultException(Constant.CONFLICT);
                        }
                        // 件数相等但重量不等，报错
                        else if (isContainsAllLuggages && bagWeight != sumWeight) {
                            throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
                        }
                    }
                }
            }
        } else {
            // 输入件数和总件数不一致或重量不一致，或者联程时有某段行李件数或重量不匹配，报错
            List<MnjxLuggage> luggageList = isAvih ? pwResultDto.getCurrentSegAvihLuggageList() : pwResultDto.getCurrentSegLuggageList();
            Integer dbWeight = isAvih ? pwResultDto.getAvihWeight() : pwResultDto.getBagWeight();
            if (bagNum != luggageList.size()) {
                log.error("多个行李件数和数据库不一致");
                throw new UnifiedResultException(Constant.BAG_TAG_MISMATCH);
            }
            if (bagWeight > dbWeight) {
                throw new UnifiedResultException(Constant.CONFLICT);
            } else if (bagWeight < dbWeight) {
                throw new UnifiedResultException(Constant.CHECK_BAG_WEIGHT);
            }
        }
    }

    private boolean checkSsr(List<String> ssrList, String pnrNmId) {
        if (CollUtil.isEmpty(ssrList)) {
            return false;
        }
        List<MnjxNmSsr> querySsrList = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .list();
        for (String ssr : ssrList) {
            if ("PSM".equals(ssr)) {
                continue;
            }
            boolean isSsr = true;
            for (MnjxNmSsr nmSsr : querySsrList) {
                if (ssr.equals(nmSsr.getSsrType())) {
                    isSsr = false;
                    break;
                }
            }
            if (isSsr) {
                return true;
            }
        }
        return false;
    }
}

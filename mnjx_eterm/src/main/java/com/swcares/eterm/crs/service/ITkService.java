
package com.swcares.eterm.crs.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.dto.TkDto;

/**
 * <AUTHOR> by yaodan
 *  2022-06-28 15:24:33
 */
public interface ITkService {

    /**
     * 返回结果处理
     *
     * @return 最终回显的数据
     */
    String recall();

    /**
     * 业务处理
     *
     * @param tkDto 参数对象
     * @throws UnifiedResultException 统一异常处理
     */
    void handle(TkDto tkDto) throws UnifiedResultException;

    /**
     * 参数解析
     *
     * @param cmd TK指令
     * @return 参数对象
     * @throws UnifiedResultException 统一异常处理
     */
    TkDto parseTk(String cmd) throws UnifiedResultException;
}

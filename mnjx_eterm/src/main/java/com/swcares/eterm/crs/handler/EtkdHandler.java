package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.cki.service.IEtkdService;

import javax.annotation.Resource;

/**
 * ETKD 指令处理
 * ETKD:TN/票号
 * ETKD:CN/PNR CRS编码
 * ETKD:NI/身份证号
 * ETKD:NM/序号  (基于上一条指令为PD，PR的查询结果序号进行二次查询)
 *
 * <AUTHOR>
 * @Date 2022-07-20
 */
@OperateType(action = "ETKD", shorthand = true, template = "/crs/etkd.jf")
public class EtkdHandler implements Handler {

    @Resource
    private IEtkdService iEtkdService;

    /**
     * @param cmd 指令
     */
    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        // 指令格式验证以及查询业务数据
        return iEtkdService.handle(cmd);
    }
}

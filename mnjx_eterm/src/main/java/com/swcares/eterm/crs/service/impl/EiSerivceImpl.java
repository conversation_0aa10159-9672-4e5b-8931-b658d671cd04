package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ReUtils;
import com.swcares.entity.MnjxNmEi;
import com.swcares.entity.MnjxPnrEi;
import com.swcares.eterm.crs.obj.dto.EiParamDto;
import com.swcares.eterm.crs.service.IEiSerivce;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.obj.dto.PnrEiDto;
import com.swcares.obj.dto.PnrNmDto;
import com.swcares.obj.dto.PnrNmEiDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Desc
 * @date 2022/6/8 - 10:52
 * @Version
 * @update 2022/8/22 by 申晓泷
 */
@Service
public class EiSerivceImpl implements IEiSerivce {

    /**
     * 格式：EI:xxxxxxxxxx
     */
    private static final Pattern EI_REG = Pattern.compile("EI[:|\\s]?(.+)(/P(\\d+))?");

    /**
     * 自由文本最大长度
     * 格式：EI:xxxxxxxxxx
     */
    private static final Integer MAX_EI_LENGTH = 48;

    @Resource
    private IPnrManageService iPnrManageService;

    @Override
    public EiParamDto parseEi(String cmd) throws UnifiedResultException {
        // 处理掉换行
        cmd = cmd.replaceAll(StrUtil.CR, "")
                .replaceAll(StrUtil.LF, "")
                .replaceAll(StrUtil.CRLF, "");
        EiParamDto eiParamDto = new EiParamDto();
        if (EI_REG.matcher(cmd).matches()) {
            List<String> allGroups = ReUtils.getAllGroups(EI_REG, cmd);
            eiParamDto.setEiText(allGroups.get(Constant.ONE));
            if (StrUtil.isNotEmpty(allGroups.get(Constant.THREE))) {
                eiParamDto.setPsgIndex(allGroups.get(Constant.THREE));
            }
            return eiParamDto;
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
    }

    @Override
    public void handle(EiParamDto eiParamDto) throws UnifiedResultException {
        // 从缓存中获取pnr
        MemoryDataPnr memoryDataPnr = iPnrManageService.getCurrentControlledPnr();
        this.ei(memoryDataPnr, eiParamDto);
    }

    @Override
    public String recall() {
        return iPnrManageService.recall(iPnrManageService.getCurrentControlledPnr());
    }

    private void ei(MemoryDataPnr memoryDataPnr, EiParamDto eiParamDto) throws UnifiedResultException {
        //自由格式文本长度不能超过48
        if (eiParamDto.getEiText().length() > MAX_EI_LENGTH) {
            throw new UnifiedResultException(Constant.LENGTH);
        }
        if (ObjectUtil.isEmpty(memoryDataPnr.getMnjxPnr().getPnrId())) {
            throw new UnifiedResultException(Constant.NO_PNR);
        }
        if (StrUtil.isNotEmpty(eiParamDto.getPsgIndex())) {
            int psgIndex = Integer.parseInt(eiParamDto.getPsgIndex());
            // 校验该旅客编号是否在PnrNm序列中
            List<PnrNmDto> pnrNmList = memoryDataPnr.getPnrNmDtos();
            pnrNmList = pnrNmList.stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxPnrNm().getPsgIndex() == psgIndex)
                    .filter(k -> !Constant.BE_UPDATED.equals(k.getUpdateMark()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(pnrNmList)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
            // 获取到该旅客
            PnrNmDto pnrNmDto = pnrNmList.get(0);
            // 同一旅客编号未执行Xe的EI项最多只能有一项，否则报错：DUP ID
            List<PnrNmEiDto> pnrNmEiDtos = pnrNmDto.getPnrNmEiDtos().stream()
                    .filter(o -> !o.isXe())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmEiDtos)) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            // 构建NmEi项
            String pnrNmId = pnrNmDto.getMnjxPnrNm().getPnrNmId();
            MnjxNmEi mnjxNmEi = new MnjxNmEi();
            mnjxNmEi.setPnrNmId(pnrNmId);
            mnjxNmEi.setEiInfo(StrUtil.format("EI {}/P{}", eiParamDto.getEiText(), psgIndex));
            mnjxNmEi.setInputValue(StrUtil.format("EI {}/P{}", eiParamDto.getEiText(), psgIndex));
            PnrNmEiDto pnrNmEiDto = new PnrNmEiDto();
            pnrNmEiDto.setMnjxNmEi(mnjxNmEi);
            pnrNmDto.getPnrNmEiDtos().add(pnrNmEiDto);
        } else {
            // PNR级别未执行Xe的EI项最多只能有一项，否则报错：DUP ID
            List<PnrEiDto> pnrEiDtos = memoryDataPnr.getPnrEiDtos().stream()
                    .filter(o -> !o.isXe())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrEiDtos)) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            //构建PnrEi项
            String pnrId = memoryDataPnr.getMnjxPnr().getPnrId();
            MnjxPnrEi mnjxPnrEi = new MnjxPnrEi();
            mnjxPnrEi.setPnrId(pnrId);
            mnjxPnrEi.setEiInfo(StrUtil.format("EI {}", eiParamDto.getEiText()));
            mnjxPnrEi.setInputValue(StrUtil.format("EI {}", eiParamDto.getEiText()));
            PnrEiDto pnrEiDto = new PnrEiDto();
            pnrEiDto.setMnjxPnrEi(mnjxPnrEi);
            memoryDataPnr.getPnrEiDtos().add(pnrEiDto);
        }
    }
}

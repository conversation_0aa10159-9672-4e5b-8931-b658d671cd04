package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.NumberUtils;
import com.swcares.entity.*;
import com.swcares.eterm.crs.service.IFcService;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.obj.dto.*;
import com.swcares.service.IMnjxAirportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FcServiceImpl implements IFcService {

    @Resource
    private IPnrManageService iPnrManageService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    /**
     * FC 格式
     * <p>
     * FC:CTU 3U PVG 2330.00Y CNY2330.00 END
     * FC:IN/CTU 3U PVG 2330.00Y CNY2330.00 END
     * FC CTU 3U PEK 1123.00Y CA SHA 1123.00Y CNY2246.00 END
     * FC CTU 3U PEK 1123.00YCH CA SHA 1123.00YCH CNY2246.00 END
     * FC CTU 3U PEK 1123.00YCH CA SHA 1123.00YCH CNY2246.00 END/P1
     * FC:IN/CTU 3U PEK 1123.00Y CA SHA 1123.00Y CNY2246.00 END
     * FC:IN/CTU 3U PEK 1123.00Y CA SHA 1123.00Y CNY2246.00 END/P1
     * FC:CTU CA PEK 1600.00A  CNY 1600.00 END
     */
    private static final String REG = "FC[:|\\s](/?IN/)?([A-Z]{3})\\s((\\w{2}\\s[A-Z]{3}\\s[0-9.]+[A-Z]([A-Z]{2})?\\s?)+)\\s*([A-Z]{3})\\s*([0-9.]+)\\s?END(/P([0-9]{1,2}))?";

    /**
     * FC 特殊格式 ARNK航段
     * <p>
     * FC CTU 3U PEK 1123.00Y CNY2246.00 END
     * FC // CTU 3U PEK 1123.00YCH CNY2246.00 END
     * FC CTU 3U PEK 1123.00Y // CTU 3U PEK 1123.00YCH CNY2246.00 END
     * FC CTU 3U PEK 1123.00Y // CTU 3U PEK 1123.00YCH CNY2246.00 END/P1
     */
    private static final String REG_ARNK = "FC[:|\\s](IN/)?((\\s?//\\s?|[A-Z]{3}\\s(\\w{2}\\s[A-Z]{3}\\s[0-9.]+[A-Z]([A-Z]{2})?\\s?))+)\\s*([A-Z]{3})\\s*([0-9.]+)\\s?END(/P([0-9]{1,2}))?";

    /**
     * CTU 3U PEK 1123.00Y
     * CTU 3U PEK 1123.00YIN
     * //
     */
    private static final String REG_SEG = "((\\s?//\\s?)|([A-Z]{3})\\s(\\w{2})\\s([A-Z]{3})\\s([0-9.]+)([A-Z])\\s?([A-Z]{2})?)";

    /**
     * 1123.00Y
     * 1123.00YIN
     */
    private static final String REG_FARE_TYPE = "([0-9.]+)[A-Z]([A-Z]{2})?";

    /**
     * 匹配是否有小数点后两位
     * 1200.00
     */
    private static final String REG_DECIMAL_PLACES = "[0-9]+[.][0-9]{2}";

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        // 处理换行符号
        cmd = cmd.replaceAll(StrUtil.CR, "")
                .replaceAll(StrUtil.LF, "")
                .replaceAll(StrUtil.DASHED, "");
        MemoryDataPnr memoryDataPnr = iPnrManageService.getCurrentControlledPnr();

        // 无pnr活动
        if (ObjectUtil.isEmpty(memoryDataPnr) || ObjectUtil.isEmpty(memoryDataPnr.getMnjxPnr())) {
            return Constant.ITINERARY_DOES_NOT_MATCH_FC;
        }
        // 航段信息
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos()
                .stream()
                .filter(k -> !k.isXe())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(pnrSegDtos)) {
            return Constant.SEGMENT;
        }
        // 票价类型
        List<String> fareTypeList = Arrays.asList("AD", "CH", "IN", "GM", "JC");
        // 计算总票价
        BigDecimal tmpTotalFare = new BigDecimal("0.00");
        // 总票价
        String totalFare;
        // 货币代码
        String currencyCode;
        // 婴儿
        boolean isIn = false;
        // pId
        String pId;
        // CTU CA PEK 1300.00
        List<String> seg = new ArrayList<>();
        // 货币代码
        if (cmd.matches(REG)) {
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(REG), cmd);
            if (StrUtil.isNotEmpty(allGroups.get(1))) {
                isIn = true;
            }
            String org = allGroups.get(2);
            String s = allGroups.get(3);
            currencyCode = allGroups.get(6);
            totalFare = allGroups.get(7);
            pId = allGroups.get(9);
            List<String> fcList = Arrays.asList(s.split(" "));
            StringBuffer buf = new StringBuffer();
            for (int i = 1; i <= fcList.size(); i++) {
                String fc = fcList.get(i - 1);
                if (i % 3 == 0) {
                    tmpTotalFare = checkFare(fc, tmpTotalFare, fareTypeList);
                }

                // 拼接字符串
                if (StrUtil.isEmpty(buf)) {
                    buf.append(org).append(" ").append(fc);
                } else {
                    buf.append(" ").append(fc);
                }
                // 出发城市
                if (i % 2 == 0) {
                    org = fc;
                }
                if (i % 3 == 0) {
                    seg.add(buf.toString());
                    buf.setLength(0);
                }
            }
        } else if (cmd.matches(REG_ARNK)) {
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(REG_ARNK), cmd);
            if (StrUtil.isNotEmpty(allGroups.get(1))) {
                isIn = true;
            }
            String s = allGroups.get(2);
            String[] fcList = s.split(" ");
            int i = 1;
            StringBuffer buf = new StringBuffer();
            for (String fc : fcList) {
                if (i % 4 == 0) {
                    tmpTotalFare = checkFare(fc, tmpTotalFare, fareTypeList);
                }
                if (StrUtil.isEmpty(buf)) {
                    buf.append(fc);
                } else {
                    buf.append(" ").append(fc);
                }
                if ("//".equals(fc) || i % 4 == 0) {
                    seg.add(buf.toString());
                    i = 0;
                    buf.setLength(0);
                }
                i++;
            }
            currencyCode = allGroups.get(6);
            totalFare = allGroups.get(7);
            pId = allGroups.get(9);
        } else {
            return Constant.FORMAT;
        }
        if (!Constant.CNY.equals(currencyCode)) {
            throw new UnifiedResultException(Constant.CURRENCY);
        }
        // 总票价校验，如果不是带完整的2位小数的数字
        if (!totalFare.matches(REG_DECIMAL_PLACES)) {
            throw new UnifiedResultException(Constant.DECIMAL);
        }
        // 处理运价中处理个位不为0
        tmpTotalFare = NumberUtils.dealTicketPrice(tmpTotalFare);
        totalFare = NumberUtils.dealTicketPrice(new BigDecimal(totalFare)).toString();
        // 总票价校验，金额总值不等于前面各航段的航段票价之和
        if (tmpTotalFare.doubleValue() != new BigDecimal(totalFare).doubleValue()) {
            throw new UnifiedResultException(Constant.AMOUNT);
        }
        // 如果PNR有多航段，但是当前FC没有完整输入所有航段的票价信息，或者航段比PNR所有航段还多
        if (seg.size() != pnrSegDtos.size()) {
            throw new UnifiedResultException(Constant.ITINERARY_DOES_NOT_MATCH_FC);
        }

        // 当前PNR内多航段的时间连续性及空间连续性校验
        if (pnrSegDtos.size() > 1) {
            checkSegContinuity(pnrSegDtos);
        }

        // fc航段票价、舱位、票价类型
        String seg1Price = "";
        String seg1Cabin = "";
        String seg1PriceType = "";

        String seg2Price = "";
        String seg2Cabin = "";
        String seg2PriceType = "";

        String seg3Price = "";
        String seg3Cabin = "";
        String seg3PriceType = "";

        String seg4Price = "";
        String seg4Cabin = "";
        String seg4PriceType = "";

        String seg5Price = "";
        String seg5Cabin = "";
        String seg5PriceType = "";

        // fc 拼接
        StringBuilder fcValue = new StringBuilder();

        if (seg.size() == 1) {
            String segFc = seg.get(0);
            List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_SEG), segFc);
            String org = groups.get(3);
            String airline = groups.get(4);
            String dst = groups.get(5);
            String segPrice = groups.get(6);
            String cabin = groups.get(7);
            String fareType = groups.get(8);

            // 出发、到达航站校验
            MnjxPnrSeg pnrSeg = pnrSegDtos.get(0).getMnjxPnrSeg();
            if (!org.equals(pnrSeg.getOrg()) || !dst.equals(pnrSeg.getDst())) {
                throw new UnifiedResultException(Constant.ITINERARY_DOES_NOT_MATCH_FC);
            }
            // 航司校验
            if (!airline.equals(pnrSeg.getFlightNo().substring(0, Constant.TWO))) {
                throw new UnifiedResultException(Constant.AIRLINE_CODE_DOES_NOT_MATCH_ITINERARY);
            }
            seg1Price = segPrice;
            seg1Cabin = cabin;
            seg1PriceType = fareType;
            fcValue.append(String.format("%s %s %s %s", org, airline, dst, segPrice));
            if (StrUtil.isNotEmpty(cabin)) {
                fcValue.append(String.format("%s", cabin));
            }
            if (StrUtil.isNotEmpty(fareType)) {
                fcValue.append(String.format("%s", fareType));
            }
        } else {
            for (int i = 0; i < seg.size(); i++) {
                String segFc = seg.get(i);
                List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_SEG), segFc);
                String org = groups.get(3);
                String airline = groups.get(4);
                String dst = groups.get(5);
                String segPrice = groups.get(6);
                String cabin = groups.get(7);
                String fareType = groups.get(8);
                MnjxPnrSeg pnrSeg = pnrSegDtos.get(i).getMnjxPnrSeg();
                // 多航段中存在ARNK航段
                if ("SA".equals(pnrSeg.getPnrSegType()) && "//".equals(segFc)) {
                    continue;
                }
                if (!org.equals(pnrSeg.getOrg()) || !dst.equals(pnrSeg.getDst())) {
                    throw new UnifiedResultException(Constant.ITINERARY_DOES_NOT_MATCH_FC);
                }
                // 航司校验
                if (!airline.equals(pnrSeg.getFlightNo().substring(0, 2))) {
                    throw new UnifiedResultException(Constant.AIRLINE_CODE_DOES_NOT_MATCH_ITINERARY);
                }
                if (i == 0) {
                    seg1Price = segPrice;
                    seg1Cabin = cabin;
                    seg1PriceType = fareType;
                    buildFc(fcValue, airline, dst, segPrice, cabin, fareType);
                } else if (i == 1) {
                    seg2Price = segPrice;
                    seg2Cabin = cabin;
                    seg2PriceType = fareType;
                    buildFc(fcValue, airline, dst, segPrice, cabin, fareType);
                } else if (i == 2) {
                    seg3Price = segPrice;
                    seg3Cabin = cabin;
                    seg3PriceType = fareType;
                    buildFc(fcValue, airline, dst, segPrice, cabin, fareType);
                } else if (i == 3) {
                    seg4Price = segPrice;
                    seg4Cabin = cabin;
                    seg4PriceType = fareType;
                    buildFc(fcValue, airline, dst, segPrice, cabin, fareType);
                } else if (i == 4) {
                    seg5Price = segPrice;
                    seg5Cabin = cabin;
                    seg5PriceType = fareType;
                    buildFc(fcValue, airline, dst, segPrice, cabin, fareType);
                }
            }
        }
        fcValue.append(" ").append(String.format("%s%sEND", currencyCode, totalFare));
        if (StrUtil.isNotEmpty(pId)) {
            fcValue.append(String.format("/P%s", pId));
        }
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        Integer isBaby = isIn ? 1 : 0;
        if (StrUtil.isNotEmpty(pId)) {
            int psgId = Integer.parseInt(pId);
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getPnrIndex() == psgId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(pnrNmDtos)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
            PnrNmDto pnrNmDto = pnrNmDtos.get(0);
            List<PnrNmFcDto> pnrNmFcDtos = pnrNmDto.getPnrNmFcDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxNmFc().getIsBaby().equals(isBaby))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmFcDtos)) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            List<PnrNmDto> nmDtos = memoryDataPnr.getPnrNmDtos().stream().filter(k -> !k.isXe()).collect(Collectors.toList());
            List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxPnrFn().getIsBaby().equals(isBaby))
                    .collect(Collectors.toList());
            if (nmDtos.size() == 1) {
                if (CollUtil.isNotEmpty(pnrFnDtos)) {
                    throw new UnifiedResultException(Constant.PSGR_ID);
                }
            }
            String pnrNmId = pnrNmDto.getMnjxPnrNm().getPnrNmId();
            PnrNmFcDto pnrNmFcDto = buildNmFc(pnrNmId, seg1Price, seg1Cabin, seg1PriceType, seg2Price, seg2Cabin, seg2PriceType,
                    seg3Price, seg3Cabin, seg3PriceType, seg4Price, seg4Cabin, seg4PriceType, seg5Price, seg5Cabin, seg5PriceType,
                    currencyCode, totalFare, isIn, fcValue.toString());
            pnrNmDto.getPnrNmFcDtos().add(pnrNmFcDto);
        } else {
            int count = 0;
            List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos().stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxPnrFc().getIsBaby().equals(isBaby))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrFcDtos)) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            // 所有旅客都直接关联有FC、FN项
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                    .filter(k -> !k.isXe())
                    .collect(Collectors.toList());

            for (PnrNmDto pnrNmDto : pnrNmDtos) {
                List<PnrNmFcDto> pnrNmFcDtos = pnrNmDto.getPnrNmFcDtos().stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> k.getMnjxNmFc().getIsBaby().equals(isBaby))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(pnrNmFcDtos)) {
                    count += pnrNmFcDtos.size();
                }
                List<PnrNmFnDto> pnrNmFnDtos = pnrNmDto.getPnrNmFnDtos().stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> k.getMnjxNmFn().getIsBaby().equals(isBaby))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(pnrNmFnDtos)) {
                    count += pnrNmFnDtos.size();
                }
            }
            if (count != 0) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
            PnrFcDto pnrFcDto = buildPnrFc(mnjxPnr.getPnrId(), seg1Price, seg1Cabin, seg1PriceType, seg2Price, seg2Cabin, seg2PriceType,
                    seg3Price, seg3Cabin, seg3PriceType, seg4Price, seg4Cabin, seg4PriceType, seg5Price, seg5Cabin, seg5PriceType,
                    currencyCode, totalFare, isIn, fcValue.toString());
            memoryDataPnr.getPnrFcDtos().add(pnrFcDto);
        }
        return iPnrManageService.recall(memoryDataPnr);
    }

    private void buildFc(StringBuilder fcValue, String airline, String dst, String segPrice, String cabin, String fareType) {
        if (StrUtil.isEmpty(fcValue)) {
            fcValue.append(String.format("%s %s %s", airline, dst, segPrice));
        } else {
            fcValue.append(" ").append(String.format("%s %s %s", airline, dst, segPrice));
        }
        if (StrUtil.isNotEmpty(cabin)) {
            fcValue.append(String.format("%s", cabin));
        }
        if (StrUtil.isNotEmpty(fareType)) {
            fcValue.append(String.format("%s", fareType));
        }
    }

    /**
     * 检查票价
     */
    private BigDecimal checkFare(String fc, BigDecimal tmpTotalFare, List<String> fareTypeList) throws UnifiedResultException {
        List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_FARE_TYPE), fc);
        // 航段票价
        String segPrice = groups.get(1);
        if (!segPrice.matches(REG_DECIMAL_PLACES)) {
            throw new UnifiedResultException(Constant.DECIMAL);
        }
        tmpTotalFare = tmpTotalFare.add(new BigDecimal(segPrice));
//        String fareType = groups.get(2);
//        // 票价类型校验
//        if (StrUtil.isNotEmpty(fareType) && !fareTypeList.contains(fareType)) {
//            throw new UnifiedResultException(Constant.FC_TYPE);
//        }
        return tmpTotalFare;
    }


    private PnrFcDto buildPnrFc(String pnrId, String seg1Price, String seg1Cabin, String seg1PriceType,
                                String seg2Price, String seg2Cabin, String seg2PriceType, String seg3Price, String seg3Cabin, String seg3PriceType,
                                String seg4Price, String seg4Cabin, String seg4PriceType, String seg5Price, String seg5Cabin, String seg5PriceType,
                                String currencyCode, String totalFare, boolean isIn, String fcValue) {
        PnrFcDto pnrFcDto = new PnrFcDto();
        MnjxPnrFc mnjxPnrFc = new MnjxPnrFc();
        mnjxPnrFc.setPnrFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxPnrFc.setPnrId(pnrId);
        mnjxPnrFc.setSeg1Price(new BigDecimal(seg1Price));
        mnjxPnrFc.setSeg1Cabin(seg1Cabin);
        mnjxPnrFc.setSeg1PriceType(seg1PriceType);

        if (StrUtil.isNotEmpty(seg2Price)) {
            mnjxPnrFc.setSeg2Price(new BigDecimal(seg2Price));
            mnjxPnrFc.setSeg2Cabin(seg2Cabin);
            mnjxPnrFc.setSeg2PriceType(seg2PriceType);
        }
        if (StrUtil.isNotEmpty(seg3Price)) {
            mnjxPnrFc.setSeg3Price(new BigDecimal(seg3Price));
            mnjxPnrFc.setSeg3Cabin(seg3Cabin);
            mnjxPnrFc.setSeg3PriceType(seg3PriceType);
        }
        if (StrUtil.isNotEmpty(seg4Price)) {
            mnjxPnrFc.setSeg4Price(new BigDecimal(seg4Price));
            mnjxPnrFc.setSeg4Cabin(seg4Cabin);
            mnjxPnrFc.setSeg4PriceType(seg4PriceType);
        }
        if (StrUtil.isNotEmpty(seg5Price)) {
            mnjxPnrFc.setSeg5Price(new BigDecimal(seg5Price));
            mnjxPnrFc.setSeg5Cabin(seg5Cabin);
            mnjxPnrFc.setSeg5PriceType(seg5PriceType);
        }
        mnjxPnrFc.setCurrency(currencyCode);
        mnjxPnrFc.setTotalPrice(new BigDecimal(totalFare));
        mnjxPnrFc.setIsBaby(0);

        String inputValue;
        if (isIn) {
            mnjxPnrFc.setIsBaby(1);
            inputValue = String.format("FC/IN/%s", fcValue);
        } else {
            inputValue = String.format("FC/%s", fcValue);
        }
        mnjxPnrFc.setInputValue(inputValue);

        pnrFcDto.setMnjxPnrFc(mnjxPnrFc);
        return pnrFcDto;
    }

    private PnrNmFcDto buildNmFc(String pnrNmId, String seg1Price, String seg1Cabin, String seg1PriceType,
                                 String seg2Price, String seg2Cabin, String seg2PriceType, String seg3Price, String seg3Cabin, String seg3PriceType,
                                 String seg4Price, String seg4Cabin, String seg4PriceType, String seg5Price, String seg5Cabin, String seg5PriceType,
                                 String currencyCode, String totalFare, boolean isIn, String fcValue) {
        PnrNmFcDto pnrNmFcDto = new PnrNmFcDto();
        MnjxNmFc mnjxNmFc = new MnjxNmFc();
        mnjxNmFc.setNmFcId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxNmFc.setPnrNmId(pnrNmId);
        mnjxNmFc.setSeg1Price(new BigDecimal(seg1Price));
        mnjxNmFc.setSeg1Cabin(seg1Cabin);
        mnjxNmFc.setSeg1PriceType(seg1PriceType);

        if (StrUtil.isNotEmpty(seg2Price)) {
            mnjxNmFc.setSeg2Price(new BigDecimal(seg2Price));
            mnjxNmFc.setSeg2Cabin(seg2Cabin);
            mnjxNmFc.setSeg2PriceType(seg2PriceType);

        }
        if (StrUtil.isNotEmpty(seg3Price)) {
            mnjxNmFc.setSeg3Price(new BigDecimal(seg3Price));
            mnjxNmFc.setSeg3Cabin(seg3Cabin);
            mnjxNmFc.setSeg3PriceType(seg3PriceType);
        }
        if (StrUtil.isNotEmpty(seg4Price)) {
            mnjxNmFc.setSeg4Price(new BigDecimal(seg4Price));
            mnjxNmFc.setSeg4Cabin(seg4Cabin);
            mnjxNmFc.setSeg4PriceType(seg4PriceType);
        }
        if (StrUtil.isNotEmpty(seg5Price)) {
            mnjxNmFc.setSeg5Price(new BigDecimal(seg5Price));
            mnjxNmFc.setSeg5Cabin(seg5Cabin);
            mnjxNmFc.setSeg5PriceType(seg5PriceType);
        }
        mnjxNmFc.setCurrency(currencyCode);
        mnjxNmFc.setTotalPrice(new BigDecimal(totalFare));
        mnjxNmFc.setIsBaby(0);

        String inputValue;
        if (isIn) {
            mnjxNmFc.setIsBaby(1);
            inputValue = String.format("FC/IN/%s", fcValue);
        } else {
            inputValue = String.format("FC/%s", fcValue);
        }
        mnjxNmFc.setInputValue(inputValue);
        pnrNmFcDto.setMnjxNmFc(mnjxNmFc);
        return pnrNmFcDto;
    }

    /**
     * PNR内多航段的时间连续性及空间连续性校验
     *
     * @param pnrSegDtos pnrSegDtos
     * @throws UnifiedResultException 统一异常
     */
    private void checkSegContinuity(List<PnrSegDto> pnrSegDtos) throws UnifiedResultException {
        for (int i = 0; i < pnrSegDtos.size() - 1; i++) {
            PnrSegDto thisSegDto = pnrSegDtos.get(i);
            PnrSegDto nextSegDto = pnrSegDtos.get(i + 1);
            MnjxPnrSeg thisSeg = thisSegDto.getMnjxPnrSeg();
            MnjxPnrSeg nextSeg = nextSegDto.getMnjxPnrSeg();
            // 检查空间连续性
            String thisDst = thisSeg.getDst();
            String nextOrg = nextSeg.getOrg();
            MnjxAirport thisAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, thisDst)
                    .one();
            String thisCityId = thisAirport.getCityId();
            MnjxAirport nextAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, nextOrg)
                    .one();
            String nextCityId = nextAirport.getCityId();
            if (!thisCityId.equals(nextCityId)) {
                throw new UnifiedResultException(Constant.CHECK_CONTINUITY);
            }
            if (!"SA".equals(thisSeg.getPnrSegType()) && !"SA".equals(nextSeg.getPnrSegType())) {
                // 检查时间连续性（转机时间）
                String thisArrTime = thisSeg.getEstimateArr();
                String nextOffTime = nextSeg.getEstimateOff();
                int timeCut = Integer.parseInt(nextOffTime) - Integer.parseInt(thisArrTime);
                if (timeCut >= 60) {
                    timeCut = timeCut - 40;
                }
                if (timeCut < 0) {
                    throw new UnifiedResultException(Constant.CHECK_CONNECTION);
                } else if (timeCut < 120) {
                    String errorInfo = StrUtil.format("{} 和 {} 航班的最短转机时间是120分钟，请修改联程航段！", thisSeg.getFlightNo(), nextSeg.getFlightNo());
                    throw new UnifiedResultException(errorInfo);
                }
            }
        }
    }
}

/*
 * Copyright (c) 2021. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.NumberUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.MnjxNmFn;
import com.swcares.entity.MnjxPnr;
import com.swcares.entity.MnjxPnrFn;
import com.swcares.eterm.crs.obj.dto.FnDto;
import com.swcares.eterm.crs.service.IFnService;
import com.swcares.eterm.crs.service.IPnrManageService;
import com.swcares.obj.dto.PnrFnDto;
import com.swcares.obj.dto.PnrNmDto;
import com.swcares.obj.dto.PnrNmFcDto;
import com.swcares.obj.dto.PnrNmFnDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FnServiceImpl implements IFnService {

    @Resource
    private IPnrManageService iPnrManageService;

    /**
     * FN:F填与客票FARE栏内的票价/S填于CASH CONLLECTION的金额/C填于COMM RATE栏中的代理费率/XCNY税款合计金额/TCNY机场建设费的金额CN/TCNY燃油附加费的金额YQ/A填写于TOTAL栏内的总金额
     * <p>
     * FN:FCNY1235.00/SCNY2345.00/C4.00
     * FN:FCNY1235.00/SCNY2345.00/C4.00/TCNY50.00CN/TCNY200.00YQ
     * FN:IN/FCNY1235.00/SCNY2345.00/C4.00/TCNY50.00CN/TCNY200.00YQ
     * FN:FCNY1235.00/SCNY2345.00/C4.00/TCNY50.00CN/TCNY200.00YQ/ACNY2300.00
     * FN:FCNY1235.00/SCNY2345.00/C4.00/TCNY50.00CN/TCNY200.00YQ/ACNY2300.00/P1
     * FN:FCNY 1200.00/ SCNY1200.00/ C0.00/ TCNY 50.00CN/ TCNY 140.00YQ
     */
    private static final String REG = "FN[:|\\s]" +
            "(/?IN/)?" +
            "([F|R]([A-Z]{3})\\s*([0-9]+([.][0-9]{1,2})?)?/)?" +
            "(\\s*S([A-Z]{3})\\s*([0-9]+([.][0-9]{1,2})?)?/)?" +
            "(\\s*C([0-9]+([.][0-9]{1,2})?))?" +
            "(/X([A-Z]{3})([0-9]+([.][0-9]{1,2})?))?" +
            "((/?\\s*T[\\w.\\s*]+)+)?" +
            "(/A([A-Z]{3})(\\d+([.][0-9]{1,2})?))?" +
            "(/P([0-9]{1,2}))?";

    /**
     * /TCNY50.00CN
     */
    private static final String REG_CN = "(\\s*T(EXEMPTCN))|(\\s*T([A-Z]{3})\\s*([0-9]+([.][0-9]{1,2})?)CN)";

    /**
     * /TCNY50.00YQ
     */
    private static final String REG_YQ = "(\\s*T(EXEMPTYQ))|(\\s*T([A-Z]{3})\\s*([0-9]+([.][0-9]{1,2})?)YQ)";

    /**
     * TCNY50.00YQ
     * TCNY 50.00YQ
     */
    private static final String REG_CY = "(((\\s*T(EXEMPTYQ))|(\\s*T([A-Z]{3})\\s*([0-9]+([.][0-9]{1,2})?)YQ))|((\\s*T(EXEMPTCN))|(\\s*T([A-Z]{3})\\s*([0-9]+([.][0-9]{1,2})?)CN)))";

    /**
     * 免税
     */
    private static final String REG_TEXEMPTCN = "TEXEMPTCN";

    /**
     * 免税
     */
    private static final String REG_TEXEMPTYQ = "TEXEMPTYQ";

    /**
     * 金额
     */
    private static final String REG_MONEY = "(\\d+[.]\\d{2})";

    @Override
    public String handle(FnDto fnDto) throws UnifiedResultException {
        MemoryDataPnr memoryDataPnr = iPnrManageService.getCurrentControlledPnr();
        if (ObjectUtil.isEmpty(memoryDataPnr.getMnjxPnr().getPnrId())) {
            iPnrManageService.createNewPnr();
        }

        // 货币种类
        List<String> currencyList = Arrays.asList(fnDto.getFCurrencyType(), fnDto.getSCurrencyType(), fnDto.getTCnCurrencyType(), fnDto.getXCurrencyType(), fnDto.getTYqCurrencyType(), fnDto.getACurrencyType());
        currencyList = currencyList.stream()
                .filter(StrUtil::isNotEmpty)
                .filter(k -> !k.matches(Constant.CNY))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(currencyList)) {
            throw new UnifiedResultException(Constant.CURRENCY);
        }
        // 输入的票价不正确、票价没有保留2位小数
        List<String> prices = Arrays.asList(fnDto.getFPrice(), fnDto.getSPrice(), fnDto.getTCnPrice(), fnDto.getTYqPrice(), fnDto.getAPrice());

        for (String price : prices) {
            if (StrUtil.isEmpty(price)) {
                throw new UnifiedResultException(Constant.AMOUNT);
            }
            if (!price.matches(REG_MONEY)) {
                throw new UnifiedResultException(Constant.DECIMAL);
            }
        }
        // 计算价格和输入的价格是否相等
        String allPrice = StrUtil.toString(new BigDecimal(fnDto.getSPrice()).add(new BigDecimal(fnDto.getXPrice())));
        if (!fnDto.getAPrice().equals(allPrice)) {
            throw new UnifiedResultException(Constant.TOTAL);
        }
        if (new BigDecimal(fnDto.getSPrice()).doubleValue() > new BigDecimal(fnDto.getFPrice()).doubleValue()) {
            throw new UnifiedResultException(Constant.AMOUNT);
        }
        Integer isBaby = fnDto.isIn() ? 1 : 0;
        if (StrUtil.isNotEmpty(fnDto.getPId())) {
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
            int psgId = Integer.parseInt(fnDto.getPId());
            List<PnrNmDto> nmDtoList = pnrNmDtos.stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getPnrIndex() == psgId)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(nmDtoList)) {
                throw new UnifiedResultException(Constant.PSGR_ID);
            }
            long count = 0;
            PnrNmDto nmDto = nmDtoList.get(0);
            if (ObjectUtil.isNotEmpty(nmDto)) {
                List<PnrFnDto> fnDtos = memoryDataPnr.getPnrFnDtos()
                        .stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> k.getMnjxPnrFn().getIsBaby().equals(isBaby))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(fnDtos)) {
                    count += fnDtos.size();
                }
                List<PnrNmFnDto> pnrNmFnDtoList = nmDto.getPnrNmFnDtos().
                        stream()
                        .filter(k -> !k.isXe())
                        .filter(k -> k.getMnjxNmFn().getIsBaby().equals(isBaby))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(pnrNmFnDtoList)) {
                    count += pnrNmFnDtoList.size();
                }

            }
            if (count != 0) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            // 如果婴儿
            PnrNmDto pnrNmDto = nmDtoList.get(0);

            fnDto.setAPrice(NumberUtils.dealTicketPrice(new BigDecimal(fnDto.getAPrice())).toString());
            MnjxNmFn mnjxNmFn = this.buildNmFn(pnrNmDto.getMnjxPnrNm().getPnrNmId(), fnDto);
            pnrNmDto.getPnrNmFnDtos().add(new PnrNmFnDto(mnjxNmFn));
        } else {
            List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos()
                    .stream()
                    .filter(k -> !k.isXe())
                    .filter(k -> k.getMnjxPnrFn().getIsBaby().equals(isBaby))
                    .collect(Collectors.toList());
            // PNR中已经存在FN项时
            if (CollUtil.isNotEmpty(pnrFnDtos)) {
                throw new UnifiedResultException(Constant.DUP_ID);
            }
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos()
                    .stream()
                    .filter(k -> !k.isXe())
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(pnrNmDtos)) {
                long count = 0;
                for (PnrNmDto pnrNmDto : pnrNmDtos) {
                    List<PnrNmFnDto> pnrNmFnDtos = pnrNmDto.getPnrNmFnDtos()
                            .stream()
                            .filter(k -> !k.isXe())
                            .filter(k -> k.getMnjxNmFn().getIsBaby().equals(isBaby))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(pnrNmFnDtos)) {
                        count += pnrNmFnDtos.size();
                    }
                    List<PnrNmFcDto> fcDtos = pnrNmDto.getPnrNmFcDtos()
                            .stream()
                            .filter(k -> !k.isXe())
                            .filter(k -> k.getMnjxNmFc().getIsBaby().equals(isBaby))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(fcDtos)) {
                        count += fcDtos.size();
                    }
                }
                if (count != 0) {
                    throw new UnifiedResultException(Constant.PSGR_ID);
                }
            }
            MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();

            fnDto.setAPrice(NumberUtils.dealTicketPrice(new BigDecimal(fnDto.getAPrice())).toString());
            MnjxPnrFn mnjxPnrFn = this.buildPnrFn(mnjxPnr.getPnrId(), fnDto);
            memoryDataPnr.getPnrFnDtos().add(new PnrFnDto(mnjxPnrFn));
        }
        return iPnrManageService.recall(memoryDataPnr);
    }

    @Override
    public FnDto parseArgs(String cmd) throws UnifiedResultException {
        FnDto fnDto = new FnDto();
        // 处理换行符号
        cmd = cmd.replaceAll(StrUtil.CR, StrUtils.EMPTY).replaceAll(StrUtil.LF, StrUtils.EMPTY).replaceAll(StrUtil.DASHED, StrUtils.EMPTY);
        if (cmd.matches(REG)) {
            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(REG), cmd);
            if (StrUtils.isNotEmpty(allGroups.get(1))) {
                fnDto.setIn(true);
            }
            // 票面价
            String facePrice = allGroups.get(2);
            if (StrUtils.isEmpty(facePrice)) {
                throw new UnifiedResultException(Constant.FIELD_ID);
            }
            fnDto.setFCurrencyType(allGroups.get(3));
            fnDto.setFPrice(allGroups.get(4));
            String sPrice = allGroups.get(6);
            if (StrUtils.isEmpty(sPrice)) {
                throw new UnifiedResultException(Constant.CASH_COLLECTION);
            }
            // 实收费用
            fnDto.setSCurrencyType(allGroups.get(7));
            fnDto.setSPrice(allGroups.get(8));
            String cPrice = allGroups.get(10);
            if (StrUtil.isEmpty(cPrice)) {
                throw new UnifiedResultException(Constant.COMMISSION);
            }
            // 代理费
            fnDto.setCPrice(allGroups.get(11));
            if (StrUtil.isEmpty(fnDto.getCPrice()) || !fnDto.getCPrice().matches(REG_MONEY)) {
                throw new UnifiedResultException(Constant.AMOUNT);
            }
            String xPriceStr = allGroups.get(13);
            if (StrUtil.isNotEmpty(xPriceStr)) {
                fnDto.setXCurrencyType(allGroups.get(14));
                fnDto.setXPrice(allGroups.get(15));
                if (!fnDto.getXPrice().matches(REG_MONEY)) {
                    throw new UnifiedResultException(Constant.AMOUNT);
                }
            }
            // 是否是婴儿xxxx 不需要验证是否是婴儿
            String cnYq = allGroups.get(17);
            if (StrUtil.isNotEmpty(cnYq)) {
                List<String> cnYqs = Arrays.stream(cnYq.split(StrUtils.SLASH))
                        .filter(StrUtil::isNotEmpty)
                        .collect(Collectors.toList());
                int cn = 0, yq = 0;
                for (String cy : cnYqs) {
                    if (cy.matches(REG_CN) || cy.matches(REG_YQ)) {
                        if (!cy.matches(REG_CY)) {
                            throw new UnifiedResultException(Constant.ONLY_TAXES_CN_YQ_OC_ARE_ALLOWED);
                        }
                        if (cy.endsWith(Constant.CN)) {
                            cn++;
                        } else if (cy.endsWith(Constant.YQ)) {
                            yq++;
                        }
                        if (cy.matches(REG_CN)) {
                            if (cy.trim().equals(REG_TEXEMPTCN)) {
                                fnDto.setTCnCurrencyType("CNY");
                            } else {
                                List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_CN), cy);
                                if (!groups.get(0).matches(REG_TEXEMPTCN)) {
                                    fnDto.setTCnCurrencyType(groups.get(4));
                                    fnDto.setTCnPrice(groups.get(5));
                                }
                            }
                        } if (cy.matches(REG_YQ)) {
                            if (cy.trim().equals(REG_TEXEMPTYQ)) {
                                fnDto.setTYqCurrencyType("CNY");
                            } else {
                                List<String> groups = ReUtil.getAllGroups(Pattern.compile(REG_YQ), cy);
                                if (!groups.get(0).matches(REG_TEXEMPTYQ)) {
                                    fnDto.setTYqCurrencyType(groups.get(4));
                                    fnDto.setTYqPrice(groups.get(5));
                                }
                            }
                        }
                    } else {
                        throw new UnifiedResultException(Constant.FORMAT);
                    }
                }
                if (cn > 1 || yq > 1) {
                    throw new UnifiedResultException(Constant.DUP_CN_YQ);
                }
                if (!fnDto.getTCnPrice().matches(REG_MONEY) || !fnDto.getTYqPrice().matches(REG_MONEY)) {
                    throw new UnifiedResultException(Constant.DECIMAL);
                }
                fnDto.setXCurrencyType(Constant.CNY);
            }
            // 计算税价
            if (StrUtil.isEmpty(fnDto.getXPrice())) {
                fnDto.setXPrice(StrUtil.toString(new BigDecimal(fnDto.getTCnPrice()).add(new BigDecimal(fnDto.getTYqPrice()))));
            } else {
                String xTmpPrice = StrUtil.toString(new BigDecimal(fnDto.getTCnPrice()).add(new BigDecimal(fnDto.getTYqPrice())));
                if (!fnDto.getXPrice().equals(xTmpPrice)) {
                    throw new UnifiedResultException(Constant.AMOUNT);
                }
            }
            // 总票价
            String totalPrice = allGroups.get(19);
            if (StrUtil.isNotEmpty(totalPrice)) {
                fnDto.setACurrencyType(allGroups.get(20));
                fnDto.setAPrice(allGroups.get(21));
            } else {
                fnDto.setAPrice(StrUtil.toString(new BigDecimal(fnDto.getSPrice()).add(new BigDecimal(fnDto.getXPrice()))));
            }
            // pid
            String pId = allGroups.get(23);
            if (StrUtil.isNotEmpty(pId)) {
                fnDto.setPId(allGroups.get(24));
            }
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return fnDto;
    }

    /**
     * 构建MnjxPnrFn
     *
     * @param pnrId pnrId
     * @return 构建MnjxPnrFn
     */
    private MnjxPnrFn buildPnrFn(String pnrId, FnDto fnDto) {
        MnjxPnrFn mnjxPnrFn = new MnjxPnrFn();
        mnjxPnrFn.setPnrFnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        mnjxPnrFn.setPnrId(pnrId);
        mnjxPnrFn.setFCurrency(fnDto.getFCurrencyType());
        mnjxPnrFn.setFPrice(new BigDecimal(fnDto.getFPrice()));
        mnjxPnrFn.setSCurrency(fnDto.getSCurrencyType());
        mnjxPnrFn.setSPrice(new BigDecimal(fnDto.getSPrice()));
        mnjxPnrFn.setCRate(new BigDecimal(fnDto.getCPrice()));
        mnjxPnrFn.setXCurrency(StrUtil.emptyToDefault(fnDto.getXCurrencyType(), "CNY"));
        mnjxPnrFn.setXPrice(new BigDecimal(fnDto.getXPrice()));
        mnjxPnrFn.setTCnCurrency(fnDto.getTCnCurrencyType());
        mnjxPnrFn.setTCnPrice(new BigDecimal(fnDto.getTCnPrice()));
        mnjxPnrFn.setTYqCurrency(fnDto.getTYqCurrencyType());
        mnjxPnrFn.setTYqPrice(new BigDecimal(fnDto.getTYqPrice()));
        mnjxPnrFn.setACurrency(fnDto.getACurrencyType());
        mnjxPnrFn.setAPrice(new BigDecimal(fnDto.getAPrice()));
        mnjxPnrFn.setIsBaby(Integer.parseInt(Constant.STR_ZERO));

        // 拼接fn value
        StringBuffer fnValue = new StringBuffer();
        fnValue.append(StrUtils.format("F{}{}", fnDto.getFCurrencyType(), fnDto.getFPrice()))
                .append(StrUtils.format("/S{}{}", fnDto.getSCurrencyType(), fnDto.getSPrice()))
                .append(StrUtils.format("/C{}", fnDto.getCPrice()));
        if (StrUtil.isNotEmpty(fnDto.getXCurrencyType())) {
            fnValue.append(StrUtils.format("/X{}{}", fnDto.getXCurrencyType(), fnDto.getXPrice()));
        }
        if (StrUtil.isNotEmpty(fnDto.getTCnCurrencyType()) && StrUtil.isNotEmpty(fnDto.getTCnPrice())) {
            fnValue.append(StrUtils.format("/T{}{}CN", fnDto.getTCnCurrencyType(), fnDto.getTCnPrice()));
        }
        if (StrUtil.isNotEmpty(fnDto.getTYqCurrencyType()) && StrUtil.isNotEmpty(fnDto.getTYqPrice())) {
            fnValue.append(StrUtils.format("/T{}{}YQ", fnDto.getTYqCurrencyType(), fnDto.getTYqPrice()));
        }
        if (StrUtil.isEmpty(fnDto.getACurrencyType())) {
            fnDto.setACurrencyType(Constant.CNY);
        }
        fnValue.append(StrUtils.format("/A{}{}", fnDto.getACurrencyType(), fnDto.getAPrice()));

        String inputValue;
        if (fnDto.isIn()) {
            mnjxPnrFn.setIsBaby(1);
            inputValue = StrUtils.format("FN/IN/{}", fnValue);
        } else {
            inputValue = StrUtils.format("FN/{}", fnValue);
        }
        mnjxPnrFn.setInputValue(inputValue);
        return mnjxPnrFn;
    }

    /**
     * 构建MnjxNmFn
     *
     * @param pnrId pnrId
     * @return 构建MnjxNmFn
     */
    private MnjxNmFn buildNmFn(String pnrId, FnDto fnDto) {
        MnjxNmFn mnjxNmFn = new MnjxNmFn();
        mnjxNmFn.setPnrNmId(pnrId);
        mnjxNmFn.setFCurrency(fnDto.getFCurrencyType());
        mnjxNmFn.setFPrice(new BigDecimal(fnDto.getFPrice()));
        mnjxNmFn.setSCurrency(fnDto.getSCurrencyType());
        mnjxNmFn.setSPrice(new BigDecimal(fnDto.getSPrice()));
        mnjxNmFn.setCRate(new BigDecimal(fnDto.getCPrice()));
        mnjxNmFn.setXCurrency(StrUtil.emptyToDefault(fnDto.getXCurrencyType(), "CNY"));
        mnjxNmFn.setXPrice(new BigDecimal(fnDto.getXPrice()));
        mnjxNmFn.setTCnCurrency(fnDto.getTCnCurrencyType());
        mnjxNmFn.setTCnPrice(new BigDecimal(fnDto.getTCnPrice()));
        mnjxNmFn.setTYqCurrency(fnDto.getTYqCurrencyType());
        mnjxNmFn.setTYqPrice(new BigDecimal(fnDto.getTYqPrice()));
        mnjxNmFn.setACurrency(fnDto.getACurrencyType());
        mnjxNmFn.setAPrice(new BigDecimal(fnDto.getAPrice()));
        mnjxNmFn.setIsBaby(Integer.parseInt(Constant.STR_ZERO));

        // 拼接fn value
        StringBuffer fnValue = new StringBuffer();
        fnValue.append(StrUtils.format("F{}{}", fnDto.getFCurrencyType(), fnDto.getFPrice()))
                .append(StrUtils.format("/S{}{}", fnDto.getSCurrencyType(), fnDto.getSPrice()))
                .append(StrUtils.format("/C{}", fnDto.getCPrice()));
        if (StrUtil.isNotEmpty(fnDto.getXCurrencyType())) {
            fnValue.append(StrUtils.format("/X{}{}", fnDto.getXCurrencyType(), fnDto.getXPrice()));
        }
        if (StrUtil.isNotEmpty(fnDto.getTCnCurrencyType()) && StrUtil.isNotEmpty(fnDto.getTCnPrice())) {
            fnValue.append(StrUtils.format("/T{}{}CN", fnDto.getTCnCurrencyType(), fnDto.getTCnPrice()));
        }
        if (StrUtil.isNotEmpty(fnDto.getTYqCurrencyType()) && StrUtil.isNotEmpty(fnDto.getTYqPrice())) {
            fnValue.append(StrUtils.format("/T{}{}YQ", fnDto.getTYqCurrencyType(), fnDto.getTYqPrice()));
        }
        if (StrUtil.isEmpty(fnDto.getACurrencyType())) {
            fnDto.setACurrencyType(Constant.CNY);
        }
        fnValue.append(StrUtils.format("/A{}{}/P{}", fnDto.getACurrencyType(), fnDto.getAPrice(), fnDto.getPId()));

        String inputValue;
        if (fnDto.isIn()) {
            mnjxNmFn.setIsBaby(1);
            inputValue = StrUtils.format("FN/IN/{}", fnValue);
        } else {
            inputValue = StrUtils.format("FN/{}", fnValue);
        }
        mnjxNmFn.setInputValue(inputValue);
        return mnjxNmFn;
    }
}

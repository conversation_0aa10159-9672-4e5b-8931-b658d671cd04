package com.swcares.eterm.crs.mapper;

import com.swcares.eterm.crs.obj.vo.TicketLimitsVo;
import com.swcares.eterm.crs.obj.vo.TolVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-7-12
 */
public interface TolMapper {

    /**
     * 查询票号的使用记录详情
     * 包括卸票历史信息，所有票证信息（包括未使用，已使用，正在使用）
     *
     * @param officeNum
     * @return
     */
    List<TicketLimitsVo> retrieveTicketNoRecord(@Param("officeNum") String officeNum);

    /**
     * 查询打票机操作记录
     *
     * @param opt
     * @param siIds
     * @param officeNo
     * @return
     */
    List<TolVo> retrievePinterLoad(@Param("opt") String opt, @Param("siIds") List<String> siIds, @Param("officeNo") String officeNo);
}

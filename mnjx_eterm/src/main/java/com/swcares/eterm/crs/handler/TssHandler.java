package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.TssDto;
import com.swcares.eterm.crs.service.ITssService;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

/**
 * TSS指令验证
 * S 选项表示执行挂起操作,B 选项表示执行解挂操作
 * TSS：TN/13位票号/S
 * TSS：TN/13位票号/B
 * TSS: CN/PNR编号/出票日期/S
 * TSS: CN/PNR编号/出票日期/B
 * eg: TSS:TN/7220000023811/B
 * eg: TSS:TN/722-0000023811/S
 * eg: TSS:CN/EQVI23/20NOV/S
 *
 * <AUTHOR>
 */
@OperateType(action = "TSS")
public class TssHandler implements Handler {

    private static final Pattern TSS_TN_PATTERN = Pattern.compile("TN[/\\s](\\d{13}|\\d{3}-\\d{10})[/\\s]([SB])");

    private static final Pattern TSS_CND_PATTERN = Pattern.compile("CN[/\\s]([A-Z0-9]{6})[/\\s](\\d{2}[A-Z]{3}(\\d{2})?)[/\\s]([SB])");

    private static final Pattern ISSUED_TIME_PATTERN = Pattern.compile("\\d{2}[A-Z]{3}\\d{2}");

    @Resource
    private ITssService iTssService;

    /**
     * @param cmd 指令
     */
    @Override
    public String handle(String cmd) throws UnifiedResultException {
        TssDto tssDto = this.parseCmd(cmd);
        return iTssService.handle(tssDto);
    }

    private TssDto parseCmd(String cmd) throws UnifiedResultException {
        String[] paramSplit = cmd.trim().split(StrUtil.COLON);
        if (cmd.endsWith(StrUtil.COLON) || cmd.endsWith(StrUtil.SLASH)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        if (paramSplit.length != Constant.TWO) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        String param = paramSplit[1];
        TssDto tssDto = new TssDto();
        if (ReUtil.isMatch(TSS_TN_PATTERN, param)) {
            List<String> allGroups = ReUtil.getAllGroups(TSS_TN_PATTERN, param);
            String ticketNo = allGroups.get(1).replace("-", "");
            tssDto.setTicketNo(ticketNo);
            tssDto.setTssType(allGroups.get(2));
        } else if (ReUtil.isMatch(TSS_CND_PATTERN, param)) {
            List<String> allGroups = ReUtil.getAllGroups(TSS_CND_PATTERN, param);
            tssDto.setPnrCode(allGroups.get(1));
            String issuedTime = allGroups.get(2);
            if (!ReUtil.isMatch(ISSUED_TIME_PATTERN, issuedTime)) {
                throw new UnifiedResultException(Constant.DATE_FORMAT);
            }
            tssDto.setIssuedTime(issuedTime);
            tssDto.setTssType(allGroups.get(4));
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        return tssDto;
    }
}

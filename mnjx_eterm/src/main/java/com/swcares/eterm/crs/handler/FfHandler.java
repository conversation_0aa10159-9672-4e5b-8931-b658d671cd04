package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.obj.dto.FfDto;
import com.swcares.eterm.crs.service.IFfService;

import javax.annotation.Resource;
import java.util.List;
import java.util.regex.Pattern;

/**
 * FF指令 查询航班的经停城市，起降时间和机型。
 * 指令格式
 * FF:航班号/日期
 * <AUTHOR>
 */
@OperateType(action = "FF", shorthand = true, predicate = false, template = "/crs/FF.jf")
public class FfHandler implements Handler {

    private static final String REG_FMT_FF = "FF:([a-zA-Z0-9]{5,7})/?(\\d{1,2}[A-Z]{3}(\\d{2})?|[+-.])";

    @Resource
    private IFfService iFfService;

    @Override
    public UnifiedResult handle (String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        // 1、验证指令格式
        if (!ReUtil.isMatch(REG_FMT_FF, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        // 2、获取指令传输对象
        unifiedResult.setArgs(ReUtil.getAllGroups(Pattern.compile(REG_FMT_FF), cmd).toArray());
        // 3、查询业务数据
        List<FfDto> ffDtos = iFfService.handle(unifiedResult);
        // 4、构建模板使用对象
        unifiedResult.setResults(ffDtos.toArray());
        return unifiedResult;
    }

}

package com.swcares;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.config.SerialPortManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 * @date 2023/11/14 18:33
 */
@Slf4j
@SpringBootApplication
public class SerialPortApplication {

    public static void main(String[] args) {
        SpringApplication.run(SerialPortApplication.class, args);
        log.info(StrUtil.format("swagger api 地址:  http://{}:{}/printer/swagger-ui/index.html", "localhost", "18350"));
    }

    @PreDestroy
    public void destroy() {
        SerialPortManager.closeSerialPort();
    }
}

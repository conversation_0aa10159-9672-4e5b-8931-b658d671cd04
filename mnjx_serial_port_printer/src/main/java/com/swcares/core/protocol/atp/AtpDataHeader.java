package com.swcares.core.protocol.atp;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
public class AtpDataHeader {
    /**
     * 数据流标识（不可变）
     */
    public final static String CP = "CP";

    /**
     * 进纸口序号，目前仅使用1（不变）
     */
    public final static String SERIAL_NO = "1";

    /**
     * 控制字，航信一般仅使用C （不变）
     */
    public final static String CONTROL_WORD = "C";

    /**
     * 登机牌数量，航信一般仅使用01（不变）
     */
    public final static String BOARDING_NUM = "01";

    /**
     * 版本标识（不变）
     */
    public final static String VERSION = "01";

    /**
     * PEC的格式码，需要与PEC的格式码对应，即若PEC的头部中是“V”，数据流的头部中也应是“V”
     */
    public final static String FMT_CODE = AtpPecHeader.FMT_CODE;

    /**
     * 返回头部数据
     *
     * @return 返回头部数据
     */
    public static String toStr() {
        return StrUtil.format("{}#{}{}{}#{}{}", CP, SERIAL_NO, CONTROL_WORD, BOARDING_NUM, VERSION, FMT_CODE);
    }
}

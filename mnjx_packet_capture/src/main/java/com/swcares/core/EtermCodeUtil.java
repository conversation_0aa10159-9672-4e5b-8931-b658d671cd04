package com.swcares.core;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.swcares.utils.ArrayUtils;
import com.swcares.utils.PinyinUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class EtermCodeUtil {
    /**
     * 定义中文编码头部字节数组
     */
    private final static byte[] HEADER = {27, 14};
    /**
     * 定义中文编码尾部字节数组
     */
    private final static byte[] TAIL = {27, 15};

    /**
     * Title：chineseEtermEncode <br>
     * description：将中文转换为eterm所认识的编码 <br>
     *
     * @param source source
     * @return 将中文转换为eterm所认识的编码
     * @throws UnsupportedEncodingException 异常
     */
    public static String chineseEtermEncode(String source) throws UnsupportedEncodingException {
        // 找出所有中文
        int length = source.length() * 2;
        if (EtermChineseCodeFourBytes.getETERM_CHINESE_CODE_FOUR_BYTES().stream().anyMatch(e -> source.contains(e.getChinese()))) {
            int count = EtermChineseCodeFourBytes.countChinese(source);
            length = count * 4 + (source.length() - count) * 2;
        }
        byte[] sources = new byte[length];
        char[] chars = source.toCharArray();
        int lastIndex = 0;
        for (int i = 0; i < chars.length; i++) {
            if (PinyinUtil.isChinese(chars[i])) {
                char aChar = chars[i];
                String sourceStr = StrUtil.toString(aChar);
                if (EtermChineseCodeFourBytes.getETERM_CHINESE_CODE_FOUR_BYTES().stream().anyMatch(e -> e.getChinese().equals(sourceStr))) {
                    byte[] bytes = EtermChineseCodeFourBytes.getETERM_CHINESE_CODE_FOUR_BYTES().stream()
                            .filter(e -> e.getChinese().equals(sourceStr))
                            .collect(Collectors.toList())
                            .get(0)
                            .getBytes();
                    for (int j = 0; j < bytes.length; j++) {
                        sources[lastIndex] = bytes[j];
                        lastIndex++;
                    }
                } else {
                    //eterm的客户端是GBK的编码，所以要指定GBK的编码，,指定这个GBK的编码，则一个中文是2个字节长度。
                    byte[] enBytes = String.valueOf(chars[i]).getBytes(CharsetUtil.GBK);
                    for (int j = 0; j < enBytes.length; j++) {
                        // 此次为什么要加128
                        // 所有的中文在获取到字节码的时候目前都是负数，与要eterm客户端要显示的正码刚好相反，所以必须将负数转换为正数（他们之间的差距刚好是128）
                        // 所以要加128
                        sources[lastIndex] = (byte) ((int) enBytes[j] + 128);
                        lastIndex++;
                    }
                }
            }
        }
        //将编码的中文做业务编码:添加头部与尾部
        byte[] all = ArrayUtil.addAll(HEADER, sources, TAIL);
        return new String(all, CharsetUtil.GBK);
    }

    /**
     * Title：chineseEtermDecode <br>
     * description：将eterm所认识的编码解码成中文字符串 <br>
     *
     * @param sources 原始码
     */
    public static byte[] chineseEtermDecode(byte[] sources) {
        // 查找是否有4字节的中文
        Map<Integer, byte[]> fourBytesChineseMap = EtermChineseCodeFourBytes.getChineseStartPositionAndChinese(sources);
        if (MapUtil.isNotEmpty(fourBytesChineseMap)) {
            Set<Map.Entry<Integer, byte[]>> entries = fourBytesChineseMap.entrySet();
            int size = entries.size();
            byte[] newSources = new byte[sources.length - (size * 2)];
            int a = 0;
            int b = 0;
            for (int i = 0; i < sources.length; i++) {
                if (fourBytesChineseMap.containsKey(i)) {
                    byte[] gbkBytes = EtermChineseCodeFourBytes.getGbkBytes(fourBytesChineseMap.get(i));
                    newSources[b] = (byte) (gbkBytes[0] + (byte) 128);
                    b++;
                    newSources[b] = (byte) (gbkBytes[1] + (byte) 128);
                    b++;
                    a = 3;
                } else {
                    if (a == 0) {
                        newSources[b] = sources[i];
                        b++;
                    } else {
                        if (a > 0) {
                            a--;
                        }
                    }
                }
            }
            sources = newSources;
        }

        // 找到header开头和tail结尾的中间值的索引
        List<List<Integer>> coordinates = ArrayUtils.findIndexOf(sources, HEADER, TAIL);
        // 对中间值-128的操作
        for (List<Integer> coordinate : coordinates) {
            /*
            中文码纠错，航信的eterm有部分编码不是标准的GBK编码，比如："北"的编码就对不上,那我们就先通过航信的硬编码找到对应的中文。
            对此中文进行GBK编码，组后将对应的编码替换原有的编码。
             */
            List<List<Integer>> etermChineseCodes = CollUtil.split(coordinate, 2);
            for (List<Integer> etermChineseCode : etermChineseCodes) {
                // 中文第一个坐标
                Integer first = etermChineseCode.get(0);
                // 中文第二个坐标
                Integer second = etermChineseCode.get(1);
                // 获取航信的中文
                EtermChineseCode chineseCode = EtermChineseCode.getChinese((int) sources[first], (int) sources[second]);
                if (chineseCode != null) {
                    try {
                        // 中文进行GBK编码
                        byte[] chineseBytes = chineseCode.getChinese().getBytes(CharsetUtil.GBK);
                        // 用GBK的编码纠正航信编码,为什么要+128,主要考虑的是为了后面统一处理
                        sources[first] = (byte) (chineseBytes[0] + (byte) 128);
                        sources[second] = (byte) (chineseBytes[1] + (byte) 128);
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }
                }
            }
            // 对所有码做中文转换
            for (Integer index : coordinate) {
                sources[index] -= (byte) 128;
            }
        }
        return sources;
    }

    /**
     * 这个方法存在问题，后面优化
     * 将中文转换为eterm所认识的编码
     *
     * @param message 要编码的消息
     * @return 将中文转换为eterm所认识的编码
     * @throws UnsupportedEncodingException Exception
     */
    public static String encodeChinese(String message) throws UnsupportedEncodingException {
        message = message + StrUtil.SPACE + StrUtil.SPACE;
        List<String> chineseChips = new ArrayList<>();
        // 找出所有中文字段
        String chineseChip = StrUtil.EMPTY;
        char[] words = message.toCharArray();
        for (char word : words) {
            // 检测到.n这个字为中文
            if (PinyinUtil.isChinese(word)) {
                chineseChip = chineseChip + word;
            } else {
                if (!StrUtil.EMPTY.equals(chineseChip)) {
                    chineseChips.add(chineseChip);
                    chineseChip = StrUtil.EMPTY;
                }
            }
        }
        // 尝试排序(按照中文的长度排序)
        chineseChips.sort((o1, o2) -> Integer.compare(o2.getBytes().length, o1.getBytes().length));
        // 替换中文字段
        for (String chip : chineseChips) {
            String converted = EtermCodeUtil.chineseEtermEncode(chip);
            try {
                int numberOfStr = numberOfStr(chip, converted);
                if (numberOfStr > 1) {
                    message = message.replaceFirst(chip, converted);
                } else {
                    message = message.replace(chip, converted);
                }
            } catch (Exception iae) {
                iae.printStackTrace();
                message = message.replace(chip, converted);
            }
            if (message.endsWith(" ")) {
                message = message.substring(0, message.length() - 1);
            }
        }
        return message;
    }

    /**
     * 字符串的出现次数
     *
     * @param str 统计要出现次数的字符串
     * @param con 待分解的字符串
     * @return 次数
     */
    private static int numberOfStr(String str, String con) {
        str = StrUtil.format(" {} ", str);
        int len = 0;
        if (str.contains(con)) {
            len = str.split(con).length - 1;
        }
        return len;
    }

    /**
     * 处理中文的 拼音、开始分隔符、结束分隔符
     *
     * @param actual 字节码
     * @return 处理的字符串
     */
    public static String removePinYins(byte[] actual) {
        // 所有的中文
        List<String> chineses = retrieveAllChineses(actual);
        // 所有中文构建的待替换对象
        Map<String, String> etermChineseFormats = constructEtermChineseFormats(chineses);
        String encodeEtermChinese = StrUtil.EMPTY;
        try {
            encodeEtermChinese = new String(actual, CharsetUtil.GBK);
            Set<String> keys = etermChineseFormats.keySet();
            for (String key : keys) {
                encodeEtermChinese = StrUtil.replace(encodeEtermChinese, key, etermChineseFormats.get(key));
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encodeEtermChinese.trim();
    }

    /**
     * 构建所有需要替换的格式数据
     *
     * @param chineses 所有中文
     * @return 所有需要替换的格式数据
     */
    private static Map<String, String> constructEtermChineseFormats(List<String> chineses) {
        // 将相同的中文串过滤掉
        List<String> distinctChineses = chineses.stream().distinct().collect(Collectors.toList());
        // 所有需要替换的格式数据
        Map<String, String> etermChineseFormats = new LinkedHashMap<>(2048);
        // 循环所有中文
        for (String distinctChinese : distinctChineses) {
            // 获得所有的拼音
            List<String> allPinYins = PinyinUtils.getAllPinYins(distinctChinese);
            // 航信有些拼音是错误的，所以我们要兼容它的错误
            EtermChinesePinYin etermChinesePinYin = EtermChinesePinYin.getPinYin(distinctChinese);
            // 把航信的拼音也兼容了
            if (ObjectUtil.isNotEmpty(etermChinesePinYin)) {
                allPinYins.add(etermChinesePinYin.getPinYin());
            }
            if (CollUtil.isNotEmpty(allPinYins)) {
                for (String allPinYin : allPinYins) {
                    //  ü 需要特殊处理为U不然认不到
                    allPinYin = allPinYin.contains(StrUtil.COLON) ? StrUtil.replace(allPinYin, StrUtil.COLON, StrUtil.EMPTY) : allPinYin;
                    // eterm中的格式
                    String etermChineseFormat = StrUtil.format("{}{}{}{}", allPinYin, "\u001B\u000E", distinctChinese, "\u001B\u000F");
                    etermChineseFormats.put(etermChineseFormat, distinctChinese);
                }
            }
        }
        return etermChineseFormats;
    }

    /**
     * 找到所有的中文
     *
     * @param actual 输入的字节码
     * @return 所有的中文
     */
    private static List<String> retrieveAllChineses(byte[] actual) {
        // 获取中文所在的坐标
        List<List<Integer>> indexOfs = ArrayUtils.findIndexOf(actual, HEADER, TAIL);
        // 找出所有的中文
        List<String> chineses = new ArrayList<>(indexOfs.size());
        // 循环找到所有的中文
        for (List<Integer> indexOf : indexOfs) {
            int size = indexOf.size();
            byte[] newChineseByte = new byte[size];
            System.arraycopy(actual, indexOf.get(0), newChineseByte, 0, size);
            try {
                // 中文使用GBK编码
                chineses.add(new String(newChineseByte, CharsetUtil.GBK));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        return chineses;
    }
}

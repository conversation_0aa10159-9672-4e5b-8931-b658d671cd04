<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.swcares</groupId>
    <artifactId>STS</artifactId>
    <version>4.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <!--核心模块-->
        <module>mnjx_core</module>
        <!--公用业务模块-->
        <module>mnjx_business</module>
        <!--eterm系统-->
        <module>mnjx_eterm</module>
        <!--web系统-->
        <module>mnjx_web</module>
        <!--任务调动中心-->
        <module>mnjx_job</module>
        <!--大屏、航显-->
        <module>mnjx_screen</module>
        <!--数据接口层-->
        <module>mnjx_data</module>
        <!--自动化测试-->
        <module>mnjx_test</module>
        <!--安检-->
        <module>mnjx_security_check</module>
        <!-- 语音广播 -->
        <module>mnjx_voice_broadcast</module>
        <!-- 自助值机系统 -->
        <module>mnjx_self_check_in</module>
        <!-- sgui -->
        <module>sgui</module>
    </modules>

    <!--属性-->
    <properties>
        <!--需要打的TAG名称,TAG命名规则：TAG_${发布版本号}_${需求版本号}_-->
        <TAG_RELEASE_VERSION>TAG_4.0.0_20221101</TAG_RELEASE_VERSION>
        <!--JDK的版本-->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!--nacos能支持的最高版本-->
        <spring-boot.version>2.5.15</spring-boot.version>
        <spring-cloud.version>2020.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <!--Maven 打包时警告-->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--依赖的具体版本-->
        <druid-spring-boot-starter.version>1.2.8</druid-spring-boot-starter.version>
        <mybatis-plus-boot-starter.version>3.4.1</mybatis-plus-boot-starter.version>
        <mybatis-plus-generator.version>3.4.1</mybatis-plus-generator.version>
        <hutool-all.version>5.8.6</hutool-all.version>
        <poi-ooxml.version>4.1.2</poi-ooxml.version>
        <RXTXcomm.version>2.2</RXTXcomm.version>
        <spire.xls.free.version>2.2</spire.xls.free.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <TinyPinyin.version>2.0.3.RELEASE</TinyPinyin.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <fastjson.version>1.2.83</fastjson.version>
        <jackson-databind.version>2.11.2</jackson-databind.version>
        <springfox-boot-starter.version>3.0.0</springfox-boot-starter.version>
        <springfox-swagger2.version>3.0.0</springfox-swagger2.version>
        <commons-codec.version>1.14</commons-codec.version>
        <commons-lang.version>2.6</commons-lang.version>
        <enjoy.version>5.0.0</enjoy.version>
        <j2cache-spring-boot2-starter.version>2.7.6-release</j2cache-spring-boot2-starter.version>
        <j2cache-core.version>2.7.8-release</j2cache-core.version>
        <jjwt.version>0.11.2</jjwt.version>
        <xxl-job.version>2.3.0</xxl-job.version>
        <mybatis-starter.version>2.2.2</mybatis-starter.version>
        <lombok.version>1.18.24</lombok.version>
        <dynamic-datasource-spring-boot-starter.version>3.5.0</dynamic-datasource-spring-boot-starter.version>
        <!--插件版本-->
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-source-plugin.version>3.1.0</maven-source-plugin.version>
        <maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
        <maven-surefire-plugin.version>2.5</maven-surefire-plugin.version>
        <maven-dependency-plugin.version>3.1.2</maven-dependency-plugin.version>
        <maven-jar-plugin.version>2.4</maven-jar-plugin.version>
        <spring-boot-maven-plugin.version>2.7.5</spring-boot-maven-plugin.version>
        <versions-maven-plugin.version>2.7</versions-maven-plugin.version>
        <maven-release-plugin.version>3.0.0-M6</maven-release-plugin.version>
        <annotations.version>3.0.1u2</annotations.version>
        <sonar.projectVersion>${project.version}</sonar.projectVersion>
        <sonar-maven-plugin.version>5.0.0.4389</sonar-maven-plugin.version>
        <sonar.login.value>sqa_17d8ec16b252cb39c834cf56c1ad6a98b87621fa</sonar.login.value>
        <sonar.host.url>https://sonar.sw/</sonar.host.url>
        <sonar.token>${sonar.login.value}</sonar.token>
        <sonar.pdf.username>sonar_admin</sonar.pdf.username>
        <sonar.pdf.password>admin@sonar</sonar.pdf.password>
        <sonar.projectKey>2025-IPRD-I-0005</sonar.projectKey>
        <sonar.projectName>模拟教学V5.0</sonar.projectName>
    </properties>

    <!--
    最重要的是connection和developerConnection这两个属性。
    分别代表远程git仓库的地址。前者要求有读权限，后者要求有写权限。
    cm地址的配置有一定的格式。必须是：scm：实现：地址。
    -->
    <scm>
        <url>http://gitlab.sw/cave/sts/code/back-end/STS.git</url>
        <connection>scm:git:http://gitlab.sw/cave/sts/code/back-end/STS.git</connection>
        <developerConnection>scm:git:http://gitlab.sw/cave/sts/code/back-end/STS.git</developerConnection>
        <tag>${TAG_RELEASE_VERSION}</tag>
    </scm>

    <!--启用springboot的开发者工具集，热部署-->
    <!-- <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <version>${spring-boot.version}</version>
            <optional>true</optional>
        </dependency>
    </dependencies> -->

    <!--依赖、版本管理-->
    <dependencyManagement>
        <dependencies>
            <!--springboot-->
            <!--目前nacos能支持的最高版本-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--springcloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--springcloud alibaba-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--=========================下面是具体的jar依赖管理==========================-->
            <!--这里面因为默认使用的是HikariCP数据源,目前也推荐使用这个，相比于druid有更高的性能-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-datasource-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus-generator.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
            </dependency>

            <dependency>
                <groupId>org.rxtx</groupId>
                <artifactId>RXTXcomm</artifactId>
                <version>${RXTXcomm.version}</version>
            </dependency>

            <dependency>
                <groupId>com.spire</groupId>
                <artifactId>spire.xls.free</artifactId>
                <version>${spire.xls.free.version}</version>
            </dependency>

            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.biezhi</groupId>
                <artifactId>TinyPinyin</artifactId>
                <version>${TinyPinyin.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!--json数据处理-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>

            <!-- 排除依赖的swagger-ui，使用com.swcares.mnjx下的swagger-ui，处理中危漏洞删除了js.map文件 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.springfox</groupId>
                        <artifactId>springfox-swagger-ui</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--swagger 配置-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${springfox-swagger2.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jfinal</groupId>
                <artifactId>enjoy</artifactId>
                <version>${enjoy.version}</version>
            </dependency>

            <!-- j2cache -->
            <dependency>
                <groupId>net.oschina.j2cache</groupId>
                <artifactId>j2cache-spring-boot2-starter</artifactId>
                <version>${j2cache-spring-boot2-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>net.oschina.j2cache</groupId>
                <artifactId>j2cache-core</artifactId>
                <version>${j2cache-core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-simple</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>annotations</artifactId>
                <version>${annotations.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.swcares</groupId>
                <artifactId>mnjx_core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.swcares</groupId>
                <artifactId>mnjx_data</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.swcares</groupId>
                <artifactId>mnjx_business</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.swcares</groupId>
                <artifactId>mnjx_eterm</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <!--本地数据库的开发环境-->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <DATABASE_DRIVER_CLASS_NAME>com.mysql.cj.jdbc.Driver</DATABASE_DRIVER_CLASS_NAME>
                <DATABASE_IP>**************</DATABASE_IP>
                <DATABASE_PORT>3309</DATABASE_PORT>
                <DATABASE_NAME_MNJX>mnjx</DATABASE_NAME_MNJX>
                <DATABASE_NAME_JOB>xxl_job</DATABASE_NAME_JOB>
                <DATABASE_USER>root</DATABASE_USER>
                <MYSQL_ROOT_PASSWORD>3w@4C2yx2</MYSQL_ROOT_PASSWORD>
                <JOB_IP>127.0.0.1</JOB_IP>
                <JOB_PORT>8380</JOB_PORT>
                <JOB_ADMIN>mnjx_job</JOB_ADMIN>
                <!--运行模式取值：exam、teach-->
                <RUNNING_MODE>teach</RUNNING_MODE>
                <ALLOW_ORIGIN>http://127.0.0.1</ALLOW_ORIGIN>
                <MINIO_IP>*************</MINIO_IP>
                <MINIO_PORT>9000</MINIO_PORT>
                <REDIS_HOST>*************</REDIS_HOST>
                <REDIS_PORT>30038</REDIS_PORT>
                <REDIS_PASSWORD>mnjx@m27faA</REDIS_PASSWORD>
                <REDIS_DATABASE>0</REDIS_DATABASE>
                <SGUI_ALLOW_ORIGIN>http://**************</SGUI_ALLOW_ORIGIN>
            </properties>
        </profile>
        <!--测试库的连接-->
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <DATABASE_DRIVER_CLASS_NAME>com.mysql.cj.jdbc.Driver</DATABASE_DRIVER_CLASS_NAME>
                <DATABASE_IP>**************</DATABASE_IP>
                <DATABASE_PORT>3309</DATABASE_PORT>
                <DATABASE_NAME_MNJX>mnjx</DATABASE_NAME_MNJX>
                <DATABASE_NAME_JOB>xxl_job</DATABASE_NAME_JOB>
                <DATABASE_USER>root</DATABASE_USER>
                <MYSQL_ROOT_PASSWORD>3w@4C2yx2</MYSQL_ROOT_PASSWORD>
                <JOB_IP>127.0.0.1</JOB_IP>
                <JOB_PORT>8380</JOB_PORT>
                <JOB_ADMIN>mnjx_job</JOB_ADMIN>
                <RUNNING_MODE>teach</RUNNING_MODE>
                <ALLOW_ORIGIN>http://*************:30080</ALLOW_ORIGIN>
                <MINIO_IP>*************</MINIO_IP>
                <MINIO_PORT>9000</MINIO_PORT>
                <REDIS_HOST>*************</REDIS_HOST>
                <REDIS_PORT>30038</REDIS_PORT>
                <REDIS_PASSWORD>mnjx@m27faA</REDIS_PASSWORD>
                <REDIS_DATABASE>0</REDIS_DATABASE>
                <SGUI_ALLOW_ORIGIN>http://*************:31655</SGUI_ALLOW_ORIGIN>
            </properties>
        </profile>
    </profiles>

    <!-- 引入插件 -->
    <build>
        <finalName>${project.artifactId}</finalName>

        <plugins>
            <!-- Compiler 插件, 设定JDK版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <!--源码打包插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>

            <!--测试插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>

            <!-- Maven 版本控制插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!-- Compiler 插件, 设定JDK版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <showWarnings>true</showWarnings>
                        <!-- 输出有关编译器正在执行的操作的消息 -->
                        <!--
                        <compilerArguments>
                            <verbose/>
                        </compilerArguments>
                        -->
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${maven.compiler.encoding}</encoding>
                    </configuration>
                </plugin>

                <!-- 源代码打包插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <executions>
                        <execution>
                            <!--生命周期的阶段-->
                            <phase>compile</phase>
                            <!--插件的目标-->
                            <goals>
                                <goal>jar</goal>
                                <goal>test-jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                    <!--
                    1、spring默认分割符即${*}
                    2、springboot默认分隔符@*@
                    -->
                    <configuration>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <propertiesEncoding>${project.build.sourceEncoding}</propertiesEncoding>
                        <!--这个是默认值-->
                        <useDefaultDelimiters>true</useDefaultDelimiters>
                    </configuration>
                </plugin>

                <!--单元测试-->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>

                <!-- 将依赖包放到dependency文件夹中,只有当这个jar包是服务的时候才引入此插件-->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven-dependency-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>copy</id>
                            <phase>compile</phase>
                            <goals>
                                <goal>copy-dependencies</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>

                <!--
                    Class-Path 和 Main-Class路径为maven-jar-plugin配置。该plugin主要作用就是配置manifest.mf文件。
                    用来获取main方法类路径和第三方jar路径。至于maven打jar包，不配置这个插件也可以。
                -->
                <!--默认的打包方式都是瘦jar，并且是不包含依赖的打包方式-->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                    <configuration>
                        <archive>
                            <!-- 生成的jar中，包含pom.xml和pom.properties这两个文件 -->
                            <addMavenDescriptor>true</addMavenDescriptor>
                        </archive>
                    </configuration>
                </plugin>

                <!--
                    该插件的作用是对普通的jar包做repackage，从而生成一个可执行的jar文件。该插件的goal主要有：repackage和run。
                    repackage目标用于打包生成可执行的jar文件，run用于启动springboot应用程序。
                    打Fat Jar的插件

                    在不使用spring-boot-maven-plugin插件打包springboot的web项目生成的jar包是无法直接运行的。
                    因为直接打包的jar包仅仅是一个普通的jar包，没有打包依赖也没有指定main-class，所以无法直接运行。
                -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- Maven 版本控制插件 -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${versions-maven-plugin.version}</version>
                    <configuration>
                        <!--pom不备份-->
                        <generateBackupPoms>false</generateBackupPoms>
                    </configuration>
                </plugin>

                <!--
                    版本管理插件
                        主要是prepare和perform两个目标。
                            prepare：概括来说，就是perform之前需要执行的准备操作，主要是代码版本修改和提交。
                        主要有以下几个：
                            1）检测是否有未提交的代码。
                            2）检测是否有snapshot依赖。
                            3）修改工程的版本号为release版本（提示输入）。
                            4）为当前代码打上一个git的tag（提示输入），提交代码至远程仓库。
                            5）修改pom为下一个snapshot版本（输入提示），提交至远程仓库。
                            注意，这里一共提交了两次代码，第一次是release版本，第二次是snapshot版本。
                        perform：
                            发包。具体来说，perform会从远程代码库拉下刚才第一次提交的release版本的代码。然后内部再起一个maven build过程，执行一次deploy，将release包发至nexus仓库。
                -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>${maven-release-plugin.version}</version>
                    <configuration>
                        <!--是否自动为子模块分配父版本。如果设置为false，将提示用户输入每个子模块的版本。-->
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>${sonar-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- 组织机构信息 -->
    <organization>
        <name>成都民航西南凯亚有限责任公司</name>
        <url>https://xxx.xxx.xxx/</url>
    </organization>

    <!-- 模块开发人员 -->
    <developers>
        <developer>
            <name>姚文兵</name>
        </developer>
        <developer>
            <name>赵侃</name>
        </developer>
        <developer>
            <name>张鑫</name>
        </developer>
        <developer>
            <name>陈春蓉</name>
        </developer>
        <developer>
            <name>姚丹</name>
        </developer>
    </developers>

    <!-- 定义发布的路径 -->
    <distributionManagement>
        <repository>
            <id>StsNexusReleases</id>
            <name>releases</name>
            <url>http://maven.sw/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>StsNexusSnapshots</id>
            <name>snapshots</name>
            <url>http://maven.sw/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
